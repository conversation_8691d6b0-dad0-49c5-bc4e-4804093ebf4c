spring:

  datasource:
    url: ${SPOT_DATASOURCE_URL:*****************************************************************}
    username: ${SPOT_DATASOURCE_USERNAME:spot_user}
    password: ${SPOT_DATASOURCE_PASSWORD:d2mwpsfU4fhSDvysiC60kwIXL3Zjojf6I9DlZJE1FUxYdGMgFPXOYZl0p6LidZnA}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    show-sql: ${SHOW_SQL:true}
    database: postgresql

  data:

    mongodb:
      uri: ${SPRING_MONGODB_URI:*******************************************}
      database: ${SPOT_MONGODB_DATABASE:spot}

    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:30679}
      database: ${REDIS_DB:0}

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: "${server.servlet.context-path}/actuator/health"
        health-check-interval: 60s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.value}
    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent

cex-services:
  market-service: "market"
  future-service: "future"

#S3 Config
s3:
  access-key-id: ${S3_ACCESS_KEY_ID:}
  access-key-secret: ${S3_ACCESS_KEY_SECRET:}
  bucket-name: ${S3_BUCKET_NAME:}
  regions-name: ${S3_REGIONS_NAME:}

#Minio Config
bitcello:
  minio:
    protocol: http
    host: ************
    port: 30000
    access-key: VTp9kKg0QBdn80dCHn0V
    secret-key: Fda7Rfa1Roafn0e9SND57T3T219MXfkTlVQak6fJ
    bucket-name: bitcello-file


##Minio Config
#bitcello:
#  minio:
#    protocol: ${MINIO_PROTOCOL:http}
#    host: ${MINIO_HOST:************}
#    port: ${MINIO_PORT:9000}
#    access-key: ${MINO_ACCESS_KEY:VTp9kKgOQBdn8OdCHn0V}
#    secret-key: ${MINIO_SECRET-KEY:Fda7Rfd1Roafn0e9SND57T2T16MXfkIlVQaK6fJe}
#    bucket-name: ${MINIO_BUCKET_NAME:icetea-software-file}

##Minio Config
#minio:
#  protocol: ${MINIO_PROTOCOL:http}
#  host: ${MINIO_HOST:************}
#  port: ${MINIO_PORT:9000}
#  access-key: ${MINO_ACCESS_KEY:VTp9kKgOQBdn8OdCHn0V}
#  secret-key: ${MINIO_SECRET-KEY:Fda7Rfd1Roafn0e9SND57T2T16MXfkIlVQaK6fJe}
#  bucket-name: ${MINIO_BUCKET_NAME:icetea-software-file}
