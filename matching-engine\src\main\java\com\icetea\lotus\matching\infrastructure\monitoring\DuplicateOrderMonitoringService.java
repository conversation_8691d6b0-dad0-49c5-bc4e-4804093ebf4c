package com.icetea.lotus.matching.infrastructure.monitoring;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Service để monitor và track duplicate order detection
 * Cung cấp metrics và alerting cho duplicate order attempts
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DuplicateOrderMonitoringService {

    private final RedisTemplate<String, String> redisTemplate;
    
    // In-memory counters for performance
    private final AtomicLong duplicateOrdersDetected = new AtomicLong(0);
    private final AtomicLong duplicateOrdersSkipped = new AtomicLong(0);
    private final AtomicLong kafkaFailuresDetected = new AtomicLong(0);
    private final AtomicLong compensationRetriesTriggered = new AtomicLong(0);
    
    private static final String DUPLICATE_COUNTER_KEY = "duplicate_orders:count:";
    private static final String KAFKA_FAILURE_COUNTER_KEY = "kafka_failures:count:";
    private static final String DAILY_STATS_KEY = "duplicate_stats:daily:";
    
    /**
     * Record duplicate order detection
     */
    public void recordDuplicateOrderDetected(String orderId, String symbol, String reason) {
        duplicateOrdersDetected.incrementAndGet();
        
        try {
            String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String dailyKey = DAILY_STATS_KEY + today;
            
            // Increment daily counter
            redisTemplate.opsForHash().increment(dailyKey, "duplicates_detected", 1);
            redisTemplate.opsForHash().increment(dailyKey, "symbol:" + symbol, 1);
            
            // Store duplicate details for analysis
            String duplicateKey = DUPLICATE_COUNTER_KEY + orderId;
            String duplicateInfo = String.format("{\"orderId\":\"%s\",\"symbol\":\"%s\",\"reason\":\"%s\",\"timestamp\":\"%s\"}", 
                orderId, symbol, reason, LocalDateTime.now().toString());
            
            redisTemplate.opsForValue().set(duplicateKey, duplicateInfo);
            
            log.info("DUPLICATE_DETECTED: orderId={}, symbol={}, reason={}", orderId, symbol, reason);
            
        } catch (Exception e) {
            log.warn("Failed to record duplicate order detection: {}", e.getMessage());
        }
    }

    /**
     * Record duplicate order skipped
     */
    public void recordDuplicateOrderSkipped(String orderId, String symbol) {
        duplicateOrdersSkipped.incrementAndGet();
        
        try {
            String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String dailyKey = DAILY_STATS_KEY + today;
            
            redisTemplate.opsForHash().increment(dailyKey, "duplicates_skipped", 1);
            
            log.info("DUPLICATE_SKIPPED: orderId={}, symbol={}", orderId, symbol);
            
        } catch (Exception e) {
            log.warn("Failed to record duplicate order skipped: {}", e.getMessage());
        }
    }

    /**
     * Record Kafka failure
     */
    public void recordKafkaFailure(String orderId, String symbol, String errorMessage) {
        kafkaFailuresDetected.incrementAndGet();
        
        try {
            String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String dailyKey = DAILY_STATS_KEY + today;
            
            redisTemplate.opsForHash().increment(dailyKey, "kafka_failures", 1);
            
            // Store failure details
            String failureKey = KAFKA_FAILURE_COUNTER_KEY + orderId;
            String failureInfo = String.format("{\"orderId\":\"%s\",\"symbol\":\"%s\",\"error\":\"%s\",\"timestamp\":\"%s\"}", 
                orderId, symbol, errorMessage, LocalDateTime.now().toString());
            
            redisTemplate.opsForValue().set(failureKey, failureInfo);
            
            log.warn("KAFKA_FAILURE: orderId={}, symbol={}, error={}", orderId, symbol, errorMessage);
            
        } catch (Exception e) {
            log.warn("Failed to record Kafka failure: {}", e.getMessage());
        }
    }

    /**
     * Record compensation retry triggered
     */
    public void recordCompensationRetryTriggered(String orderId, String symbol, int retryAttempt) {
        compensationRetriesTriggered.incrementAndGet();
        
        try {
            String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String dailyKey = DAILY_STATS_KEY + today;
            
            redisTemplate.opsForHash().increment(dailyKey, "compensation_retries", 1);
            
            log.info("COMPENSATION_RETRY: orderId={}, symbol={}, attempt={}", orderId, symbol, retryAttempt);
            
        } catch (Exception e) {
            log.warn("Failed to record compensation retry: {}", e.getMessage());
        }
    }

    /**
     * Get current monitoring statistics
     */
    public MonitoringStats getCurrentStats() {
        try {
            String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String dailyKey = DAILY_STATS_KEY + today;
            
            Long dailyDuplicatesDetected = (Long) redisTemplate.opsForHash().get(dailyKey, "duplicates_detected");
            Long dailyDuplicatesSkipped = (Long) redisTemplate.opsForHash().get(dailyKey, "duplicates_skipped");
            Long dailyKafkaFailures = (Long) redisTemplate.opsForHash().get(dailyKey, "kafka_failures");
            Long dailyCompensationRetries = (Long) redisTemplate.opsForHash().get(dailyKey, "compensation_retries");
            
            return MonitoringStats.builder()
                .totalDuplicatesDetected(duplicateOrdersDetected.get())
                .totalDuplicatesSkipped(duplicateOrdersSkipped.get())
                .totalKafkaFailures(kafkaFailuresDetected.get())
                .totalCompensationRetries(compensationRetriesTriggered.get())
                .dailyDuplicatesDetected(dailyDuplicatesDetected != null ? dailyDuplicatesDetected : 0L)
                .dailyDuplicatesSkipped(dailyDuplicatesSkipped != null ? dailyDuplicatesSkipped : 0L)
                .dailyKafkaFailures(dailyKafkaFailures != null ? dailyKafkaFailures : 0L)
                .dailyCompensationRetries(dailyCompensationRetries != null ? dailyCompensationRetries : 0L)
                .build();
                
        } catch (Exception e) {
            log.error("Failed to get monitoring stats", e);
            return MonitoringStats.builder()
                .totalDuplicatesDetected(duplicateOrdersDetected.get())
                .totalDuplicatesSkipped(duplicateOrdersSkipped.get())
                .totalKafkaFailures(kafkaFailuresDetected.get())
                .totalCompensationRetries(compensationRetriesTriggered.get())
                .build();
        }
    }

    /**
     * Check if duplicate rate is too high and needs alerting
     */
    public boolean isDuplicateRateHigh() {
        try {
            String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String dailyKey = DAILY_STATS_KEY + today;
            
            Long duplicatesDetected = (Long) redisTemplate.opsForHash().get(dailyKey, "duplicates_detected");
            Long totalOrders = duplicatesDetected != null ? duplicatesDetected + duplicateOrdersSkipped.get() : duplicateOrdersSkipped.get();
            
            if (totalOrders > 100) { // Only check if we have significant volume
                double duplicateRate = duplicatesDetected != null ? (double) duplicatesDetected / totalOrders : 0.0;
                return duplicateRate > 0.05; // Alert if duplicate rate > 5%
            }
            
            return false;
            
        } catch (Exception e) {
            log.warn("Failed to check duplicate rate: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Log monitoring summary - called periodically every 5 minutes
     */
    @Scheduled(fixedDelay = 300000) // 5 minutes
    public void logMonitoringSummary() {
        try {
            MonitoringStats stats = getCurrentStats();
            
            log.info("=== DUPLICATE ORDER MONITORING SUMMARY ===");
            log.info("Total Duplicates Detected: {}", stats.getTotalDuplicatesDetected());
            log.info("Total Duplicates Skipped: {}", stats.getTotalDuplicatesSkipped());
            log.info("Total Kafka Failures: {}", stats.getTotalKafkaFailures());
            log.info("Total Compensation Retries: {}", stats.getTotalCompensationRetries());
            log.info("Daily Duplicates Detected: {}", stats.getDailyDuplicatesDetected());
            log.info("Daily Duplicates Skipped: {}", stats.getDailyDuplicatesSkipped());
            log.info("Daily Kafka Failures: {}", stats.getDailyKafkaFailures());
            log.info("Daily Compensation Retries: {}", stats.getDailyCompensationRetries());
            
            if (isDuplicateRateHigh()) {
                log.warn("⚠️  HIGH DUPLICATE RATE DETECTED - Investigation needed!");
            }
            
            log.info("==========================================");
            
        } catch (Exception e) {
            log.error("Failed to log monitoring summary", e);
        }
    }

    /**
     * Monitoring statistics data class
     */
    public static class MonitoringStats {
        private final long totalDuplicatesDetected;
        private final long totalDuplicatesSkipped;
        private final long totalKafkaFailures;
        private final long totalCompensationRetries;
        private final long dailyDuplicatesDetected;
        private final long dailyDuplicatesSkipped;
        private final long dailyKafkaFailures;
        private final long dailyCompensationRetries;
        
        private MonitoringStats(Builder builder) {
            this.totalDuplicatesDetected = builder.totalDuplicatesDetected;
            this.totalDuplicatesSkipped = builder.totalDuplicatesSkipped;
            this.totalKafkaFailures = builder.totalKafkaFailures;
            this.totalCompensationRetries = builder.totalCompensationRetries;
            this.dailyDuplicatesDetected = builder.dailyDuplicatesDetected;
            this.dailyDuplicatesSkipped = builder.dailyDuplicatesSkipped;
            this.dailyKafkaFailures = builder.dailyKafkaFailures;
            this.dailyCompensationRetries = builder.dailyCompensationRetries;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        // Getters
        public long getTotalDuplicatesDetected() { return totalDuplicatesDetected; }
        public long getTotalDuplicatesSkipped() { return totalDuplicatesSkipped; }
        public long getTotalKafkaFailures() { return totalKafkaFailures; }
        public long getTotalCompensationRetries() { return totalCompensationRetries; }
        public long getDailyDuplicatesDetected() { return dailyDuplicatesDetected; }
        public long getDailyDuplicatesSkipped() { return dailyDuplicatesSkipped; }
        public long getDailyKafkaFailures() { return dailyKafkaFailures; }
        public long getDailyCompensationRetries() { return dailyCompensationRetries; }
        
        public static class Builder {
            private long totalDuplicatesDetected;
            private long totalDuplicatesSkipped;
            private long totalKafkaFailures;
            private long totalCompensationRetries;
            private long dailyDuplicatesDetected;
            private long dailyDuplicatesSkipped;
            private long dailyKafkaFailures;
            private long dailyCompensationRetries;
            
            public Builder totalDuplicatesDetected(long totalDuplicatesDetected) {
                this.totalDuplicatesDetected = totalDuplicatesDetected;
                return this;
            }
            
            public Builder totalDuplicatesSkipped(long totalDuplicatesSkipped) {
                this.totalDuplicatesSkipped = totalDuplicatesSkipped;
                return this;
            }
            
            public Builder totalKafkaFailures(long totalKafkaFailures) {
                this.totalKafkaFailures = totalKafkaFailures;
                return this;
            }
            
            public Builder totalCompensationRetries(long totalCompensationRetries) {
                this.totalCompensationRetries = totalCompensationRetries;
                return this;
            }
            
            public Builder dailyDuplicatesDetected(long dailyDuplicatesDetected) {
                this.dailyDuplicatesDetected = dailyDuplicatesDetected;
                return this;
            }
            
            public Builder dailyDuplicatesSkipped(long dailyDuplicatesSkipped) {
                this.dailyDuplicatesSkipped = dailyDuplicatesSkipped;
                return this;
            }
            
            public Builder dailyKafkaFailures(long dailyKafkaFailures) {
                this.dailyKafkaFailures = dailyKafkaFailures;
                return this;
            }
            
            public Builder dailyCompensationRetries(long dailyCompensationRetries) {
                this.dailyCompensationRetries = dailyCompensationRetries;
                return this;
            }
            
            public MonitoringStats build() {
                return new MonitoringStats(this);
            }
        }
    }
}
