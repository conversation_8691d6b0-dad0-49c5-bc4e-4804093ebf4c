package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QWalletFuture is a Querydsl query type for WalletFuture
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QWalletFuture extends EntityPathBase<WalletFuture> {

    private static final long serialVersionUID = -1356022676L;

    public static final QWalletFuture walletFuture = new QWalletFuture("walletFuture");

    public final StringPath address = createString("address");

    public final NumberPath<java.math.BigDecimal> availableBalance = createNumber("availableBalance", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> balance = createNumber("balance", java.math.BigDecimal.class);

    public final StringPath coin = createString("coin");

    public final DateTimePath<java.time.LocalDateTime> createTime = createDateTime("createTime", java.time.LocalDateTime.class);

    public final NumberPath<java.math.BigDecimal> frozenBalance = createNumber("frozenBalance", java.math.BigDecimal.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final BooleanPath isLocked = createBoolean("isLocked");

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<java.math.BigDecimal> realizedPnl = createNumber("realizedPnl", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> totalFee = createNumber("totalFee", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> totalFundingFee = createNumber("totalFundingFee", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> unrealizedPnl = createNumber("unrealizedPnl", java.math.BigDecimal.class);

    public final DateTimePath<java.time.LocalDateTime> updateTime = createDateTime("updateTime", java.time.LocalDateTime.class);

    public final NumberPath<java.math.BigDecimal> usedMargin = createNumber("usedMargin", java.math.BigDecimal.class);

    public final NumberPath<Integer> version = createNumber("version", Integer.class);

    public QWalletFuture(String variable) {
        super(WalletFuture.class, forVariable(variable));
    }

    public QWalletFuture(Path<? extends WalletFuture> path) {
        super(path.getType(), path.getMetadata());
    }

    public QWalletFuture(PathMetadata metadata) {
        super(WalletFuture.class, metadata);
    }

}

