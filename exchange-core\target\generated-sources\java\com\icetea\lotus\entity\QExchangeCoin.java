package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QExchangeCoin is a Querydsl query type for ExchangeCoin
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QExchangeCoin extends EntityPathBase<ExchangeCoin> {

    private static final long serialVersionUID = -134056796L;

    public static final QExchangeCoin exchangeCoin = new QExchangeCoin("exchangeCoin");

    public final NumberPath<Integer> baseCoinScale = createNumber("baseCoinScale", Integer.class);

    public final StringPath baseSymbol = createString("baseSymbol");

    public final StringPath clearTime = createString("clearTime");

    public final NumberPath<Integer> coinScale = createNumber("coinScale", Integer.class);

    public final StringPath coinSymbol = createString("coinSymbol");

    public final NumberPath<Integer> enable = createNumber("enable", Integer.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> enableBuy = createEnum("enableBuy", com.icetea.lotus.constant.BooleanEnum.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> enableMarketBuy = createEnum("enableMarketBuy", com.icetea.lotus.constant.BooleanEnum.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> enableMarketSell = createEnum("enableMarketSell", com.icetea.lotus.constant.BooleanEnum.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> enableSell = createEnum("enableSell", com.icetea.lotus.constant.BooleanEnum.class);

    public final StringPath endTime = createString("endTime");

    public final NumberPath<Integer> exchangeable = createNumber("exchangeable", Integer.class);

    public final NumberPath<Integer> fakeDataStatus = createNumber("fakeDataStatus", Integer.class);

    public final NumberPath<java.math.BigDecimal> fee = createNumber("fee", java.math.BigDecimal.class);

    public final NumberPath<Integer> flag = createNumber("flag", Integer.class);

    public final NumberPath<java.math.BigDecimal> maxBuyPrice = createNumber("maxBuyPrice", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> maxPrice = createNumber("maxPrice", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> maxTotal = createNumber("maxTotal", java.math.BigDecimal.class);

    public final NumberPath<Integer> maxTradingOrder = createNumber("maxTradingOrder", Integer.class);

    public final NumberPath<Integer> maxTradingTime = createNumber("maxTradingTime", Integer.class);

    public final NumberPath<java.math.BigDecimal> maxVolume = createNumber("maxVolume", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> minPrice = createNumber("minPrice", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> minSellPrice = createNumber("minSellPrice", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> minTotal = createNumber("minTotal", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> minTurnover = createNumber("minTurnover", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> minVolume = createNumber("minVolume", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> publishAmount = createNumber("publishAmount", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> publishPrice = createNumber("publishPrice", java.math.BigDecimal.class);

    public final EnumPath<ExchangeCoinPublishType> publishType = createEnum("publishType", ExchangeCoinPublishType.class);

    public final NumberPath<Integer> robotType = createNumber("robotType", Integer.class);

    public final NumberPath<Integer> sort = createNumber("sort", Integer.class);

    public final StringPath startTime = createString("startTime");

    public final StringPath symbol = createString("symbol");

    public final NumberPath<Integer> visible = createNumber("visible", Integer.class);

    public final NumberPath<Integer> zone = createNumber("zone", Integer.class);

    public QExchangeCoin(String variable) {
        super(ExchangeCoin.class, forVariable(variable));
    }

    public QExchangeCoin(Path<? extends ExchangeCoin> path) {
        super(path.getType(), path.getMetadata());
    }

    public QExchangeCoin(PathMetadata metadata) {
        super(ExchangeCoin.class, metadata);
    }

}

