package com.icetea.lotus.matching.infrastructure.idempotency;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Service để xử lý idempotency cho order processing
 * Ngăn chặn duplicate order processing
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderIdempotencyService {

    private final RedisTemplate<String, String> redisTemplate;
    
    // Fallback local cache nếu Redis không available
    private final ConcurrentMap<String, Long> localProcessingCache = new ConcurrentHashMap<>();
    
    private static final String PROCESSING_LOCK_PREFIX = "order_processing:";
    private static final String PROCESSED_ORDER_PREFIX = "order_processed:";
    private static final Duration PROCESSING_LOCK_TTL = Duration.ofMinutes(5);
    private static final Duration PROCESSED_ORDER_TTL = Duration.ofHours(24);

    /**
     * <PERSON><PERSON>m tra và acquire processing lock cho order
     * 
     * @param orderId ID của order
     * @return true nếu có thể process order, false nếu đang được process hoặc đã processed
     */
    public boolean tryAcquireProcessingLock(String orderId) {
        if (orderId == null || orderId.trim().isEmpty()) {
            log.warn("Invalid orderId for processing lock: {}", orderId);
            return false;
        }

        try {
            // Check if order was already processed
            if (isOrderAlreadyProcessed(orderId)) {
                log.info("Order {} was already processed, skipping", orderId);
                return false;
            }

            // Try to acquire processing lock
            String lockKey = PROCESSING_LOCK_PREFIX + orderId;
            Boolean lockAcquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, "processing", PROCESSING_LOCK_TTL);

            if (Boolean.TRUE.equals(lockAcquired)) {
                log.debug("Successfully acquired processing lock for order: {}", orderId);
                return true;
            } else {
                log.info("Order {} is already being processed by another instance, skipping", orderId);
                return false;
            }

        } catch (Exception e) {
            log.warn("Redis error when acquiring processing lock for order {}, falling back to local cache", orderId, e);
            return tryAcquireLocalProcessingLock(orderId);
        }
    }

    /**
     * Release processing lock cho order
     * 
     * @param orderId ID của order
     */
    public void releaseProcessingLock(String orderId) {
        if (orderId == null || orderId.trim().isEmpty()) {
            return;
        }

        try {
            String lockKey = PROCESSING_LOCK_PREFIX + orderId;
            redisTemplate.delete(lockKey);
            log.debug("Released processing lock for order: {}", orderId);

        } catch (Exception e) {
            log.warn("Redis error when releasing processing lock for order {}", orderId, e);
            releaseLocalProcessingLock(orderId);
        }
    }

    /**
     * Đánh dấu order đã được processed thành công
     * 
     * @param orderId ID của order
     */
    public void markOrderAsProcessed(String orderId) {
        if (orderId == null || orderId.trim().isEmpty()) {
            return;
        }

        try {
            String processedKey = PROCESSED_ORDER_PREFIX + orderId;
            redisTemplate.opsForValue().set(processedKey, "processed", PROCESSED_ORDER_TTL);
            log.debug("Marked order {} as processed", orderId);

        } catch (Exception e) {
            log.warn("Redis error when marking order {} as processed", orderId, e);
            markOrderAsProcessedLocally(orderId);
        }
    }

    /**
     * Kiểm tra xem order đã được processed chưa
     * 
     * @param orderId ID của order
     * @return true nếu order đã được processed
     */
    public boolean isOrderAlreadyProcessed(String orderId) {
        if (orderId == null || orderId.trim().isEmpty()) {
            return false;
        }

        try {
            String processedKey = PROCESSED_ORDER_PREFIX + orderId;
            Boolean exists = redisTemplate.hasKey(processedKey);
            return Boolean.TRUE.equals(exists);

        } catch (Exception e) {
            log.warn("Redis error when checking if order {} was processed, falling back to local cache", orderId, e);
            return isOrderProcessedLocally(orderId);
        }
    }

    /**
     * Fallback local processing lock
     */
    private boolean tryAcquireLocalProcessingLock(String orderId) {
        long currentTime = System.currentTimeMillis();
        Long existingTime = localProcessingCache.putIfAbsent(orderId, currentTime);
        
        if (existingTime == null) {
            log.debug("Acquired local processing lock for order: {}", orderId);
            return true;
        }
        
        // Check if existing lock is expired (5 minutes)
        if (currentTime - existingTime > PROCESSING_LOCK_TTL.toMillis()) {
            localProcessingCache.put(orderId, currentTime);
            log.debug("Acquired expired local processing lock for order: {}", orderId);
            return true;
        }
        
        log.info("Order {} is already being processed locally, skipping", orderId);
        return false;
    }

    /**
     * Release local processing lock
     */
    private void releaseLocalProcessingLock(String orderId) {
        localProcessingCache.remove(orderId);
        log.debug("Released local processing lock for order: {}", orderId);
    }

    /**
     * Mark order as processed locally
     */
    private void markOrderAsProcessedLocally(String orderId) {
        // For local cache, we use negative timestamp to indicate processed
        localProcessingCache.put(orderId, -System.currentTimeMillis());
        log.debug("Marked order {} as processed locally", orderId);
    }

    /**
     * Check if order was processed locally
     */
    private boolean isOrderProcessedLocally(String orderId) {
        Long timestamp = localProcessingCache.get(orderId);
        return timestamp != null && timestamp < 0;
    }

    /**
     * Cleanup expired entries from local cache
     * Should be called periodically
     */
    public void cleanupExpiredLocalEntries() {
        long currentTime = System.currentTimeMillis();
        long expirationTime = PROCESSING_LOCK_TTL.toMillis();
        
        localProcessingCache.entrySet().removeIf(entry -> {
            long timestamp = Math.abs(entry.getValue());
            return currentTime - timestamp > expirationTime;
        });
        
        log.debug("Cleaned up expired local cache entries");
    }
}
