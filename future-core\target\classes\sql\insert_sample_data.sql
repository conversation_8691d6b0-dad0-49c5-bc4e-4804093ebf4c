-- <PERSON><PERSON><PERSON><PERSON> dữ liệu mẫu cho bảng contract_coin
INSERT INTO contract_coin (symbol, name, base_symbol, quote_symbol, multiplier, min_volume, max_volume, price_precision, volume_precision, maintenance_margin_rate, initial_margin_rate, leverage_max, leverage_min, sort, fee, funding_rate_coefficient, max_funding_rate, min_funding_rate, funding_interval, margin_mode, expiry_date, enable, create_time, update_time)
VALUES
('BTCUSDT', 'Bitcoin Perpetual', 'BTC', 'USDT', 1.00000000, 0.00100000, 100.00000000, 0.10000000, 0.00100000, 0.00500000, 0.01000000, 125, 1, 1, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', NULL, 1, NOW(), NOW()),
('ETHUSDT', 'Ethereum Perpetual', 'ETH', 'USDT', 1.00000000, 0.01000000, 1000.00000000, 0.01000000, 0.01000000, 0.00500000, 0.01000000, 100, 1, 2, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', NULL, 1, NOW(), NOW()),
('SOLUSDT', 'Solana Perpetual', 'SOL', 'USDT', 1.00000000, 0.10000000, 10000.00000000, 0.00100000, 0.10000000, 0.00500000, 0.01000000, 75, 1, 3, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', NULL, 1, NOW(), NOW()),
('BNBUSDT', 'Binance Coin Perpetual', 'BNB', 'USDT', 1.00000000, 0.01000000, 1000.00000000, 0.01000000, 0.01000000, 0.00500000, 0.01000000, 75, 1, 4, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', NULL, 1, NOW(), NOW()),
('ADAUSDT', 'Cardano Perpetual', 'ADA', 'USDT', 1.00000000, 1.00000000, 100000.00000000, 0.00010000, 1.00000000, 0.00500000, 0.01000000, 75, 1, 5, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', NULL, 1, NOW(), NOW()),
('BTCUSDT-240628', 'Bitcoin Quarterly 2024-06-28', 'BTC', 'USDT', 1.00000000, 0.00100000, 100.00000000, 0.10000000, 0.00100000, 0.00500000, 0.01000000, 125, 1, 6, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', '2024-06-28 16:00:00', 1, NOW(), NOW()),
('ETHUSDT-240628', 'Ethereum Quarterly 2024-06-28', 'ETH', 'USDT', 1.00000000, 0.01000000, 1000.00000000, 0.01000000, 0.01000000, 0.00500000, 0.01000000, 100, 1, 7, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', '2024-06-28 16:00:00', 1, NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_price_configuration
INSERT INTO contract_price_configuration (symbol, index_price_method, mark_price_method, custom_index_price_formula, custom_mark_price_formula, parameters, create_time, update_time)
VALUES
('BTCUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('ETHUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('SOLUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('BNBUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('ADAUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('BTCUSDT-240628', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('ETHUSDT-240628', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_insurance_fund
INSERT INTO contract_insurance_fund (contract_id, symbol, balance, frozen_balance, available_balance, create_time, update_time)
VALUES
(1, 'BTCUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(2, 'ETHUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(3, 'SOLUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(4, 'BNBUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(5, 'ADAUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(6, 'BTCUSDT-240628', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(7, 'ETHUSDT-240628', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_funding_rate
INSERT INTO contract_funding_rate (contract_id, symbol, rate, mark_price, index_price, time, next_time)
VALUES
(1, 'BTCUSDT', 0.00010000, 50000.00000000, 50010.00000000, NOW(), NOW() + INTERVAL '8 HOUR'),
(2, 'ETHUSDT', 0.00015000, 3000.00000000, 3005.00000000, NOW(), NOW() + INTERVAL '8 HOUR'),
(3, 'SOLUSDT', 0.00020000, 100.00000000, 100.50000000, NOW(), NOW() + INTERVAL '8 HOUR'),
(4, 'BNBUSDT', 0.00012000, 400.00000000, 401.00000000, NOW(), NOW() + INTERVAL '8 HOUR'),
(5, 'ADAUSDT', 0.00018000, 1.20000000, 1.21000000, NOW(), NOW() + INTERVAL '8 HOUR'),
(6, 'BTCUSDT-240628', 0.00025000, 50500.00000000, 50510.00000000, NOW(), NOW() + INTERVAL '8 HOUR'),
(7, 'ETHUSDT-240628', 0.00030000, 3050.00000000, 3055.00000000, NOW(), NOW() + INTERVAL '8 HOUR');

-- Thêm dữ liệu mẫu cho bảng contract_user_position_mode
INSERT INTO contract_user_position_mode (member_id, position_mode, create_time, update_time)
VALUES
(1, 'HEDGE', NOW(), NOW()),
(2, 'ONE_WAY', NOW(), NOW()),
(3, 'HEDGE', NOW(), NOW()),
(4, 'ONE_WAY', NOW(), NOW()),
(5, 'HEDGE', NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_user_leverage_setting
INSERT INTO contract_user_leverage_setting (member_id, symbol, leverage, create_time, update_time)
VALUES
(1, 'BTCUSDT', 10.00000000, NOW(), NOW()),
(1, 'ETHUSDT', 20.00000000, NOW(), NOW()),
(2, 'BTCUSDT', 5.00000000, NOW(), NOW()),
(2, 'ETHUSDT', 10.00000000, NOW(), NOW()),
(3, 'BTCUSDT', 20.00000000, NOW(), NOW()),
(3, 'SOLUSDT', 15.00000000, NOW(), NOW()),
(4, 'BNBUSDT', 10.00000000, NOW(), NOW()),
(5, 'ADAUSDT', 25.00000000, NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_wallet
INSERT INTO contract_wallet (member_id, coin, balance, frozen_balance, available_balance, address, unrealized_pnl, realized_pnl, used_margin, total_fee, total_funding_fee, is_locked, create_time, update_time, version)
VALUES
(1, 'USDT', 100000.00000000, 0.00000000, 100000.00000000, 'TXyz123456789', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0),
(2, 'USDT', 50000.00000000, 0.00000000, 50000.00000000, 'TXyz987654321', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0),
(3, 'USDT', 75000.00000000, 0.00000000, 75000.00000000, 'TXyz123789456', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0),
(4, 'USDT', 25000.00000000, 0.00000000, 25000.00000000, 'TXyz456123789', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0),
(5, 'USDT', 10000.00000000, 0.00000000, 10000.00000000, 'TXyz789456123', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0);

-- Thêm dữ liệu mẫu cho bảng contract_order
INSERT INTO contract_order (order_id, member_id, contract_id, symbol, coin_symbol, base_symbol, direction, type, price, trigger_price, volume, filled_volume, turnover, fee, status, create_time, canceled_time, execute_time, expire_time, liquidation, adl, implied, source_order_id, oco_id, oco_order_no, leverage, reduce_only, callback_rate, time_in_force, trigger_type, slippage)
VALUES
('ORD123456789', 1, 1, 'BTCUSDT', 'BTC', 'USDT', 'BUY', 'LIMIT', 50000.00000000, NULL, 1.00000000, 1.00000000, 50000.00000000, 20.00000000, 'FILLED', NOW() - INTERVAL '1 HOUR', NULL, NOW() - INTERVAL '55 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 10.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000),
('ORD987654321', 2, 1, 'BTCUSDT', 'BTC', 'USDT', 'SELL', 'LIMIT', 50000.00000000, NULL, 1.00000000, 1.00000000, 50000.00000000, 20.00000000, 'FILLED', NOW() - INTERVAL '1 HOUR', NULL, NOW() - INTERVAL '55 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 5.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000),
('ORD123789456', 1, 2, 'ETHUSDT', 'ETH', 'USDT', 'BUY', 'LIMIT', 3000.00000000, NULL, 5.00000000, 5.00000000, 15000.00000000, 6.00000000, 'FILLED', NOW() - INTERVAL '2 HOUR', NULL, NOW() - INTERVAL '1 HOUR 55 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 20.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000),
('ORD456123789', 3, 2, 'ETHUSDT', 'ETH', 'USDT', 'SELL', 'LIMIT', 3000.00000000, NULL, 5.00000000, 5.00000000, 15000.00000000, 6.00000000, 'FILLED', NOW() - INTERVAL '2 HOUR', NULL, NOW() - INTERVAL '1 HOUR 55 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 20.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000),
('ORD789456123', 4, 4, 'BNBUSDT', 'BNB', 'USDT', 'BUY', 'MARKET', NULL, NULL, 10.00000000, 10.00000000, 4000.00000000, 1.60000000, 'FILLED', NOW() - INTERVAL '30 MINUTE', NULL, NOW() - INTERVAL '30 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 10.00000000, FALSE, NULL, 'IOC', NULL, 0.00000000),
('ORD159753456', 5, 4, 'BNBUSDT', 'BNB', 'USDT', 'SELL', 'MARKET', NULL, NULL, 10.00000000, 10.00000000, 4000.00000000, 1.60000000, 'FILLED', NOW() - INTERVAL '30 MINUTE', NULL, NOW() - INTERVAL '30 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 10.00000000, FALSE, NULL, 'IOC', NULL, 0.00000000),
('ORD753159456', 1, 3, 'SOLUSDT', 'SOL', 'USDT', 'BUY', 'STOP_LIMIT', 100.00000000, 99.00000000, 100.00000000, 0.00000000, 0.00000000, 0.00000000, 'NEW', NOW() - INTERVAL '15 MINUTE', NULL, NULL, NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 15.00000000, FALSE, NULL, 'GTC', 'MARK_PRICE', 0.00000000),
('ORD456159753', 2, 5, 'ADAUSDT', 'ADA', 'USDT', 'SELL', 'STOP_MARKET', NULL, 1.25000000, 1000.00000000, 0.00000000, 0.00000000, 0.00000000, 'NEW', NOW() - INTERVAL '10 MINUTE', NULL, NULL, NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 25.00000000, FALSE, NULL, 'GTC', 'LAST_PRICE', 0.00000000);

-- Thêm dữ liệu mẫu cho bảng contract_trade
INSERT INTO contract_trade (symbol, buy_order_id, sell_order_id, buy_member_id, sell_member_id, price, volume, buy_fee, sell_fee, buy_turnover, sell_turnover, time)
VALUES
('BTCUSDT', 'ORD123456789', 'ORD987654321', 1, 2, 50000.00000000, 1.00000000, 20.00000000, 20.00000000, 50000.00000000, 50000.00000000, NOW() - INTERVAL '55 MINUTE'),
('ETHUSDT', 'ORD123789456', 'ORD456123789', 1, 3, 3000.00000000, 5.00000000, 6.00000000, 6.00000000, 15000.00000000, 15000.00000000, NOW() - INTERVAL '1 HOUR 55 MINUTE'),
('BNBUSDT', 'ORD789456123', 'ORD159753456', 4, 5, 400.00000000, 10.00000000, 1.60000000, 1.60000000, 4000.00000000, 4000.00000000, NOW() - INTERVAL '30 MINUTE');

-- Thêm dữ liệu mẫu cho bảng contract_position
INSERT INTO contract_position (member_id, symbol, direction, volume, open_price, close_price, liquidation_price, maintenance_margin, margin, profit, margin_mode, leverage, create_time, update_time, remark, status)
VALUES
(1, 'BTCUSDT', 'LONG', 1.00000000, 50000.00000000, NULL, 45000.00000000, 250.00000000, 5000.00000000, 0.00000000, 'CROSSED', 10.00000000, NOW() - INTERVAL '55 MINUTE', NOW() - INTERVAL '55 MINUTE', NULL, 'OPEN'),
(2, 'BTCUSDT', 'SHORT', 1.00000000, 50000.00000000, NULL, 55000.00000000, 500.00000000, 10000.00000000, 0.00000000, 'CROSSED', 5.00000000, NOW() - INTERVAL '55 MINUTE', NOW() - INTERVAL '55 MINUTE', NULL, 'OPEN'),
(1, 'ETHUSDT', 'LONG', 5.00000000, 3000.00000000, NULL, 2850.00000000, 75.00000000, 750.00000000, 0.00000000, 'CROSSED', 20.00000000, NOW() - INTERVAL '1 HOUR 55 MINUTE', NOW() - INTERVAL '1 HOUR 55 MINUTE', NULL, 'OPEN'),
(3, 'ETHUSDT', 'SHORT', 5.00000000, 3000.00000000, NULL, 3150.00000000, 75.00000000, 750.00000000, 0.00000000, 'CROSSED', 20.00000000, NOW() - INTERVAL '1 HOUR 55 MINUTE', NOW() - INTERVAL '1 HOUR 55 MINUTE', NULL, 'OPEN'),
(4, 'BNBUSDT', 'LONG', 10.00000000, 400.00000000, NULL, 360.00000000, 20.00000000, 400.00000000, 0.00000000, 'CROSSED', 10.00000000, NOW() - INTERVAL '30 MINUTE', NOW() - INTERVAL '30 MINUTE', NULL, 'OPEN'),
(5, 'BNBUSDT', 'SHORT', 10.00000000, 400.00000000, NULL, 440.00000000, 20.00000000, 400.00000000, 0.00000000, 'CROSSED', 10.00000000, NOW() - INTERVAL '30 MINUTE', NOW() - INTERVAL '30 MINUTE', NULL, 'OPEN');

-- Thêm dữ liệu mẫu cho bảng contract_fee
INSERT INTO contract_fee (contract_id, member_id, symbol, order_id, direction, volume, price, turnover, fee, maker, create_time)
VALUES
(1, 1, 'BTCUSDT', 'ORD123456789', 'BUY', 1.00000000, 50000.00000000, 50000.00000000, 20.00000000, FALSE, NOW() - INTERVAL '55 MINUTE'),
(1, 2, 'BTCUSDT', 'ORD987654321', 'SELL', 1.00000000, 50000.00000000, 50000.00000000, 20.00000000, TRUE, NOW() - INTERVAL '55 MINUTE'),
(2, 1, 'ETHUSDT', 'ORD123789456', 'BUY', 5.00000000, 3000.00000000, 15000.00000000, 6.00000000, FALSE, NOW() - INTERVAL '1 HOUR 55 MINUTE'),
(2, 3, 'ETHUSDT', 'ORD456123789', 'SELL', 5.00000000, 3000.00000000, 15000.00000000, 6.00000000, TRUE, NOW() - INTERVAL '1 HOUR 55 MINUTE'),
(4, 4, 'BNBUSDT', 'ORD789456123', 'BUY', 10.00000000, 400.00000000, 4000.00000000, 1.60000000, FALSE, NOW() - INTERVAL '30 MINUTE'),
(4, 5, 'BNBUSDT', 'ORD159753456', 'SELL', 10.00000000, 400.00000000, 4000.00000000, 1.60000000, TRUE, NOW() - INTERVAL '30 MINUTE');

-- Thêm dữ liệu mẫu cho bảng contract_transaction
INSERT INTO contract_transaction (member_id, amount, coin, reference_id, create_time, type, address, fee, flag, real_fee, discount_fee, is_reward, contract_id, symbol, order_id, trade_id, leverage, margin_mode, realized_pnl, liquidation, adl, funding_fee)
VALUES
(1, -5000.00000000, 'USDT', 'ORD123456789', NOW() - INTERVAL '1 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 1, 'BTCUSDT', 'ORD123456789', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(2, -10000.00000000, 'USDT', 'ORD987654321', NOW() - INTERVAL '1 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 1, 'BTCUSDT', 'ORD987654321', NULL, 5, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(1, -750.00000000, 'USDT', 'ORD123789456', NOW() - INTERVAL '2 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 2, 'ETHUSDT', 'ORD123789456', NULL, 20, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(3, -750.00000000, 'USDT', 'ORD456123789', NOW() - INTERVAL '2 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 2, 'ETHUSDT', 'ORD456123789', NULL, 20, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(4, -400.00000000, 'USDT', 'ORD789456123', NOW() - INTERVAL '30 MINUTE', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 4, 'BNBUSDT', 'ORD789456123', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(5, -400.00000000, 'USDT', 'ORD159753456', NOW() - INTERVAL '30 MINUTE', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 4, 'BNBUSDT', 'ORD159753456', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(1, -20.00000000, 'USDT', 'FEE-ORD123456789', NOW() - INTERVAL '55 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 1, 'BTCUSDT', 'ORD123456789', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(2, -20.00000000, 'USDT', 'FEE-ORD987654321', NOW() - INTERVAL '55 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 1, 'BTCUSDT', 'ORD987654321', NULL, 5, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(1, -6.00000000, 'USDT', 'FEE-ORD123789456', NOW() - INTERVAL '1 HOUR 55 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 2, 'ETHUSDT', 'ORD123789456', NULL, 20, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(3, -6.00000000, 'USDT', 'FEE-ORD456123789', NOW() - INTERVAL '1 HOUR 55 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 2, 'ETHUSDT', 'ORD456123789', NULL, 20, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(4, -1.60000000, 'USDT', 'FEE-ORD789456123', NOW() - INTERVAL '30 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 4, 'BNBUSDT', 'ORD789456123', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(5, -1.60000000, 'USDT', 'FEE-ORD159753456', NOW() - INTERVAL '30 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 4, 'BNBUSDT', 'ORD159753456', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000);

-- Thêm dữ liệu mẫu cho bảng contract_last_price
INSERT INTO contract_last_price (contract_id, symbol, price, volume, create_time, update_time)
VALUES
(1, 'BTCUSDT', 50000.00000000, 1.00000000, NOW() - INTERVAL '55 MINUTE', NOW() - INTERVAL '55 MINUTE'),
(2, 'ETHUSDT', 3000.00000000, 5.00000000, NOW() - INTERVAL '1 HOUR 55 MINUTE', NOW() - INTERVAL '1 HOUR 55 MINUTE'),
(3, 'SOLUSDT', 100.00000000, 0.00000000, NOW() - INTERVAL '2 HOUR', NOW() - INTERVAL '2 HOUR'),
(4, 'BNBUSDT', 400.00000000, 10.00000000, NOW() - INTERVAL '30 MINUTE', NOW() - INTERVAL '30 MINUTE'),
(5, 'ADAUSDT', 1.20000000, 0.00000000, NOW() - INTERVAL '3 HOUR', NOW() - INTERVAL '3 HOUR'),
(6, 'BTCUSDT-240628', 50500.00000000, 0.50000000, NOW() - INTERVAL '45 MINUTE', NOW() - INTERVAL '45 MINUTE'),
(7, 'ETHUSDT-240628', 3050.00000000, 2.00000000, NOW() - INTERVAL '1 HOUR 45 MINUTE', NOW() - INTERVAL '1 HOUR 45 MINUTE');

-- Thêm dữ liệu mẫu cho bảng contract_index_price
INSERT INTO contract_index_price (contract_id, symbol, price, reference_prices, create_time)
VALUES
(1, 'BTCUSDT', 50000.00000000, '{"binance": 50010.00000000, "okex": 49990.00000000, "huobi": 50000.00000000}', NOW() - INTERVAL '1 HOUR'),
(2, 'ETHUSDT', 3000.00000000, '{"binance": 3005.00000000, "okex": 2995.00000000, "huobi": 3000.00000000}', NOW() - INTERVAL '1 HOUR'),
(3, 'SOLUSDT', 100.00000000, '{"binance": 100.50000000, "okex": 99.50000000, "huobi": 100.00000000}', NOW() - INTERVAL '1 HOUR'),
(4, 'BNBUSDT', 400.00000000, '{"binance": 401.00000000, "okex": 399.00000000, "huobi": 400.00000000}', NOW() - INTERVAL '1 HOUR'),
(5, 'ADAUSDT', 1.20000000, '{"binance": 1.21000000, "okex": 1.19000000, "huobi": 1.20000000}', NOW() - INTERVAL '1 HOUR'),
(6, 'BTCUSDT-240628', 50500.00000000, '{"binance": 50510.00000000, "okex": 50490.00000000, "huobi": 50500.00000000}', NOW() - INTERVAL '1 HOUR'),
(7, 'ETHUSDT-240628', 3050.00000000, '{"binance": 3055.00000000, "okex": 3045.00000000, "huobi": 3050.00000000}', NOW() - INTERVAL '1 HOUR');
