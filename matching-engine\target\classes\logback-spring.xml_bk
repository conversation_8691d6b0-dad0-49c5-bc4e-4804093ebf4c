<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- Set debug level for our matching engine packages -->
    <logger name="com.icetea.lotus.matching" level="INFO"/>
    
    <!-- Keep other packages at INFO level -->
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.apache.kafka" level="WARN"/>
    <logger name="org.mongodb" level="WARN"/>
    <logger name="org.redisson" level="WARN"/>
    
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
