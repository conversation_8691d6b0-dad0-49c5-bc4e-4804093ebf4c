-- <PERSON><PERSON> liệu mẫu cho bảng contract_coin
INSERT INTO contract_coin (symbol, name, base_symbol, quote_symbol, multiplier, min_volume, max_volume, price_precision, volume_precision, maintenance_margin_rate, initial_margin_rate, leverage_max, leverage_min, sort, fee, funding_rate_coefficient, max_funding_rate, min_funding_rate, funding_interval, margin_mode, enable, create_time, update_time)
VALUES 
('BTCUSDT', 'Bitcoin Perpetual', 'BTC', 'USDT', 1.0, 0.001, 1000.0, 0.5, 0.001, 0.005, 0.01, 125, 1, 1, 0.0005, 0.0001, 0.001, -0.001, 8, 'CROSSED', 1, NOW(), NOW()),
('ETHUSDT', 'Ethereum Perpetual', 'ETH', 'USDT', 1.0, 0.01, 10000.0, 0.05, 0.01, 0.005, 0.01, 100, 1, 2, 0.0005, 0.0001, 0.001, -0.001, 8, 'CROSSED', 1, NOW(), NOW()),
('SOLUSDT', 'Solana Perpetual', 'SOL', 'USDT', 1.0, 0.1, 100000.0, 0.01, 0.1, 0.005, 0.01, 75, 1, 3, 0.0005, 0.0001, 0.001, -0.001, 8, 'CROSSED', 1, NOW(), NOW()),
('BNBUSDT', 'Binance Coin Perpetual', 'BNB', 'USDT', 1.0, 0.01, 10000.0, 0.05, 0.01, 0.005, 0.01, 75, 1, 4, 0.0005, 0.0001, 0.001, -0.001, 8, 'CROSSED', 1, NOW(), NOW()),
('ADAUSDT', 'Cardano Perpetual', 'ADA', 'USDT', 1.0, 1.0, 1000000.0, 0.0001, 1.0, 0.005, 0.01, 75, 1, 5, 0.0005, 0.0001, 0.001, -0.001, 8, 'CROSSED', 1, NOW(), NOW());

-- Dữ liệu mẫu cho bảng contract_index_price
INSERT INTO contract_index_price (contract_id, symbol, price, reference_prices, create_time)
VALUES 
(1, 'BTCUSDT', 50000.0, '{"binance": 50100.0, "huobi": 49900.0, "okex": 50000.0}', NOW()),
(2, 'ETHUSDT', 3000.0, '{"binance": 3010.0, "huobi": 2990.0, "okex": 3000.0}', NOW()),
(3, 'SOLUSDT', 100.0, '{"binance": 101.0, "huobi": 99.0, "okex": 100.0}', NOW()),
(4, 'BNBUSDT', 400.0, '{"binance": 402.0, "huobi": 398.0, "okex": 400.0}', NOW()),
(5, 'ADAUSDT', 1.2, '{"binance": 1.21, "huobi": 1.19, "okex": 1.2}', NOW());

-- Dữ liệu mẫu cho bảng contract_mark_price
INSERT INTO contract_mark_price (contract_id, symbol, price, index_price, last_price, book_price, create_time)
VALUES 
(1, 'BTCUSDT', 50050.0, 50000.0, 50100.0, 50025.0, NOW()),
(2, 'ETHUSDT', 3005.0, 3000.0, 3010.0, 3002.5, NOW()),
(3, 'SOLUSDT', 100.5, 100.0, 101.0, 100.25, NOW()),
(4, 'BNBUSDT', 401.0, 400.0, 402.0, 400.5, NOW()),
(5, 'ADAUSDT', 1.21, 1.2, 1.22, 1.205, NOW());

-- Dữ liệu mẫu cho bảng contract_settlement_price
INSERT INTO contract_settlement_price (symbol, price, create_time)
VALUES 
('BTCUSDT', 50000.0, NOW()),
('ETHUSDT', 3000.0, NOW()),
('SOLUSDT', 100.0, NOW()),
('BNBUSDT', 400.0, NOW()),
('ADAUSDT', 1.2, NOW());

-- Dữ liệu mẫu cho bảng contract_last_price
INSERT INTO contract_last_price (contract_id, symbol, price, volume, create_time, update_time)
VALUES 
(1, 'BTCUSDT', 50100.0, 1.5, NOW(), NOW()),
(2, 'ETHUSDT', 3010.0, 10.0, NOW(), NOW()),
(3, 'SOLUSDT', 101.0, 100.0, NOW(), NOW()),
(4, 'BNBUSDT', 402.0, 5.0, NOW(), NOW()),
(5, 'ADAUSDT', 1.22, 1000.0, NOW(), NOW());

-- Dữ liệu mẫu cho bảng contract_funding_rate
INSERT INTO contract_funding_rate (contract_id, symbol, rate, mark_price, index_price, time, next_time)
VALUES 
(1, 'BTCUSDT', 0.0001, 50050.0, 50000.0, NOW(), NOW() + INTERVAL '8 HOUR'),
(2, 'ETHUSDT', 0.0002, 3005.0, 3000.0, NOW(), NOW() + INTERVAL '8 HOUR'),
(3, 'SOLUSDT', 0.0003, 100.5, 100.0, NOW(), NOW() + INTERVAL '8 HOUR'),
(4, 'BNBUSDT', 0.0002, 401.0, 400.0, NOW(), NOW() + INTERVAL '8 HOUR'),
(5, 'ADAUSDT', 0.0001, 1.21, 1.2, NOW(), NOW() + INTERVAL '8 HOUR');

-- Dữ liệu mẫu cho bảng contract_price_configuration
INSERT INTO contract_price_configuration (symbol, index_price_method, mark_price_method, custom_index_price_formula, custom_mark_price_formula, parameters, create_time, update_time)
VALUES 
('BTCUSDT', 'WEIGHTED_AVERAGE', 'HYBRID', NULL, NULL, '{"weights": {"binance": 0.4, "huobi": 0.3, "okex": 0.3}}', NOW(), NOW()),
('ETHUSDT', 'WEIGHTED_AVERAGE', 'HYBRID', NULL, NULL, '{"weights": {"binance": 0.4, "huobi": 0.3, "okex": 0.3}}', NOW(), NOW()),
('SOLUSDT', 'WEIGHTED_AVERAGE', 'HYBRID', NULL, NULL, '{"weights": {"binance": 0.4, "huobi": 0.3, "okex": 0.3}}', NOW(), NOW()),
('BNBUSDT', 'WEIGHTED_AVERAGE', 'HYBRID', NULL, NULL, '{"weights": {"binance": 0.4, "huobi": 0.3, "okex": 0.3}}', NOW(), NOW()),
('ADAUSDT', 'WEIGHTED_AVERAGE', 'HYBRID', NULL, NULL, '{"weights": {"binance": 0.4, "huobi": 0.3, "okex": 0.3}}', NOW(), NOW());
