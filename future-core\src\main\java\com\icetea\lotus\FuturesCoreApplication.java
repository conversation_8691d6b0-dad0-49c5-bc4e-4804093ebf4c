package com.icetea.lotus;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Lớp main cho ứng dụng Future Core
 * Kích hoạt tính năng Service Discovery để đăng ký với Consul
 * Kích hoạt tính năng Retry để tự động thử lại khi gặp lỗi
 * <PERSON><PERSON>ch hoạt tính năng Scheduling để chạy các tác vụ định kỳ
 * Kích hoạt tính năng Async để xử lý bất đồng bộ
 */
@SpringBootApplication
@EnableRetry
@EnableScheduling
@EnableAsync
@Slf4j
public class FuturesCoreApplication {
    // Đếm số lượng cặp tiền đang được nhận xử lý tại pod này
    public static final ConcurrentHashMap<String, Long> symbolHandlerCount = new ConcurrentHashMap<>();
    public static void main(String[] args) {
        SpringApplication.run(FuturesCoreApplication.class, args);
        log.info("Ứng dụng Future Core đã khởi động thành công với các tính năng nâng cao");
    }

}
