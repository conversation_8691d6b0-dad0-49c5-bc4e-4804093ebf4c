-- Tạo bảng contract_last_price
CREATE TABLE IF NOT EXISTS contract_last_price (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    volume DECIMAL(18,8) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL
);

-- T<PERSON><PERSON> các chỉ mục cho bảng contract_last_price
CREATE INDEX IF NOT EXISTS idx_contract_last_price_symbol ON contract_last_price (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_last_price_contract_id ON contract_last_price (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_last_price_create_time ON contract_last_price (create_time);
