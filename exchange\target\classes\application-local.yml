spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${SPOT_DATASOURCE_URL:*****************************************************************}
    username: ${SPOT_DATASOURCE_USERNAME:spot_user}
    password: ${SPOT_DATASOURCE_PASSWORD:d2mwpsfU4fhSDvysiC60kwIXL3Zjojf6I9DlZJE1FUxYdGMgFPXOYZl0p6LidZnA}
  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:*******************************************}
      database: ${SPRING_MONGODB_DATABASE:spot}
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:29092}
    consumer:
      group.id: ${KAFKA_CONSUMER_GROUP_ID:exchange-local}
    properties:
      #      security.protocol: SASL_PLAINTEXT
      sasl.mechanism: PLAIN
      #      sasl.jaas.config: >-
      #        org.apache.kafka.common.security.plain.PlainLoginModule required username=""
      #        password="2m0881Xc30Wh";

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}
      #        instance-id: ${HOSTNAME}-${SPRING_APPLICATION_NAME:exchange}-${SERVER_PORT}
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://************:8082/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082/realms/cex-lotus}
            token-uri: ${KEYCLOAK_TOKEN_URI:http://************:32082/realms/cex-lotus/protocol/openid-connect/token}
keycloak:
  auth-server-url: ${KEYCLOAK_AUTH_SERVER_URL:http://************:32082}
  realm: cex-lotus
  resource: cex-exchange
  credentials:
    secret: Okc6TpmuPBeySygoiUXTtsRKzdvC1sg0
cex-security:
  permit-all-endpoints:
    - "/**"
topic-kafka:
  exchange:
    order: ${EXCHANGE_ORDER:exchange-order-local}
    order-cancel-success: ${EXCHANGE_ORDER_CANCEL_SUCCESS:exchange-order-cancel-success-local}
    order-completed: ${EXCHANGE_ORDER_COMPLETED:exchange-order-completed-local}
    order-cancel: ${EXCHANGE_ORDER_CANCEL:exchange-order-cancel-local}
    trade: ${EXCHANGE_TRADE:exchange-trade-local}
    trade-plate: ${EXCHANGE_TRADE_PLATE:exchange-trade-plate-local}