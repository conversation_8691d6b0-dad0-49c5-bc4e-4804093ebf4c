package com.icetea.lotus.infrastructure.sharding;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.icetea.lotus.FuturesCoreApplication.symbolHandlerCount;

/**
 * Smart Sharding Manager với intelligent load balancing
 * Thay thế logic sharding cũ bằng cơ chế thông minh hơn
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmartShardingManager {

    private final PartitionBasedLoadBalancer loadBalancer;
    private final PodLoadMonitor podLoadMonitor;
    private final RedissonClient redissonClient;

    // Constants
    private static final String SMART_SHARDING_LOCK = "smart-sharding-lock";
    private static final double OVERLOAD_THRESHOLD = 0.8;
    private static final double UNDERLOAD_THRESHOLD = 0.3;

    @Value("${pod.min-acquired:1000}")
    private long minAcquired;

    @Value("${pod.max-lock:30}")
    private long maxLock;

    @Value("${pod.max-symbol:10}")
    private int maxSymbol;

    /**
     * Kiểm tra xem symbol có được gán cho pod này không
     * Thay thế cho SymbolShardingManager.isSymbolOwnedByThisPod()
     */
    public boolean canProcessSymbol(String symbol) {
        if (symbolHandlerCount.size() >= maxSymbol) {
            return false;
        } else {
            // lock this symbol on Redis and return true
            String lockKey = "lock:pair:" + symbol;
            if (symbolHandlerCount.containsKey(lockKey)) {
                // check lock symbol within `maxLock` time
                if (symbolHandlerCount.get(lockKey) + maxLock * 1000L > System.currentTimeMillis()) {
                    return true;
                } {
                    symbolHandlerCount.remove(lockKey);
                }
            } else {
                // trying to claim this symbol
                RLock lock = redissonClient.getLock(lockKey);
                boolean acquired = false;
                try {
                    // Try to acquire lock within 2s, and auto-release after 30s
                    log.info("try lock:{} with minAcquired: {} and max log time sec: {}", lockKey, minAcquired, maxLock);
                    acquired = lock.tryLock(minAcquired, maxLock * 1000, TimeUnit.MILLISECONDS);

                    if (acquired) {
                        // ✔ Thực hiện xử lý
                        log.info("lock acquired: {}", lockKey);
                        symbolHandlerCount.put(lockKey, System.currentTimeMillis());
                        return true;
                    } else {
                        log.info("Another pod is processing {}", lockKey);
                        symbolHandlerCount.remove(lockKey);
                        return false;
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    if (acquired && lock.isHeldByCurrentThread()) {
                        lock.unlock(); // 🛑 Giải phóng lock
                    }
                }
            }
        }
        return false;
    }

    /**
     * Smart rebalancing - chạy định kỳ
     */
    @Scheduled(fixedRate = 60000) // Mỗi phút
    public void performSmartRebalancing() {
        RLock lock = redissonClient.getLock(SMART_SHARDING_LOCK);
        
        try {
            // Sử dụng quick lock cho smart rebalancing (200ms wait, 2000ms lease)
            if (lock.tryLock(200, 2000, TimeUnit.MILLISECONDS)) {
                try {
                    log.debug("Starting smart rebalancing...");
                    
                    // Phân tích load của tất cả pods
                    Map<String, PodLoadInfo> podLoads = analyzePodLoads();
                    
                    // Tìm pods overloaded và underloaded
                    List<String> overloadedPods = findOverloadedPods(podLoads);
                    List<String> underloadedPods = findUnderloadedPods(podLoads);
                    
                    if (!overloadedPods.isEmpty() && !underloadedPods.isEmpty()) {
                        // Thực hiện rebalancing
                        performLoadRebalancing(overloadedPods, underloadedPods, podLoads);
                    }
                    
                    // Cập nhật partition configurations
                    updatePartitionConfigurations();
                    
                    log.debug("Smart rebalancing completed");
                    
                } finally {
                    lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted during smart rebalancing");
        }
    }
    
    /**
     * Phân tích load của tất cả pods
     */
    private Map<String, PodLoadInfo> analyzePodLoads() {
        List<String> availablePods = podLoadMonitor.getAvailablePods();
        Map<String, PodLoadInfo> podLoads = new HashMap<>();
        
        for (String pod : availablePods) {
            PodLoadInfo loadInfo = podLoadMonitor.getPodLoadInfo(pod);
            if (loadInfo != null) {
                podLoads.put(pod, loadInfo);
            }
        }
        
        return podLoads;
    }
    
    /**
     * Tìm pods bị overloaded
     */
    private List<String> findOverloadedPods(Map<String, PodLoadInfo> podLoads) {
        return podLoads.entrySet().stream()
                .filter(entry -> entry.getValue().getOverallLoad() > OVERLOAD_THRESHOLD)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
    
    /**
     * Tìm pods bị underloaded
     */
    private List<String> findUnderloadedPods(Map<String, PodLoadInfo> podLoads) {
        return podLoads.entrySet().stream()
                .filter(entry -> entry.getValue().getOverallLoad() < UNDERLOAD_THRESHOLD)
                .filter(entry -> entry.getValue().isHealthy())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
    
    /**
     * Thực hiện load rebalancing
     */
    private void performLoadRebalancing(List<String> overloadedPods, 
                                       List<String> underloadedPods,
                                       Map<String, PodLoadInfo> podLoads) {
        
        for (String overloadedPod : overloadedPods) {
            // Tìm symbols/partitions có thể di chuyển
            List<String> movablePartitions = findMovablePartitions(overloadedPod);
            
            for (String partition : movablePartitions) {
                if (underloadedPods.isEmpty()) break;
                
                // Chọn target pod tốt nhất
                String targetPod = selectBestTargetPod(underloadedPods, podLoads);
                
                if (targetPod != null) {
                    // Di chuyển partition
                    boolean moved = movePartition(partition, overloadedPod, targetPod);
                    
                    if (moved) {
                        log.info("Moved partition {} from {} to {}", partition, overloadedPod, targetPod);
                        
                        // Cập nhật load info
                        updateLoadAfterMove(targetPod, podLoads);
                        
                        // Nếu target pod đã đủ load, remove khỏi underloaded list
                        PodLoadInfo targetLoad = podLoads.get(targetPod);
                        if (targetLoad != null && targetLoad.getOverallLoad() > UNDERLOAD_THRESHOLD) {
                            underloadedPods.remove(targetPod);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Tìm partitions có thể di chuyển từ pod
     */
    private List<String> findMovablePartitions(String podName) {
        RMap<String, String> assignments = redissonClient.getMap("partition-assignments");
        
        return assignments.entrySet().stream()
                .filter(entry -> podName.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .filter(this::isPartitionMovable)
                .collect(Collectors.toList());
    }
    
    /**
     * Kiểm tra xem partition có thể di chuyển không
     */
    private boolean isPartitionMovable(String partition) {
        // Kiểm tra load của partition
        String loadKey = "partition:load:" + partition;
        Object load = redissonClient.getBucket(loadKey).get();
        
        // Chỉ di chuyển partitions có load thấp hoặc trung bình
        if (load != null) {
            double partitionLoad = Double.parseDouble(load.toString());
            return partitionLoad < 0.6; // Chỉ move partitions có load < 60%
        }
        
        return true; // Default: có thể move
    }
    
    /**
     * Chọn target pod tốt nhất
     */
    private String selectBestTargetPod(List<String> candidates, Map<String, PodLoadInfo> podLoads) {
        return candidates.stream()
                .filter(pod -> podLoads.get(pod).canAcceptMoreLoad())
                .min((pod1, pod2) -> Double.compare(
                    podLoads.get(pod1).getOverallLoad(),
                    podLoads.get(pod2).getOverallLoad()
                ))
                .orElse(null);
    }
    
    /**
     * Di chuyển partition từ source đến target pod
     */
    private boolean movePartition(String partition, String sourcePod, String targetPod) {
        String lockKey = "move-partition:" + partition;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            // Sử dụng quick lock cho move partition (100ms wait, 1000ms lease)
            if (lock.tryLock(100, 1000, TimeUnit.MILLISECONDS)) {
                try {
                    // Cập nhật assignment
                    RMap<String, String> assignments = redissonClient.getMap("partition-assignments");
                    assignments.put(partition, targetPod);
                    
                    // Trigger partition state transfer (sẽ được handle bởi DistributedMatchingEngineManager)
                    triggerPartitionTransfer(partition, sourcePod, targetPod);
                    
                    return true;
                    
                } finally {
                    lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted while moving partition {}", partition);
        }
        
        return false;
    }
    
    /**
     * Trigger partition transfer
     */
    private void triggerPartitionTransfer(String partition, String sourcePod, String targetPod) {
        // Gửi notification để source pod save state và target pod load state
        String transferKey = "partition:transfer:" + partition;
        Map<String, String> transferInfo = new HashMap<>();
        transferInfo.put("sourcePod", sourcePod);
        transferInfo.put("targetPod", targetPod);
        transferInfo.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        redissonClient.getMap(transferKey).putAll(transferInfo);
        
        log.info("Triggered partition transfer: {} from {} to {}", partition, sourcePod, targetPod);
    }
    
    /**
     * Cập nhật load sau khi move
     */
    private void updateLoadAfterMove(String targetPod, Map<String, PodLoadInfo> podLoads) {
        PodLoadInfo loadInfo = podLoads.get(targetPod);
        if (loadInfo != null) {
            // Estimate load increase (simplified)
            PodLoadInfo updatedInfo = PodLoadInfo.builder()
                    .podName(targetPod)
                    .cpuUsage(Math.min(1.0, loadInfo.getCpuUsage() + 0.1))
                    .memoryUsage(Math.min(1.0, loadInfo.getMemoryUsage() + 0.05))
                    .networkUsage(loadInfo.getNetworkUsage())
                    .diskUsage(loadInfo.getDiskUsage())
                    .activeConnections(loadInfo.getActiveConnections() + 10)
                    .orderRate(loadInfo.getOrderRate() + 5)
                    .avgLatency(loadInfo.getAvgLatency())
                    .errorRate(loadInfo.getErrorRate())
                    .lastUpdated(System.currentTimeMillis())
                    .healthy(loadInfo.isHealthy())
                    .build();
            
            podLoads.put(targetPod, updatedInfo);
        }
    }
    
    /**
     * Cập nhật partition configurations
     */
    private void updatePartitionConfigurations() {
        // Lấy danh sách tất cả symbols
        Set<String> symbols = getAllActiveSymbols();

        for (String symbol : symbols) {
            if (loadBalancer.shouldRebalancePartitions(symbol)) {
                loadBalancer.rebalancePartitions(symbol);
            }
        }
    }

    /**
     * Thực hiện graceful migration cho partition
     */
    public boolean performGracefulMigration(String partitionKey, String sourcePod, String targetPod) {
        String lockKey = "graceful-migration:" + partitionKey;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // Sử dụng medium lock cho graceful migration (500ms wait, 5000ms lease)
            if (lock.tryLock(500, 5000, TimeUnit.MILLISECONDS)) {
                try {
                    log.info("Starting graceful migration: {} from {} to {}", partitionKey, sourcePod, targetPod);

                    // Bước 1: Tạm dừng nhận orders mới cho partition này
                    pausePartitionProcessing(partitionKey);

                    // Bước 2: Đợi xử lý hết orders đang pending
                    waitForPendingOrders(partitionKey);

                    // Bước 3: Tạo snapshot của partition state
                    String snapshot = createPartitionSnapshot(partitionKey);

                    // Bước 4: Transfer snapshot đến target pod
                    boolean transferred = transferSnapshotToTarget(partitionKey, snapshot, targetPod);

                    if (transferred) {
                        // Bước 5: Cập nhật partition assignment
                        updatePartitionAssignment(partitionKey, targetPod);

                        // Bước 6: Resume processing trên target pod
                        resumePartitionProcessing(partitionKey, targetPod);

                        // Bước 7: Cleanup trên source pod
                        cleanupSourcePartition(partitionKey, sourcePod);

                        log.info("Graceful migration completed: {} from {} to {}", partitionKey, sourcePod, targetPod);
                        return true;
                    } else {
                        // Rollback nếu transfer thất bại
                        resumePartitionProcessing(partitionKey, sourcePod);
                        log.error("Graceful migration failed: {} from {} to {}", partitionKey, sourcePod, targetPod);
                        return false;
                    }

                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("Could not acquire migration lock for partition: {}", partitionKey);
                return false;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted during graceful migration for partition: {}", partitionKey);
            return false;
        }
    }

    /**
     * Tạm dừng xử lý partition
     */
    private void pausePartitionProcessing(String partitionKey) {
        String pauseKey = "partition:paused:" + partitionKey;
        redissonClient.getBucket(pauseKey).set(true);
        redissonClient.getBucket(pauseKey).expire(java.time.Duration.ofSeconds(300)); // 5 minutes timeout
        log.info("Paused processing for partition: {}", partitionKey);
    }

    /**
     * Tiếp tục xử lý partition
     */
    private void resumePartitionProcessing(String partitionKey, String podName) {
        String pauseKey = "partition:paused:" + partitionKey;
        redissonClient.getBucket(pauseKey).delete();
        log.info("Resumed processing for partition: {} on pod: {}", partitionKey, podName);
    }

    /**
     * Đợi xử lý hết orders đang pending
     */
    private void waitForPendingOrders(String partitionKey) {
        try {
            // Đợi tối đa 30 giây để xử lý hết orders pending
            Thread.sleep(5000); // Simplified - trong thực tế sẽ check queue size
            log.info("Finished waiting for pending orders for partition: {}", partitionKey);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Interrupted while waiting for pending orders for partition: {}", partitionKey);
        }
    }

    /**
     * Tạo snapshot của partition state
     */
    private String createPartitionSnapshot(String partitionKey) {
        try {
            String stateKey = "partition:state:" + partitionKey;
            Object snapshot = redissonClient.getBucket(stateKey).get();
            return snapshot != null ? snapshot.toString() : "{}";
        } catch (Exception e) {
            log.error("Error creating snapshot for partition {}: {}", partitionKey, e.getMessage());
            return "{}";
        }
    }

    /**
     * Transfer snapshot đến target pod
     */
    private boolean transferSnapshotToTarget(String partitionKey, String snapshot, String targetPod) {
        try {
            String transferKey = "partition:transfer:" + partitionKey + ":" + targetPod;
            redissonClient.getBucket(transferKey).set(snapshot);
            redissonClient.getBucket(transferKey).expire(java.time.Duration.ofSeconds(600)); // 10 minutes

            // Notify target pod
            String notificationKey = "partition:notification:" + targetPod;
            redissonClient.getList(notificationKey).add(partitionKey);

            log.info("Transferred snapshot for partition {} to pod {}", partitionKey, targetPod);
            return true;
        } catch (Exception e) {
            log.error("Error transferring snapshot for partition {} to pod {}: {}",
                     partitionKey, targetPod, e.getMessage());
            return false;
        }
    }

    /**
     * Cập nhật partition assignment
     */
    private void updatePartitionAssignment(String partitionKey, String targetPod) {
        RMap<String, String> assignments = redissonClient.getMap("partition-assignments");
        assignments.put(partitionKey, targetPod);
        log.info("Updated partition assignment: {} -> {}", partitionKey, targetPod);
    }

    /**
     * Cleanup partition trên source pod
     */
    private void cleanupSourcePartition(String partitionKey, String sourcePod) {
        try {
            // Remove partition state
            String stateKey = "partition:state:" + partitionKey;
            redissonClient.getBucket(stateKey).delete();

            // Notify source pod to cleanup
            String cleanupKey = "partition:cleanup:" + sourcePod;
            redissonClient.getList(cleanupKey).add(partitionKey);

            log.info("Cleaned up partition {} from source pod {}", partitionKey, sourcePod);
        } catch (Exception e) {
            log.error("Error cleaning up partition {} from source pod {}: {}",
                     partitionKey, sourcePod, e.getMessage());
        }
    }
    
    /**
     * Lấy danh sách tất cả symbols đang active
     */
    private Set<String> getAllActiveSymbols() {
        // Lấy từ Redis hoặc database
        RMap<String, String> assignments = redissonClient.getMap("partition-assignments");
        
        return assignments.keySet().stream()
                .map(partition -> partition.split("-")[0]) // Extract symbol from partition key
                .collect(Collectors.toSet());
    }
    
    /**
     * Lấy partition owner
     */
    private String getPartitionOwner(String partition) {
        RMap<String, String> assignments = redissonClient.getMap("partition-assignments");
        return assignments.get(partition);
    }
    
    /**
     * Lấy primary pod cho symbol
     */
    private String getPrimaryPodForSymbol(String symbol) {
        String key = "primary-pod:" + symbol;
        return (String) redissonClient.getBucket(key).get();
    }

}
