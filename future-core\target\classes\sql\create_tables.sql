-- Tạo bảng contract_coin
CREATE TABLE IF NOT EXISTS contract_coin (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    base_symbol VARCHAR(50) NOT NULL,
    quote_symbol VARCHAR(50) NOT NULL,
    multiplier DECIMAL(18,8) NOT NULL,
    min_volume DECIMAL(18,8) NOT NULL,
    max_volume DECIMAL(18,8) NOT NULL,
    price_precision DECIMAL(18,8) NOT NULL,
    volume_precision DECIMAL(18,8) NOT NULL,
    maintenance_margin_rate DECIMAL(18,8) NOT NULL,
    initial_margin_rate DECIMAL(18,8) NOT NULL,
    leverage_max INTEGER NOT NULL,
    leverage_min INTEGER NOT NULL,
    sort INTEGER,
    fee DECIMAL(18,8) NOT NULL,
    funding_rate_coefficient DECIMAL(18,8) NOT NULL,
    max_funding_rate DECIMAL(18,8) NOT NULL,
    min_funding_rate DECIMAL(18,8) NOT NULL,
    funding_interval INTEGER NOT NULL,
    margin_mode VARCHAR(20) NOT NULL,
    expiry_date TIMESTAMP,
    enable INTEGER NOT NULL DEFAULT 1,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    CONSTRAINT uk_contract_coin_symbol UNIQUE (symbol)
);

-- Tạo bảng contract_order
CREATE TABLE IF NOT EXISTS contract_order (
    order_id VARCHAR(50) PRIMARY KEY,
    member_id BIGINT NOT NULL,
    contract_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    coin_symbol VARCHAR(50) NOT NULL,
    base_symbol VARCHAR(50) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    type VARCHAR(20) NOT NULL,
    price DECIMAL(18,8),
    trigger_price DECIMAL(18,8),
    volume DECIMAL(18,8) NOT NULL,
    filled_volume DECIMAL(18,8) DEFAULT 0,
    turnover DECIMAL(18,8) DEFAULT 0,
    fee DECIMAL(18,8) DEFAULT 0,
    status VARCHAR(20) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    canceled_time TIMESTAMP,
    execute_time TIMESTAMP,
    expire_time TIMESTAMP,
    complete_time TIMESTAMP,
    liquidation BOOLEAN DEFAULT FALSE,
    adl BOOLEAN DEFAULT FALSE,
    implied BOOLEAN DEFAULT FALSE,
    source_order_id VARCHAR(50),
    oco_id VARCHAR(50),
    oco_order_no VARCHAR(50),
    leverage DECIMAL(18,8) NOT NULL,
    reduce_only BOOLEAN DEFAULT FALSE,
    callback_rate DECIMAL(18,8),
    time_in_force VARCHAR(10) NOT NULL,
    trigger_type VARCHAR(20),
    max_slippage DECIMAL(18,8) DEFAULT 0,
    activation_price DECIMAL(18,8),
    post_only BOOLEAN DEFAULT FALSE,
    fill_or_kill BOOLEAN DEFAULT FALSE,
    immediate_or_cancel BOOLEAN DEFAULT FALSE,
    cancel_reason VARCHAR(255),
    deal_volume DECIMAL(18,8) DEFAULT 0,
    deal_money DECIMAL(18,8) DEFAULT 0
);

-- Tạo bảng contract_order_detail
CREATE TABLE IF NOT EXISTS contract_order_detail (
    id VARCHAR(50) PRIMARY KEY,
    order_id VARCHAR(50) NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    amount DECIMAL(18,8) NOT NULL,
    turnover DECIMAL(18,8) NOT NULL,
    fee DECIMAL(18,8) NOT NULL,
    time TIMESTAMP NOT NULL,
    contract_id BIGINT NOT NULL,
    leverage DECIMAL(18,8) NOT NULL,
    margin_mode VARCHAR(20) NOT NULL
);

-- Tạo bảng contract_position
CREATE TABLE IF NOT EXISTS contract_position (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    volume DECIMAL(18,8) NOT NULL,
    open_price DECIMAL(18,8) NOT NULL,
    close_price DECIMAL(18,8),
    liquidation_price DECIMAL(18,8),
    maintenance_margin DECIMAL(18,8) NOT NULL,
    margin DECIMAL(18,8) NOT NULL,
    profit DECIMAL(18,8) DEFAULT 0,
    margin_mode VARCHAR(20) NOT NULL,
    leverage DECIMAL(18,8) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    remark VARCHAR(255),
    status VARCHAR(20) NOT NULL
);

-- Tạo bảng contract_trade
CREATE TABLE IF NOT EXISTS contract_trade (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    buy_order_id VARCHAR(50) NOT NULL,
    sell_order_id VARCHAR(50) NOT NULL,
    buy_member_id BIGINT NOT NULL,
    sell_member_id BIGINT NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    volume DECIMAL(18,8) NOT NULL,
    buy_fee DECIMAL(18,8) NOT NULL,
    sell_fee DECIMAL(18,8) NOT NULL,
    buy_turnover DECIMAL(18,8) NOT NULL,
    sell_turnover DECIMAL(18,8) NOT NULL,
    time TIMESTAMP NOT NULL,
    buy_order_type VARCHAR(20),
    sell_order_type VARCHAR(20)
);

-- Tạo bảng contract_wallet
CREATE TABLE IF NOT EXISTS contract_wallet (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT NOT NULL,
    coin VARCHAR(50) NOT NULL,
    balance DECIMAL(26,16) NOT NULL DEFAULT 0,
    frozen_balance DECIMAL(26,16) NOT NULL DEFAULT 0,
    available_balance DECIMAL(18,8) NOT NULL DEFAULT 0,
    address VARCHAR(100),
    unrealized_pnl DECIMAL(18,8) DEFAULT 0,
    realized_pnl DECIMAL(18,8) DEFAULT 0,
    used_margin DECIMAL(18,8) DEFAULT 0,
    total_fee DECIMAL(18,8) DEFAULT 0,
    total_funding_fee DECIMAL(18,8) DEFAULT 0,
    is_locked BOOLEAN DEFAULT FALSE,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    version INTEGER NOT NULL DEFAULT 0,
    CONSTRAINT uk_contract_wallet_member_coin UNIQUE (member_id, coin)
);

-- Tạo bảng contract_transaction
CREATE TABLE IF NOT EXISTS contract_transaction (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT NOT NULL,
    amount DECIMAL(26,16) NOT NULL,
    coin VARCHAR(50) NOT NULL,
    reference_id VARCHAR(100),
    create_time TIMESTAMP NOT NULL,
    type VARCHAR(50) NOT NULL,
    address VARCHAR(100),
    fee DECIMAL(26,16),
    flag INTEGER NOT NULL DEFAULT 0,
    real_fee VARCHAR(50),
    discount_fee VARCHAR(50),
    is_reward INTEGER DEFAULT 0,
    contract_id BIGINT,
    symbol VARCHAR(50),
    order_id VARCHAR(50),
    trade_id VARCHAR(50),
    leverage INTEGER,
    margin_mode VARCHAR(20),
    realized_pnl DECIMAL(18,8) DEFAULT 0,
    liquidation BOOLEAN,
    adl BOOLEAN,
    funding_fee DECIMAL(18,8) DEFAULT 0
);

-- Tạo bảng contract_funding_rate
CREATE TABLE IF NOT EXISTS contract_funding_rate (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    rate DECIMAL(18,8) DEFAULT 0,
    mark_price DECIMAL(18,8) DEFAULT 0,
    index_price DECIMAL(18,8) DEFAULT 0,
    time TIMESTAMP NOT NULL,
    next_time TIMESTAMP NOT NULL
);

-- Tạo bảng contract_funding_payment
CREATE TABLE IF NOT EXISTS contract_funding_payment (
    id BIGSERIAL PRIMARY KEY,
    position_id BIGINT NOT NULL,
    member_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    amount DECIMAL(18,8) NOT NULL,
    rate DECIMAL(18,8) NOT NULL,
    time TIMESTAMP NOT NULL
);

-- Tạo bảng funding_settlement
CREATE TABLE IF NOT EXISTS funding_settlement (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    member_id BIGINT NOT NULL,
    position_id BIGINT NOT NULL,
    funding_rate DECIMAL(18,8) NOT NULL,
    funding_amount DECIMAL(18,8) NOT NULL,
    timestamp TIMESTAMP NOT NULL
);

-- Tạo bảng contract_settlement
CREATE TABLE IF NOT EXISTS contract_settlement (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    member_id BIGINT NOT NULL,
    position_id BIGINT NOT NULL,
    settlement_price DECIMAL(18,8) NOT NULL,
    pnl DECIMAL(18,8) NOT NULL,
    create_time TIMESTAMP NOT NULL
);

-- Tạo bảng contract_fee
CREATE TABLE IF NOT EXISTS contract_fee (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    member_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    order_id VARCHAR(50) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    volume DECIMAL(18,8) DEFAULT 0,
    price DECIMAL(18,8) DEFAULT 0,
    turnover DECIMAL(18,8) DEFAULT 0,
    fee DECIMAL(18,8) DEFAULT 0,
    maker BOOLEAN,
    create_time TIMESTAMP NOT NULL
);

-- Tạo bảng contract_insurance_fund
CREATE TABLE IF NOT EXISTS contract_insurance_fund (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    balance DECIMAL(18,8) DEFAULT 0,
    frozen_balance DECIMAL(18,8) DEFAULT 0,
    available_balance DECIMAL(18,8) DEFAULT 0,
    total_used DECIMAL(18,8) DEFAULT 0,
    total_deposit DECIMAL(18,8) DEFAULT 0,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    remark VARCHAR(255)
);

-- Tạo bảng contract_liquidation
CREATE TABLE IF NOT EXISTS contract_liquidation (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    member_id BIGINT NOT NULL,
    position_id BIGINT NOT NULL,
    liquidation_order_id VARCHAR(50) NOT NULL,
    liquidation_price DECIMAL(18,8) DEFAULT 0,
    liquidation_volume DECIMAL(18,8) DEFAULT 0,
    realized_pnl DECIMAL(18,8) DEFAULT 0,
    insurance_amount DECIMAL(18,8) DEFAULT 0,
    type VARCHAR(20) NOT NULL,
    create_time TIMESTAMP NOT NULL
);

-- Tạo bảng contract_adl_record
CREATE TABLE IF NOT EXISTS contract_adl_record (
    id BIGSERIAL PRIMARY KEY,
    position_id BIGINT NOT NULL,
    member_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    volume DECIMAL(18,8) NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    time TIMESTAMP NOT NULL
);

-- Tạo bảng contract_clawback_position
CREATE TABLE IF NOT EXISTS contract_clawback_position (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    member_id BIGINT NOT NULL,
    position_id BIGINT NOT NULL,
    unrealized_pnl DECIMAL(18,8) DEFAULT 0,
    clawback_amount DECIMAL(18,8) DEFAULT 0,
    clawback_rate DECIMAL(18,8) DEFAULT 0,
    create_time TIMESTAMP NOT NULL
);

-- Tạo bảng contract_circuit_breaker
CREATE TABLE IF NOT EXISTS contract_circuit_breaker (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    reason VARCHAR(255),
    trigger_price DECIMAL(18,8) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL
);

-- Tạo bảng contract_user_leverage_setting
CREATE TABLE IF NOT EXISTS contract_user_leverage_setting (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    leverage DECIMAL(18,8) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    CONSTRAINT uk_user_leverage_setting_member_symbol UNIQUE (member_id, symbol)
);

-- Tạo bảng contract_user_position_mode
CREATE TABLE IF NOT EXISTS contract_user_position_mode (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT NOT NULL,
    position_mode VARCHAR(20) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    CONSTRAINT uk_user_position_mode_member UNIQUE (member_id)
);

-- Tạo bảng contract_price_configuration
CREATE TABLE IF NOT EXISTS contract_price_configuration (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    index_price_method VARCHAR(50) NOT NULL,
    mark_price_method VARCHAR(50) NOT NULL,
    custom_index_price_formula TEXT,
    custom_mark_price_formula TEXT,
    parameters TEXT,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    CONSTRAINT uk_price_configuration_symbol UNIQUE (symbol)
);

-- Tạo bảng contract_last_price
CREATE TABLE IF NOT EXISTS contract_last_price (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    volume DECIMAL(18,8) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL
);

-- Tạo bảng contract_index_price
CREATE TABLE IF NOT EXISTS contract_index_price (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT,
    symbol VARCHAR(50) NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    reference_prices TEXT,
    create_time TIMESTAMP NOT NULL
);

-- Tạo bảng contract_mark_price
CREATE TABLE IF NOT EXISTS contract_mark_price (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT,
    symbol VARCHAR(50) NOT NULL,
    price NUMERIC(20, 8) NOT NULL,
    index_price NUMERIC(20, 8),
    last_price NUMERIC(20, 8),
    book_price NUMERIC(20, 8),
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_contract_mark_price_contract FOREIGN KEY (contract_id) REFERENCES contract_coin(id) ON DELETE CASCADE
);

-- Tạo index cho bảng contract_mark_price
CREATE INDEX IF NOT EXISTS idx_contract_mark_price_symbol ON contract_mark_price(symbol);
CREATE INDEX IF NOT EXISTS idx_contract_mark_price_create_time ON contract_mark_price(create_time);

-- Thêm comment cho bảng contract_mark_price và các cột
COMMENT ON TABLE contract_mark_price IS 'Bảng lưu trữ giá đánh dấu (mark price) của các hợp đồng';
COMMENT ON COLUMN contract_mark_price.id IS 'ID của giá đánh dấu';
COMMENT ON COLUMN contract_mark_price.contract_id IS 'ID của hợp đồng';
COMMENT ON COLUMN contract_mark_price.symbol IS 'Symbol của hợp đồng';
COMMENT ON COLUMN contract_mark_price.price IS 'Giá đánh dấu';
COMMENT ON COLUMN contract_mark_price.index_price IS 'Giá chỉ số tương ứng';
COMMENT ON COLUMN contract_mark_price.last_price IS 'Giá giao dịch gần nhất';
COMMENT ON COLUMN contract_mark_price.book_price IS 'Giá sổ lệnh';
COMMENT ON COLUMN contract_mark_price.create_time IS 'Thời gian tạo';

-- Tạo bảng contract_settlement_price
CREATE TABLE IF NOT EXISTS contract_settlement_price (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    price NUMERIC(20, 8) NOT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    CONSTRAINT fk_contract_settlement_price_symbol FOREIGN KEY (symbol) REFERENCES contract_coin(symbol) ON DELETE CASCADE
);

-- Tạo index cho bảng contract_settlement_price
CREATE INDEX IF NOT EXISTS idx_contract_settlement_price_symbol ON contract_settlement_price(symbol);
CREATE INDEX IF NOT EXISTS idx_contract_settlement_price_create_time ON contract_settlement_price(create_time);

-- Thêm comment cho bảng contract_settlement_price và các cột
COMMENT ON TABLE contract_settlement_price IS 'Bảng lưu trữ giá thanh toán (settlement price) của các hợp đồng';
COMMENT ON COLUMN contract_settlement_price.id IS 'ID của giá thanh toán';
COMMENT ON COLUMN contract_settlement_price.symbol IS 'Symbol của hợp đồng';
COMMENT ON COLUMN contract_settlement_price.price IS 'Giá thanh toán';
COMMENT ON COLUMN contract_settlement_price.create_time IS 'Thời gian tạo';
