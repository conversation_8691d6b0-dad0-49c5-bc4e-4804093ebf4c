-- Self Trade Prevention Migration Script for Exchange-Core
-- Adds STP mode column to exchange_order table

-- Add STP mode column to exchange_order table
ALTER TABLE exchange_order 
ADD COLUMN stp_mode VARCHAR(20) DEFAULT 'CANCEL_TAKER' 
COMMENT 'Self Trade Prevention Mode: NONE, CANCEL_TAKER, CANCEL_MAKER, CANCEL_BOTH, CANCEL_NEWEST, CANCEL_OLDEST';

-- Create index for STP mode queries
CREATE INDEX idx_exchange_order_stp_mode ON exchange_order(stp_mode);

-- Create index for member_id + stp_mode combination queries
CREATE INDEX idx_exchange_order_member_stp ON exchange_order(member_id, stp_mode);

-- Update existing orders with default STP mode (if needed)
UPDATE exchange_order 
SET stp_mode = 'CANCEL_TAKER' 
WHERE stp_mode IS NULL;

-- Add constraint to ensure valid STP mode values
ALTER TABLE exchange_order 
ADD CONSTRAINT chk_stp_mode 
CHECK (stp_mode IN ('NONE', 'CA<PERSON>EL_TAKER', 'CANCEL_MAKER', 'CA<PERSON>EL_BOTH', 'CA<PERSON>EL_NEWEST', 'CANCEL_OLDEST'));

-- Create STP statistics table for monitoring
CREATE TABLE IF NOT EXISTS stp_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    date_time DATETIME NOT NULL,
    symbol VARCHAR(50),
    member_id BIGINT,
    stp_mode VARCHAR(20),
    total_checks BIGINT DEFAULT 0,
    self_trades_detected BIGINT DEFAULT 0,
    self_trades_prevented BIGINT DEFAULT 0,
    prevention_rate DECIMAL(5,2) DEFAULT 0.00,
    avg_processing_time_us DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_stp_stats_datetime (date_time),
    INDEX idx_stp_stats_symbol (symbol),
    INDEX idx_stp_stats_member (member_id),
    INDEX idx_stp_stats_mode (stp_mode)
) COMMENT='Self Trade Prevention Statistics';

-- Create STP events table for audit trail
CREATE TABLE IF NOT EXISTS stp_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    taker_order_id VARCHAR(50) NOT NULL,
    maker_order_id VARCHAR(50) NOT NULL,
    member_id BIGINT NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    stp_mode VARCHAR(20) NOT NULL,
    action_taken VARCHAR(20) NOT NULL,
    reason TEXT,
    taker_amount DECIMAL(26,16),
    maker_amount DECIMAL(26,16),
    taker_price DECIMAL(18,8),
    maker_price DECIMAL(18,8),
    
    INDEX idx_stp_events_time (event_time),
    INDEX idx_stp_events_member (member_id),
    INDEX idx_stp_events_symbol (symbol),
    INDEX idx_stp_events_taker_order (taker_order_id),
    INDEX idx_stp_events_maker_order (maker_order_id)
) COMMENT='Self Trade Prevention Events Audit Trail';

-- Insert sample STP configuration data
INSERT INTO system_config (config_key, config_value, description, created_at) VALUES
('stp.default.mode', 'CANCEL_TAKER', 'Default Self Trade Prevention mode for new orders', NOW()),
('stp.statistics.enabled', 'true', 'Enable STP statistics collection', NOW()),
('stp.statistics.interval', '60000', 'STP statistics collection interval in milliseconds', NOW()),
('stp.audit.enabled', 'true', 'Enable STP events audit trail', NOW()),
('stp.audit.retention.days', '30', 'STP events retention period in days', NOW())
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = NOW();

-- Create stored procedure for STP statistics cleanup
DELIMITER //
CREATE PROCEDURE CleanupStpStatistics(IN retention_days INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Delete old STP statistics
    DELETE FROM stp_statistics 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- Delete old STP events
    DELETE FROM stp_events 
    WHERE event_time < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    COMMIT;
    
    SELECT ROW_COUNT() as deleted_rows;
END //
DELIMITER ;

-- Create event to automatically cleanup old STP data (runs daily)
CREATE EVENT IF NOT EXISTS evt_cleanup_stp_data
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  CALL CleanupStpStatistics(30);

-- Verify the migration
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'exchange_order' 
    AND COLUMN_NAME = 'stp_mode';

-- Show indexes created
SHOW INDEX FROM exchange_order WHERE Key_name LIKE '%stp%';

-- Show STP-related tables
SHOW TABLES LIKE '%stp%';

-- Migration completed successfully
SELECT 'STP Migration completed successfully' as status;
