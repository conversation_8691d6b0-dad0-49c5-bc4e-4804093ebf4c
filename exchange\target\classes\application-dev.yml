spring:

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${SPOT_DATASOURCE_URL:*****************************************************************}
    username: ${SPOT_DATASOURCE_USERNAME:spot_user}
    password: ${SPOT_DATASOURCE_PASSWORD:d2mwpsfU4fhSDvysiC60kwIXL3Zjojf6I9DlZJE1FUxYdGMgFPXOYZl0p6LidZnA}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    show-sql: ${SHOW_SQL:true}
    database: postgresql

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:*******************************************}
      database: ${SPRING_MONGODB_DATABASE:spot}
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:30679}
      #      password: ${REDIS_PASSWORD:2m0881Xc30Wh}
      connect-timeout: ${REDIS_TIMEOUT:30000}
      jedis:
        pool:
          min-idle: 20
          max-idle: 100
          max-wait: 60000
          max-active: 300

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:30092}
    consumer:
      group.id: ${KAFKA_CONSUMER_GROUP_ID:default-group}
    properties:
      sasl.mechanism: PLAIN

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082/realms/cex-lotus}
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082/realms/cex-lotus}
            token-uri: ${KEYCLOAK_TOKEN_URI:http://************:32082/realms/cex-lotus/protocol/openid-connect/token}

topic-kafka:
  exchange:
    order: ${EXCHANGE_ORDER:exchange-order}
    order-cancel-success: ${EXCHANGE_ORDER_CANCEL_SUCCESS:exchange-order-cancel-success}
    order-completed: ${EXCHANGE_ORDER_COMPLETED:exchange-order-completed}
    order-cancel: ${EXCHANGE_ORDER_CANCEL:exchange-order-cancel}
    trade: ${EXCHANGE_TRADE:exchange-trade}
    trade-plate: ${EXCHANGE_TRADE_PLATE:exchange-trade-plate}

keycloak:
  auth-server-url: ${KEYCLOAK_AUTH_SERVER_URL:http://************:32082}
  realm: cex-lotus
  resource: cex-exchange
  credentials:
    secret: Okc6TpmuPBeySygoiUXTtsRKzdvC1sg0

cex-security:
  resource-server-enabled: true
  default-principal-name: "internal-service"
  permit-all-endpoints:
    - "/**"

exchange:
  trader:
    use-v2: true
