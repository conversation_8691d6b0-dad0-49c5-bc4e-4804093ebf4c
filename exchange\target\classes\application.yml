server:
  port: ${SERVER_PORT:6005}
  servlet:
    context-path: ${SERVER_CONTEXT_PATH:/exchange}

spring:
  application:
    name: exchange
  session:
    store-type: ${SPRING_SESSION_STORE_TYPE:none}
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 2
      maximum-pool-size: 2
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      auto-commit: true
      leak-detection-threshold: 2000
    #    filters: stat,wall,log4j
    tomcat:
      initial-size: 5
      min-idle: 5
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
    dbcp2:
      pool-prepared-statements: true

  redis:
    pool:
      max-active: 300
      max-wait: 60000
      max-idle: 100
      min-idle: 20

  kafka:
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:0}
      batch:
        size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      linger: ${KAFKA_PRODUCER_LINGER:1}
      buffer:
        memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
    consumer:
      enable.auto.commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
      session.timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto.commit.interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
      auto.offset.reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      concurrency: 9
      maxPollRecordsConfig: 50

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:erlzeziorjuccpvx}
  data:
    jpa:
      repositories:
        enabled: true
    redis:
      timeout: 30000
  devtools:
    livereload:
      enabled: true
  cloud:
    consul:
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: ${server.servlet.context-path:/exchange}/actuator/health
        health-check-interval: 10s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.value}
#        fail-fast: true
#        heartbeat:
#          enabled: true
    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent

management:
  health:
    elasticsearch:
      enabled: false
    mail:
      enabled: false
  security:
    enabled: false
  context-path: /actuator


endpoints:
  health:
    sensitive: false
    enabled: true
  info:
    sensitive: false
  metrics:
    sensitive: false

spark:
  system:
    work-id: 1
    data-center-id: 1
    host: "@spark.system.host@"
    name: "@spark.system.name@"
    admins: "@spark.system.admins@"
    admin-phones: "@spark.system.admin-phones@"

aliyun:
  mail-sms:
    region: "@aliyun.mail-sms.region@"
    access-key-id: "@aliyun.mail-sms.access-key-id@"
    access-secret: "@aliyun.mail-sms.access-secret@"
    from-address: "@aliyun.mail-sms.from-address@"
    from-alias: "@aliyun.mail-sms.from-alias@"
    sms-sign: "@aliyun.mail-sms.sms-sign@"
    sms-template: "@aliyun.mail-sms.sms-template@"

es:
  username: ""
  password: ""
  mine:
    index: ""
    type: ""
  public:
    ip: ""
  private:
    ip: ""
  port: 9200



