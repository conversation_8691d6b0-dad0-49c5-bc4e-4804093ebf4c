-- T<PERSON><PERSON><PERSON> dữ liệu mẫu cho bảng contract_order_detail
INSERT INTO contract_order_detail (id, order_id, price, amount, turnover, fee, time, contract_id, leverage, margin_mode)
VALUES
('DETAIL123456789', 'ORD123456789', 50000.00000000, 1.00000000, 50000.00000000, 20.00000000, NOW() - INTERVAL '55 MINUTE', 1, 10.00000000, 'CROSSED'),
('DETAIL987654321', 'ORD987654321', 50000.00000000, 1.00000000, 50000.00000000, 20.00000000, NOW() - INTERVAL '55 MINUTE', 1, 5.00000000, 'CROSSED'),
('DETAIL123789456', 'ORD123789456', 3000.00000000, 5.00000000, 15000.00000000, 6.00000000, NOW() - INTERVAL '1 HOUR 55 MINUTE', 2, 20.00000000, 'CROSSED'),
('DETAIL456123789', 'ORD456123789', 3000.00000000, 5.00000000, 15000.00000000, 6.00000000, NOW() - INTERVAL '1 HOUR 55 MINUTE', 2, 20.00000000, 'CROSSED'),
('DETAIL789456123', 'ORD789456123', 400.00000000, 10.00000000, 4000.00000000, 1.60000000, NOW() - INTERVAL '30 MINUTE', 4, 10.00000000, 'CROSSED'),
('DETAIL159753456', 'ORD159753456', 400.00000000, 10.00000000, 4000.00000000, 1.60000000, NOW() - INTERVAL '30 MINUTE', 4, 10.00000000, 'CROSSED'),
('DETAIL202305010001', 'ORD202305010001', 0.12000000, 10000.00000000, 1200.00000000, 0.48000000, NOW() - INTERVAL '2 HOUR 55 MINUTE', 6, 10.00000000, 'CROSSED'),
('DETAIL202305010002', 'ORD202305010002', 0.12000000, 10000.00000000, 1200.00000000, 0.48000000, NOW() - INTERVAL '2 HOUR 55 MINUTE', 6, 10.00000000, 'CROSSED'),
('DETAIL202305010003', 'ORD202305010003', 0.50000000, 1000.00000000, 500.00000000, 0.20000000, NOW() - INTERVAL '3 HOUR 55 MINUTE', 7, 15.00000000, 'CROSSED'),
('DETAIL202305010004', 'ORD202305010004', 0.50000000, 1000.00000000, 500.00000000, 0.20000000, NOW() - INTERVAL '3 HOUR 55 MINUTE', 7, 15.00000000, 'CROSSED'),
('DETAIL202305010005', 'ORD202305010005', 15.00000000, 100.00000000, 1500.00000000, 0.60000000, NOW() - INTERVAL '2 HOUR', 8, 10.00000000, 'CROSSED'),
('DETAIL202305010006', 'ORD202305010006', 15.00000000, 100.00000000, 1500.00000000, 0.60000000, NOW() - INTERVAL '2 HOUR', 8, 10.00000000, 'CROSSED');

-- Thêm dữ liệu mẫu cho bảng contract_funding_payment
INSERT INTO contract_funding_payment (position_id, member_id, symbol, amount, rate, time)
VALUES
(1, 1, 'BTCUSDT', 5.00000000, 0.00010000, NOW() - INTERVAL '4 HOUR'),
(2, 2, 'BTCUSDT', -5.00000000, 0.00010000, NOW() - INTERVAL '4 HOUR'),
(3, 1, 'ETHUSDT', 2.25000000, 0.00015000, NOW() - INTERVAL '4 HOUR'),
(4, 3, 'ETHUSDT', -2.25000000, 0.00015000, NOW() - INTERVAL '4 HOUR'),
(5, 4, 'BNBUSDT', 0.48000000, 0.00012000, NOW() - INTERVAL '4 HOUR'),
(6, 5, 'BNBUSDT', -0.48000000, 0.00012000, NOW() - INTERVAL '4 HOUR'),
(7, 6, 'DOGEUSDT', 0.30000000, 0.00025000, NOW() - INTERVAL '4 HOUR'),
(8, 7, 'DOGEUSDT', -0.30000000, 0.00025000, NOW() - INTERVAL '4 HOUR'),
(9, 8, 'XRPUSDT', 0.11000000, 0.00022000, NOW() - INTERVAL '4 HOUR'),
(10, 9, 'XRPUSDT', -0.11000000, 0.00022000, NOW() - INTERVAL '4 HOUR'),
(11, 10, 'DOTUSDT', 0.27000000, 0.00018000, NOW() - INTERVAL '4 HOUR'),
(12, 1, 'DOTUSDT', -0.27000000, 0.00018000, NOW() - INTERVAL '4 HOUR');

-- Thêm dữ liệu mẫu cho bảng funding_settlement
INSERT INTO funding_settlement (symbol, member_id, position_id, funding_rate, funding_amount, timestamp)
VALUES
('BTCUSDT', 1, 1, 0.00010000, 5.00000000, NOW() - INTERVAL '4 HOUR'),
('BTCUSDT', 2, 2, 0.00010000, -5.00000000, NOW() - INTERVAL '4 HOUR'),
('ETHUSDT', 1, 3, 0.00015000, 2.25000000, NOW() - INTERVAL '4 HOUR'),
('ETHUSDT', 3, 4, 0.00015000, -2.25000000, NOW() - INTERVAL '4 HOUR'),
('BNBUSDT', 4, 5, 0.00012000, 0.48000000, NOW() - INTERVAL '4 HOUR'),
('BNBUSDT', 5, 6, 0.00012000, -0.48000000, NOW() - INTERVAL '4 HOUR'),
('DOGEUSDT', 6, 7, 0.00025000, 0.30000000, NOW() - INTERVAL '4 HOUR'),
('DOGEUSDT', 7, 8, 0.00025000, -0.30000000, NOW() - INTERVAL '4 HOUR'),
('XRPUSDT', 8, 9, 0.00022000, 0.11000000, NOW() - INTERVAL '4 HOUR'),
('XRPUSDT', 9, 10, 0.00022000, -0.11000000, NOW() - INTERVAL '4 HOUR'),
('DOTUSDT', 10, 11, 0.00018000, 0.27000000, NOW() - INTERVAL '4 HOUR'),
('DOTUSDT', 1, 12, 0.00018000, -0.27000000, NOW() - INTERVAL '4 HOUR');

-- Thêm dữ liệu mẫu cho bảng contract_settlement
INSERT INTO contract_settlement (contract_id, symbol, member_id, position_id, settlement_price, pnl, create_time)
VALUES
(1, 'BTCUSDT', 1, 1, 50500.00000000, 500.00000000, NOW() - INTERVAL '12 HOUR'),
(1, 'BTCUSDT', 2, 2, 50500.00000000, -500.00000000, NOW() - INTERVAL '12 HOUR'),
(2, 'ETHUSDT', 1, 3, 3050.00000000, 250.00000000, NOW() - INTERVAL '12 HOUR'),
(2, 'ETHUSDT', 3, 4, 3050.00000000, -250.00000000, NOW() - INTERVAL '12 HOUR'),
(4, 'BNBUSDT', 4, 5, 405.00000000, 50.00000000, NOW() - INTERVAL '12 HOUR'),
(4, 'BNBUSDT', 5, 6, 405.00000000, -50.00000000, NOW() - INTERVAL '12 HOUR'),
(6, 'DOGEUSDT', 6, 7, 0.12100000, 100.00000000, NOW() - INTERVAL '12 HOUR'),
(6, 'DOGEUSDT', 7, 8, 0.12100000, -100.00000000, NOW() - INTERVAL '12 HOUR'),
(7, 'XRPUSDT', 8, 9, 0.50500000, 50.00000000, NOW() - INTERVAL '12 HOUR'),
(7, 'XRPUSDT', 9, 10, 0.50500000, -50.00000000, NOW() - INTERVAL '12 HOUR'),
(8, 'DOTUSDT', 10, 11, 15.20000000, 20.00000000, NOW() - INTERVAL '12 HOUR'),
(8, 'DOTUSDT', 1, 12, 15.20000000, -20.00000000, NOW() - INTERVAL '12 HOUR');

-- Thêm dữ liệu mẫu cho bảng contract_liquidation
INSERT INTO contract_liquidation (contract_id, symbol, member_id, position_id, liquidation_order_id, liquidation_price, liquidation_volume, realized_pnl, insurance_amount, type, create_time)
VALUES
(3, 'SOLUSDT', 11, 13, 'LIQ202305010001', 90.00000000, 50.00000000, -500.00000000, 100.00000000, 'PARTIAL', NOW() - INTERVAL '5 HOUR'),
(5, 'ADAUSDT', 12, 14, 'LIQ202305010002', 1.10000000, 1000.00000000, -200.00000000, 50.00000000, 'FULL', NOW() - INTERVAL '6 HOUR'),
(9, 'AVAXUSDT', 13, 15, 'LIQ202305010003', 22.00000000, 20.00000000, -300.00000000, 75.00000000, 'FULL', NOW() - INTERVAL '7 HOUR'),
(10, 'MATICUSDT', 14, 16, 'LIQ202305010004', 0.75000000, 500.00000000, -150.00000000, 30.00000000, 'PARTIAL', NOW() - INTERVAL '8 HOUR');

-- Thêm dữ liệu mẫu cho bảng contract_adl_record
INSERT INTO contract_adl_record (position_id, member_id, symbol, direction, volume, price, time)
VALUES
(17, 15, 'BTCUSDT', 'LONG', 0.50000000, 49000.00000000, NOW() - INTERVAL '9 HOUR'),
(18, 16, 'ETHUSDT', 'SHORT', 2.50000000, 2950.00000000, NOW() - INTERVAL '10 HOUR'),
(19, 17, 'BNBUSDT', 'LONG', 5.00000000, 395.00000000, NOW() - INTERVAL '11 HOUR'),
(20, 18, 'DOGEUSDT', 'SHORT', 5000.00000000, 0.11800000, NOW() - INTERVAL '12 HOUR');

-- Thêm dữ liệu mẫu cho bảng contract_clawback_position
INSERT INTO contract_clawback_position (contract_id, symbol, member_id, position_id, unrealized_pnl, clawback_amount, clawback_rate, create_time)
VALUES
(1, 'BTCUSDT', 19, 21, 1000.00000000, 100.00000000, 0.10000000, NOW() - INTERVAL '13 HOUR'),
(2, 'ETHUSDT', 20, 22, 500.00000000, 50.00000000, 0.10000000, NOW() - INTERVAL '14 HOUR'),
(4, 'BNBUSDT', 21, 23, 200.00000000, 20.00000000, 0.10000000, NOW() - INTERVAL '15 HOUR'),
(6, 'DOGEUSDT', 22, 24, 100.00000000, 10.00000000, 0.10000000, NOW() - INTERVAL '16 HOUR');

-- Thêm dữ liệu mẫu cho bảng contract_circuit_breaker
INSERT INTO contract_circuit_breaker (symbol, status, reason, trigger_price, create_time, update_time)
VALUES
('BTCUSDT', 'TRIGGERED', 'Price dropped more than 10% in 5 minutes', 45000.00000000, NOW() - INTERVAL '17 HOUR', NOW() - INTERVAL '16 HOUR 30 MINUTE'),
('BTCUSDT', 'RESOLVED', 'Market stabilized', 47000.00000000, NOW() - INTERVAL '16 HOUR 30 MINUTE', NOW() - INTERVAL '16 HOUR'),
('ETHUSDT', 'TRIGGERED', 'Price increased more than 10% in 5 minutes', 3300.00000000, NOW() - INTERVAL '18 HOUR', NOW() - INTERVAL '17 HOUR 30 MINUTE'),
('ETHUSDT', 'RESOLVED', 'Market stabilized', 3200.00000000, NOW() - INTERVAL '17 HOUR 30 MINUTE', NOW() - INTERVAL '17 HOUR'),
('SOLUSDT', 'TRIGGERED', 'Abnormal trading volume detected', 105.00000000, NOW() - INTERVAL '19 HOUR', NOW() - INTERVAL '18 HOUR 30 MINUTE'),
('SOLUSDT', 'RESOLVED', 'Trading volume returned to normal', 102.00000000, NOW() - INTERVAL '18 HOUR 30 MINUTE', NOW() - INTERVAL '18 HOUR');
