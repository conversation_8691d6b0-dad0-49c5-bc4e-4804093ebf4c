spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${SPOT_DATASOURCE_URL:*****************************************************************}
    username: ${SPOT_DATASOURCE_USERNAME:spot_user}
    password: ${SPOT_DATASOURCE_PASSWORD:d2mwpsfU4fhSDvysiC60kwIXL3Zjojf6I9DlZJE1FUxYdGMgFPXOYZl0p6LidZnA}

  jpa:
    properties:
      hibernate:
        default_schema: public
    show-sql: true
    database: postgresql

  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}
      #      password: ${REDIS_PASSWORD:2m0881Xc30Wh}
      database: ${REDIS_DATABASE:0}
      timeout: ${REDIS_TIMEOUT:5000}
    mongodb:
      uri: ${SPRING_MONGODB_URI:*******************************************}
      database: ${SPOT_MONGODB_DATABASE:spot}

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:9092}
    properties:
      sasl.mechanism: PLAIN

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}