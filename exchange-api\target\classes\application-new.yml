spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${SPOT_DATASOURCE_URL:*****************************************************************}
    username: ${SPOT_DATASOURCE_USERNAME:spot_user}
    password: ${SPOT_DATASOURCE_PASSWORD:d2mwpsfU4fhSDvysiC60kwIXL3Zjojf6I9DlZJE1FUxYdGMgFPXOYZl0p6LidZnA}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2

  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    hibernate:
      ddl-auto: none
    show-sql: ${SHOW_SQL:true}
    database: postgresql


  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:30092}
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:exchange-api-dev-group}
    properties:
      sasl.mechanism: PLAIN

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082/realms/cex-lotus}
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082/realms/cex-lotus}
            token-uri: ${KEYCLOAK_TOKEN_URI:http://************:32082/realms/cex-lotus/protocol/openid-connect/token}


  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:30679}
#      password: ${REDIS_PASSWORD:2m0881Xc30Wh}
    mongodb:
      uri: ${SPRING_MONGODB_URI:*******************************************}
      database: ${SPOT_MONGODB_DATABASE:spot}

keycloak:
  auth-server-url: ${KEYCLOAK_AUTH_SERVER_URL:http://************:32082}
  realm: cex-lotus
  resource: cex-exchange-api
  credentials:
    secret: UTj5XuHBZiLHJSZYUd7IJ2uOpyL4IG8S

cex-security:
  permit-all-endpoints:
    - "/**"
  resource-server-enabled: true

topic-kafka:
  exchange:
    order: ${EXCHANGE_ORDER:dev-exchange-order}
    order-cancel-success: ${EXCHANGE_ORDER_CANCEL_SUCCESS:exchange-order-cancel-success}
    order-completed: ${EXCHANGE_ORDER_COMPLETED:exchange-order-completed}
    order-cancel: ${EXCHANGE_ORDER_CANCEL:dev-exchange-order-cancel}
    trade: ${EXCHANGE_TRADE:exchange-trade}
    trade-plate: ${EXCHANGE_TRADE_PLATE:exchange-trade-plate}
