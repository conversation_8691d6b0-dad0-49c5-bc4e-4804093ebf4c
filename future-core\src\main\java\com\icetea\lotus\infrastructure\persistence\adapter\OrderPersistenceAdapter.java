package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.OrderPersistencePort;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.OrderDirection;
import com.icetea.lotus.core.domain.entity.OrderStatus;
import com.icetea.lotus.core.domain.entity.OrderType;
import com.icetea.lotus.core.domain.repository.OrderRepository;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.api.request.FindOrderRequest;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.OrderJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.OrderPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.OrderJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Primary;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Adapter cho OrderPersistencePort
 * Triển khai các phương thức của OrderPersistencePort
 */
@Slf4j
@Component("orderPersistenceAdapter")
@Primary
@RequiredArgsConstructor
@Transactional
public class OrderPersistenceAdapter implements OrderPersistencePort, OrderRepository {

    private final OrderJpaRepository orderJpaRepository;
    private final OrderPersistenceMapper orderPersistenceMapper;
    private final ApplicationContext applicationContext;

    /**
     * Tìm tất cả các lệnh
     *
     * @return Danh sách các lệnh
     */
    @Override
    public List<Order> findAll() {
        log.info("Tìm tất cả các lệnh");

        try {
            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findAll().stream()
                    .limit(1000)
                    .toList();

            return entities.stream()
                    .map(orderPersistenceMapper::entityToDomain)
                    .toList();
        } catch (Exception e) {
            log.error("Tìm tất cả các lệnh thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm lệnh theo orderId với xử lý ngoại lệ và thử lại
     *
     * @param orderId ID của lệnh
     * @return Optional chứa lệnh nếu tìm thấy
     */
    @Override
    @Cacheable(value = "orders", key = "#orderId != null ? #orderId.value : ''", unless = "#result == null")
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<Order> findByOrderId(OrderId orderId) {
        try {
            log.info("OrderPersistenceAdapter.findByOrderId được gọi, orderId = {}", orderId != null ? orderId.getValue() : "null");

            if (orderId == null) {
                throw new IllegalArgumentException("OrderId không được để trống");
            }

            // Không xóa cache ở đây vì sẽ làm @Cacheable không hoạt động
            // Chỉ xóa cache khi cần thiết, ví dụ: khi có lỗi hoặc khi cập nhật dữ liệu

            log.info("Tìm lệnh trong database với orderId = {}", orderId.getValue());
            Optional<OrderJpaEntity> entity = orderJpaRepository.findById(orderId.getValue());

            if (entity.isPresent()) {
                log.info("Đã tìm thấy lệnh trong database, orderId = {}, entity = {}", orderId.getValue(), entity.get());

                try {
                    // Chuyển đổi entity sang domain
                    log.info("Bắt đầu chuyển đổi entity sang domain, orderId = {}", orderId.getValue());
                    Order order = orderPersistenceMapper.entityToDomain(entity.get());

                    if (order != null) {
                        log.info("Chuyển đổi entity sang domain thành công, orderId = {}, order = {}", orderId.getValue(), order);
                        return Optional.of(order);
                    } else {
                        log.error("Lỗi khi chuyển đổi entity sang domain, kết quả là null, orderId = {}, entity = {}", orderId.getValue(), entity.get());
                        // Xóa cache khi có lỗi
                        clearOrderCache(orderId);
                        return Optional.empty();
                    }
                } catch (Exception e) {
                    log.error("Lỗi khi chuyển đổi entity sang domain, orderId = {}, entity = {}", orderId.getValue(), entity.get(), e);
                    // Xóa cache khi có lỗi
                    clearOrderCache(orderId);
                    return Optional.empty();
                }
            } else {
                log.info("Không tìm thấy lệnh trong database, orderId = {}", orderId.getValue());
                return Optional.empty();
            }
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm lệnh theo orderId, orderId = {}", orderId != null ? orderId.getValue() : "null", e);
            // Xóa cache khi có lỗi
            if (orderId != null) {
                clearOrderCache(orderId);
            }
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo orderId, orderId = {}", orderId != null ? orderId.getValue() : "null", e);
            // Xóa cache khi có lỗi
            if (orderId != null) {
                clearOrderCache(orderId);
            }
            throw new DatabaseException("Lỗi khi tìm lệnh theo orderId: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm lệnh theo orderId thất bại
     *
     * @param e       Ngoại lệ
     * @param orderId ID của lệnh
     * @return Optional<Order>
     */
    @Recover
    public Optional<Order> recoverFindByOrderId(Exception e, OrderId orderId) {
        log.error("Đã thử lại tìm lệnh theo orderId 3 lần nhưng thất bại, orderId = {}",
                orderId != null ? orderId.getValue() : "null", e);

        // Xóa cache nếu có lỗi
        try {
            CacheManager cacheManager = applicationContext.getBean(CacheManager.class);
            Cache ordersCache = cacheManager.getCache("orders");
            if (ordersCache != null && orderId != null) {
                log.info("Xóa cache cho orderId = {}", orderId.getValue());
                ordersCache.evict(orderId.getValue());
            }
        } catch (Exception ex) {
            log.error("Lỗi khi xóa cache, orderId = {}", orderId != null ? orderId.getValue() : "null", ex);
        }

        return Optional.empty();
    }

    /**
     * Tìm các lệnh theo memberId
     *
     * @param memberId ID của thành viên
     * @return Danh sách các lệnh
     */
    @Override
    @Cacheable(value = "ordersByMemberId", key = "#memberId")
    public List<Order> findByMemberId(Long memberId) {
        log.info("Tìm các lệnh theo memberId, memberId = {}", memberId);

        try {
            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findAllByMemberId(memberId).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo memberId thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo memberId với phân trang
     *
     * @param memberId    ID của thành viên
     * @param pageRequest Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    @Override
    public com.icetea.lotus.core.domain.valueobject.Page<Order> findByMemberId(Long memberId, com.icetea.lotus.core.domain.valueobject.PageRequest pageRequest) {
        log.info("Tìm các lệnh theo memberId với phân trang, memberId = {}, pageRequest = {}", memberId, pageRequest);

        try {
            Pageable pageable = org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize());
            org.springframework.data.domain.Page<OrderJpaEntity> page = orderJpaRepository.findAllByMemberId(memberId, pageable);

            List<Order> content = mapToDomainEntities(page.getContent());

            return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, page.getTotalElements());
        } catch (Exception e) {
            log.error("Tìm các lệnh theo memberId với phân trang thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo memberId và symbol
     *
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @return Danh sách các lệnh
     */
    @Override
    @Cacheable(value = "ordersBySymbol", key = "#memberId + '_' + (#symbol != null ? #symbol.value : '')")
    public List<Order> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        log.info("Tìm các lệnh theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);

        try {
            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findAllByMemberIdAndSymbol(memberId, symbol.getValue()).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo memberId và symbol thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo memberId và symbol với phân trang
     *
     * @param memberId    ID của thành viên
     * @param symbol      Symbol của hợp đồng
     * @param pageRequest Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    @Override
    public com.icetea.lotus.core.domain.valueobject.Page<Order> findByMemberIdAndSymbol(Long memberId, Symbol symbol, com.icetea.lotus.core.domain.valueobject.PageRequest pageRequest) {
        log.info("Tìm các lệnh theo memberId và symbol với phân trang, memberId = {}, symbol = {}, pageRequest = {}", memberId, symbol, pageRequest);

        try {
            Pageable pageable = org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize());
            org.springframework.data.domain.Page<OrderJpaEntity> page = orderJpaRepository.findAllByMemberIdAndSymbol(memberId, symbol.getValue(), pageable);

            List<Order> content = mapToDomainEntities(page.getContent());

            return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, page.getTotalElements());
        } catch (Exception e) {
            log.error("Tìm các lệnh theo memberId và symbol với phân trang thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo status
     *
     * @param status Trạng thái lệnh
     * @return Danh sách các lệnh
     */
    @Override
    @Cacheable(value = "ordersByStatus", key = "#status")
    public List<Order> findByStatus(OrderStatus status) {
        log.info("Tìm các lệnh theo status, status = {}", status);

        try {
            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findAllByStatus(status.name()).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo status thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo status với phân trang
     *
     * @param status      Trạng thái lệnh
     * @param pageRequest Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    @Override
    public com.icetea.lotus.core.domain.valueobject.Page<Order> findByStatus(OrderStatus status, com.icetea.lotus.core.domain.valueobject.PageRequest pageRequest) {
        log.info("Tìm các lệnh theo status với phân trang, status = {}, pageRequest = {}", status, pageRequest);

        try {
            Pageable pageable = org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize());
            org.springframework.data.domain.Page<OrderJpaEntity> page = orderJpaRepository.findAllByStatus(status.name(), pageable);

            List<Order> content = mapToDomainEntities(page.getContent());

            return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, page.getTotalElements());
        } catch (Exception e) {
            log.error("Tìm các lệnh theo status với phân trang thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo status và thời gian hết hạn
     *
     * @param status     Trạng thái lệnh
     * @param expireTime Thời gian hết hạn
     * @return Danh sách các lệnh
     */
    @Override
    public List<Order> findByStatusAndExpireTimeBefore(OrderStatus status, LocalDateTime expireTime) {
        log.info("Tìm các lệnh theo status và thời gian hết hạn, status = {}, expireTime = {}", status, expireTime);
        try {
            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findByStatusAndExpireTimeBefore(status, expireTime).stream()
                    .limit(1000)
                    .toList();
            log.info("findByStatusAndExpireTimeBefore entities = {}", entities.toString());
            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo status và thời gian hết hạn thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo status và thời gian thực hiện
     *
     * @param status      Trạng thái lệnh
     * @param executeTime Thời gian thực hiện
     * @return Danh sách các lệnh
     */
    @Override
    public List<Order> findByStatusAndExecuteTimeBefore(OrderStatus status, LocalDateTime executeTime) {
        log.info("Tìm các lệnh theo status và thời gian thực hiện, status = {}, executeTime = {}", status, executeTime);

        try {
            // Sử dụng phương thức findByStatusAndExecuteTimeBefore trong OrderJpaRepository
            List<OrderJpaEntity> entities = orderJpaRepository.findByStatusAndExecuteTimeBefore(status, executeTime).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo status và thời gian thực hiện thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo status và thời gian hết hạn với phân trang
     *
     * @param status      Trạng thái lệnh
     * @param expireTime  Thời gian hết hạn
     * @param pageRequest Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    @Override
    public com.icetea.lotus.core.domain.valueobject.Page<Order> findByStatusAndExpireTimeBefore(OrderStatus status, LocalDateTime expireTime, com.icetea.lotus.core.domain.valueobject.PageRequest pageRequest) {
        log.info("Tìm các lệnh theo status và thời gian hết hạn với phân trang, status = {}, expireTime = {}, pageRequest = {}", status, expireTime, pageRequest);

        try {
            Pageable pageable = org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize());
            org.springframework.data.domain.Page<OrderJpaEntity> page = orderJpaRepository.findByStatusAndExpireTimeBefore(status, expireTime, pageable);

            List<Order> content = mapToDomainEntities(page.getContent());

            return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, page.getTotalElements());
        } catch (Exception e) {
            log.error("Tìm các lệnh theo status và thời gian hết hạn với phân trang thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo memberId, symbol và danh sách status
     *
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @param statuses Danh sách trạng thái lệnh
     * @return Danh sách các lệnh
     */
    @Override
    public List<Order> findByMemberIdAndSymbolAndStatusIn(Long memberId, Symbol symbol, List<OrderStatus> statuses) {
        log.info("Tìm các lệnh theo memberId, symbol và danh sách status, memberId = {}, symbol = {}, statuses = {}", memberId, symbol, statuses);

        try {
            // Giới hạn số lượng kết quả trả về
            List<String> statusNames = statuses.stream().map(OrderStatus::name).toList();
            List<OrderJpaEntity> entities = orderJpaRepository.findAllByMemberIdAndSymbolAndStatusIn(memberId, symbol.getValue(), statusNames).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo memberId, symbol và danh sách status thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo memberId, symbol và danh sách status với phân trang
     *
     * @param memberId    ID của thành viên
     * @param symbol      Symbol của hợp đồng
     * @param statuses    Danh sách trạng thái lệnh
     * @param pageRequest Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    @Override
    public com.icetea.lotus.core.domain.valueobject.Page<Order> findByMemberIdAndSymbolAndStatusIn(Long memberId, Symbol symbol, List<OrderStatus> statuses, com.icetea.lotus.core.domain.valueobject.PageRequest pageRequest) {
        log.info("Tìm các lệnh theo memberId, symbol và danh sách status với phân trang, memberId = {}, symbol = {}, statuses = {}, pageRequest = {}", memberId, symbol, statuses, pageRequest);

        try {
            Pageable pageable = org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize());
            List<String> statusNames = statuses.stream().map(OrderStatus::name).toList();
            org.springframework.data.domain.Page<OrderJpaEntity> page = orderJpaRepository.findAllByMemberIdAndSymbolAndStatusIn(memberId, symbol.getValue(), statusNames, pageable);

            List<Order> content = mapToDomainEntities(page.getContent());

            return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, page.getTotalElements());
        } catch (Exception e) {
            log.error("Tìm các lệnh theo memberId, symbol và danh sách status với phân trang thất bại", e);
            throw e;
        }
    }

    /**
     * Lưu lệnh với xử lý ngoại lệ và thử lại
     *
     * @param order Lệnh cần lưu
     * @return Lệnh đã được lưu
     */
    @Override
//    @Caching(
//        put = {
//            @CachePut(value = "orders", key = "#result.id != null ? #result.id.value : ''")
//        },
//        evict = {
//            @CacheEvict(value = "ordersByMemberId", key = "#order.memberId"),
//            @CacheEvict(value = "ordersBySymbol", key = "#order.memberId + '_' + (#order.symbol != null ? #order.symbol.value : '')"),
//            @CacheEvict(value = "ordersByStatus", key = "#order.status")
//        }
//    )
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Order save(Order order) {
        try {
            if (order == null) {
                throw new IllegalArgumentException("Order không được để trống");
            }

            log.debug("Lưu lệnh, id = {}, memberId = {}, symbol = {}, status = {}",
                    order.getId() != null ? order.getId() : "null",
                    order.getMemberId(),
                    order.getSymbol() != null ? order.getSymbol().getValue() : "null",
                    order.getStatus());

            OrderJpaEntity entity = orderPersistenceMapper.domainToEntity(order);
            OrderJpaEntity savedEntity = orderJpaRepository.save(entity);

            log.debug("Đã lưu lệnh thành công, id = {}", savedEntity.getOrderId());

            return orderPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataIntegrityViolationException e) {
            // Handle duplicate key violations gracefully
            if (e.getMessage() != null && e.getMessage().contains("uk_contract_order")) {
                log.warn("Duplicate order detected, orderId = {}: {}",
                    order.getOrderId() != null ? order.getOrderId().getValue() : "null", e.getMessage());

                // Try to find existing order
                if (order.getOrderId() != null) {
                    Optional<Order> existingOrder = findByOrderId(order.getOrderId());
                    if (existingOrder.isPresent()) {
                        log.info("Returning existing order for duplicate, orderId = {}", order.getOrderId().getValue());
                        return existingOrder.get();
                    }
                }
            }

            log.error("Data integrity violation when saving order, id = {}",
                    order.getId() != null ? order.getId() : "null", e);
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu lệnh, id = {}",
                    order.getId() != null ? order.getId() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu lệnh, id = {}",
                    order.getId() != null ? order.getId() : "null", e);
            throw new DatabaseException("Lỗi khi lưu lệnh: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu lệnh thất bại
     *
     * @param e     Ngoại lệ
     * @param order Lệnh cần lưu
     * @return Order
     */
    @Recover
    public Order recoverSave(Exception e, Order order) {
        log.error("Đã thử lại lưu lệnh 3 lần nhưng thất bại, id = {}",
                order.getId() != null ? order.getId() : "null", e);
        throw new DatabaseException("Không thể lưu lệnh sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Xóa lệnh
     *
     * @param order Lệnh cần xóa
     */
    @Override
    @Caching(
            evict = {
                    @CacheEvict(value = "orders", key = "#order.id != null ? #order.id.value : ''"),
                    @CacheEvict(value = "ordersByMemberId", key = "#order.memberId"),
                    @CacheEvict(value = "ordersBySymbol", key = "#order.memberId + '_' + (#order.symbol != null ? #order.symbol.value : '')"),
                    @CacheEvict(value = "ordersByStatus", key = "#order.status")
            }
    )
    public void delete(Order order) {
        log.info("Xóa lệnh, order = {}", order);

        try {
            OrderJpaEntity entity = orderPersistenceMapper.domainToEntity(order);
            orderJpaRepository.delete(entity);
        } catch (Exception e) {
            log.error("Xóa lệnh thất bại", e);
            throw e;
        }
    }

    /**
     * Xóa lệnh theo orderId
     *
     * @param orderId ID của lệnh
     */
    @Override
    @CacheEvict(value = "orders", key = "#orderId != null ? #orderId.value : ''")
    public void deleteByOrderId(OrderId orderId) {
        log.info("Xóa lệnh theo orderId, orderId = {}", orderId);

        try {
            // Tìm lệnh trước khi xóa để có thể xóa cache khác
            Optional<OrderJpaEntity> entityOpt = orderJpaRepository.findById(orderId.getValue());
            if (entityOpt.isPresent()) {
                Order order = orderPersistenceMapper.entityToDomain(entityOpt.get());
                // Xóa cache khác
                evictOrderCaches(order);
            }

            orderJpaRepository.deleteById(orderId.getValue());
        } catch (Exception e) {
            log.error("Xóa lệnh theo orderId thất bại", e);
            throw e;
        }
    }

    @Override
    public Page<OrderJpaEntity> findOrder(FindOrderRequest request, OrderDirection direction, Pageable pageable) {
        return orderJpaRepository.findBySymbolAndStatusInAndDirectionOrderByPriceDesc(Symbol.of(request.getSymbol()).getValue(), request.getStatus(),
                direction, pageable);
    }

    /**
     * Xóa lệnh theo ID
     *
     * @param id ID của lệnh
     */
    @Override
    @CacheEvict(value = "orders", key = "#id != null ? #id.value : ''")
    public void deleteById(OrderId id) {
        log.info("Xóa lệnh theo ID, id = {}", id);

        try {
            // Tìm lệnh trước khi xóa để có thể xóa cache khác
            Optional<OrderJpaEntity> entityOpt = orderJpaRepository.findById(id.getValue());
            if (entityOpt.isPresent()) {
                Order order = orderPersistenceMapper.entityToDomain(entityOpt.get());
                // Xóa cache khác
                evictOrderCaches(order);
            }

            orderJpaRepository.deleteById(id.getValue());
        } catch (Exception e) {
            log.error("Xóa lệnh theo ID thất bại", e);
            throw e;
        }
    }

    /**
     * Xóa cache của lệnh
     *
     * @param order Lệnh cần xóa cache
     */
    private void evictOrderCaches(Order order) {
        // Không cần xóa cache orders vì đã được xóa bởi annotation @CacheEvict
        // Xóa cache ordersByMemberId
        CacheManager cacheManager = applicationContext.getBean(CacheManager.class);
        Cache ordersByMemberIdCache = cacheManager.getCache("ordersByMemberId");
        if (ordersByMemberIdCache != null) {
            ordersByMemberIdCache.evict(order.getMemberId());
        }

        // Xóa cache ordersBySymbol
        Cache ordersBySymbolCache = cacheManager.getCache("ordersBySymbol");
        if (ordersBySymbolCache != null) {
            ordersBySymbolCache.evict(order.getMemberId() + "_" + order.getSymbol().getValue());
        }

        // Xóa cache ordersByStatus
        Cache ordersByStatusCache = cacheManager.getCache("ordersByStatus");
        if (ordersByStatusCache != null) {
            ordersByStatusCache.evict(order.getStatus());
        }
    }

    /**
     * Xóa cache cho lệnh theo orderId
     *
     * @param orderId ID của lệnh
     */
    public void clearOrderCache(OrderId orderId) {
        if (orderId == null) {
            return;
        }

        try {
            log.info("Xóa cache cho orderId = {}", orderId.getValue());
            CacheManager cacheManager = applicationContext.getBean(CacheManager.class);
            Cache ordersCache = cacheManager.getCache("orders");
            if (ordersCache != null) {
                ordersCache.evict(orderId.getValue());
                log.info("Đã xóa cache cho orderId = {}", orderId.getValue());
            } else {
                log.warn("Không tìm thấy cache 'orders'");
            }
        } catch (Exception e) {
            log.error("Lỗi khi xóa cache, orderId = {}", orderId.getValue(), e);
        }
    }

    /**
     * Xóa cache cho lệnh theo orderId và cập nhật lại cache
     *
     * @param orderId ID của lệnh
     * @return Optional<Order> Lệnh đã được cập nhật
     */
    public Optional<Order> refreshOrderCache(OrderId orderId) {
        if (orderId == null) {
            return Optional.empty();
        }

        try {
            log.info("Cập nhật cache cho orderId = {}", orderId.getValue());

            // Xóa cache hiện tại
            clearOrderCache(orderId);

            // Tìm lệnh trong database
            Optional<OrderJpaEntity> entity = orderJpaRepository.findById(orderId.getValue());

            if (entity.isPresent()) {
                try {
                    // Chuyển đổi entity sang domain
                    Order order = orderPersistenceMapper.entityToDomain(entity.get());

                    if (order != null) {
                        // Cập nhật cache
                        CacheManager cacheManager = applicationContext.getBean(CacheManager.class);
                        Cache ordersCache = cacheManager.getCache("orders");
                        if (ordersCache != null) {
                            ordersCache.put(orderId.getValue(), Optional.of(order));
                            log.info("Đã cập nhật cache cho orderId = {}", orderId.getValue());
                        } else {
                            log.warn("Không tìm thấy cache 'orders'");
                        }

                        return Optional.of(order);
                    } else {
                        log.error("Lỗi khi chuyển đổi entity sang domain, kết quả là null, orderId = {}, entity = {}", orderId.getValue(), entity.get());
                        return Optional.empty();
                    }
                } catch (Exception e) {
                    log.error("Lỗi khi chuyển đổi entity sang domain, orderId = {}, entity = {}", orderId.getValue(), entity.get(), e);
                    return Optional.empty();
                }
            } else {
                log.info("Không tìm thấy lệnh trong database, orderId = {}", orderId.getValue());
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("Lỗi khi cập nhật cache, orderId = {}", orderId.getValue(), e);
            return Optional.empty();
        }
    }

    /**
     * Xóa tất cả cache liên quan đến lệnh
     */
    public void clearAllOrderCaches() {
        try {
            log.info("Xóa tất cả cache liên quan đến lệnh");
            CacheManager cacheManager = applicationContext.getBean(CacheManager.class);

            // Xóa cache orders
            Cache ordersCache = cacheManager.getCache("orders");
            if (ordersCache != null) {
                ordersCache.clear();
                log.info("Đã xóa cache 'orders'");
            } else {
                log.warn("Không tìm thấy cache 'orders'");
            }

            // Xóa cache ordersByMemberId
            Cache ordersByMemberIdCache = cacheManager.getCache("ordersByMemberId");
            if (ordersByMemberIdCache != null) {
                ordersByMemberIdCache.clear();
                log.info("Đã xóa cache 'ordersByMemberId'");
            } else {
                log.warn("Không tìm thấy cache 'ordersByMemberId'");
            }

            // Xóa cache ordersBySymbol
            Cache ordersBySymbolCache = cacheManager.getCache("ordersBySymbol");
            if (ordersBySymbolCache != null) {
                ordersBySymbolCache.clear();
                log.info("Đã xóa cache 'ordersBySymbol'");
            } else {
                log.warn("Không tìm thấy cache 'ordersBySymbol'");
            }

            // Xóa cache ordersByStatus
            Cache ordersByStatusCache = cacheManager.getCache("ordersByStatus");
            if (ordersByStatusCache != null) {
                ordersByStatusCache.clear();
                log.info("Đã xóa cache 'ordersByStatus'");
            } else {
                log.warn("Không tìm thấy cache 'ordersByStatus'");
            }

            // Xóa cache ordersBySymbolAndStatus
            Cache ordersBySymbolAndStatusCache = cacheManager.getCache("ordersBySymbolAndStatus");
            if (ordersBySymbolAndStatusCache != null) {
                ordersBySymbolAndStatusCache.clear();
                log.info("Đã xóa cache 'ordersBySymbolAndStatus'");
            } else {
                log.warn("Không tìm thấy cache 'ordersBySymbolAndStatus'");
            }

            // Xóa cache ordersBySymbolAndStatusPage
            Cache ordersBySymbolAndStatusPageCache = cacheManager.getCache("ordersBySymbolAndStatusPage");
            if (ordersBySymbolAndStatusPageCache != null) {
                ordersBySymbolAndStatusPageCache.clear();
                log.info("Đã xóa cache 'ordersBySymbolAndStatusPage'");
            } else {
                log.warn("Không tìm thấy cache 'ordersBySymbolAndStatusPage'");
            }
        } catch (Exception e) {
            log.error("Lỗi khi xóa tất cả cache liên quan đến lệnh", e);
        }
    }

    /**
     * Chuyển đổi danh sách OrderJpaEntity sang danh sách Order
     * Xử lý lỗi khi chuyển đổi từng entity
     *
     * @param entities Danh sách OrderJpaEntity
     * @return Danh sách Order
     */
    private List<Order> mapToDomainEntities(List<OrderJpaEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return List.of();
        }

        List<Order> result = new ArrayList<>();

        for (OrderJpaEntity entity : entities) {
            try {
                Order order = orderPersistenceMapper.entityToDomain(entity);
                if (order != null) {
                    result.add(order);
                } else {
                    log.error("Lỗi khi chuyển đổi entity sang domain, entity = {}", entity);
                }
            } catch (Exception e) {
                log.error("Lỗi khi chuyển đổi entity sang domain, entity = {}", entity, e);
            }
        }

        return result;
    }

    /**
     * Tìm lệnh theo ID
     *
     * @param id ID của lệnh
     * @return Optional chứa lệnh nếu tìm thấy
     */
    @Override
    @Cacheable(value = "orders", key = "#id != null ? #id.value : ''", unless = "#result == null")
    public Optional<Order> findById(OrderId id) {
        log.info("Tìm lệnh theo ID, id = {}", id);

        try {
            Optional<OrderJpaEntity> entity = orderJpaRepository.findById(id.getValue());

            if (entity.isPresent()) {
                try {
                    // Chuyển đổi entity sang domain
                    Order order = orderPersistenceMapper.entityToDomain(entity.get());

                    if (order != null) {
                        return Optional.of(order);
                    } else {
                        log.error("Lỗi khi chuyển đổi entity sang domain, entity = {}", entity.get());
                        // Xóa cache khi có lỗi
                        clearOrderCache(id);
                        return Optional.empty();
                    }
                } catch (Exception e) {
                    log.error("Lỗi khi chuyển đổi entity sang domain, entity = {}", entity.get(), e);
                    // Xóa cache khi có lỗi
                    clearOrderCache(id);
                    return Optional.empty();
                }
            } else {
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("Tìm lệnh theo ID thất bại", e);
            // Xóa cache khi có lỗi
            if (id != null) {
                clearOrderCache(id);
            }
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo memberId, symbol và status
     *
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @param status   Trạng thái lệnh
     * @return Danh sách các lệnh
     */
    @Override
    public List<Order> findByMemberIdAndSymbolAndStatus(Long memberId, Symbol symbol, OrderStatus status) {
        log.info("Tìm các lệnh theo memberId, symbol và status, memberId = {}, symbol = {}, status = {}", memberId, symbol, status);

        try {
            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findByMemberIdAndSymbolAndStatus(memberId, symbol.getValue(), status.name()).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo memberId, symbol và status thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo memberId, symbol và status với phân trang
     *
     * @param memberId    ID của thành viên
     * @param symbol      Symbol của hợp đồng
     * @param status      Trạng thái lệnh
     * @param pageRequest Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    @Override
    public com.icetea.lotus.core.domain.valueobject.Page<Order> findByMemberIdAndSymbolAndStatus(Long memberId, Symbol symbol, OrderStatus status, com.icetea.lotus.core.domain.valueobject.PageRequest pageRequest) {
        log.info("Tìm các lệnh theo memberId, symbol và status với phân trang, memberId = {}, symbol = {}, status = {}, pageRequest = {}", memberId, symbol, status, pageRequest);

        try {
            Pageable pageable = org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize());
            org.springframework.data.domain.Page<OrderJpaEntity> page = orderJpaRepository.findByMemberIdAndSymbolAndStatus(memberId, symbol.getValue(), status.name(), pageable);

            List<Order> content = mapToDomainEntities(page.getContent());

            return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, page.getTotalElements());
        } catch (Exception e) {
            log.error("Tìm các lệnh theo memberId, symbol và status với phân trang thất bại", e);
            throw e;
        }
    }

    /**
     * Lưu danh sách lệnh
     *
     * @param orders Danh sách lệnh cần lưu
     * @return Danh sách lệnh đã được lưu
     */
    @Override
    public List<Order> saveAll(List<Order> orders) {
        log.info("Lưu danh sách lệnh, số lượng = {}", orders.size());

        try {
            List<OrderJpaEntity> entities = orders.stream()
                    .map(orderPersistenceMapper::domainToEntity)
                    .toList();
            List<OrderJpaEntity> savedEntities = orderJpaRepository.saveAll(entities);
            return mapToDomainEntities(savedEntities);
        } catch (Exception e) {
            log.error("Lưu danh sách lệnh thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo symbol, direction và status
     *
     * @param symbol    Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status    Trạng thái lệnh
     * @return Danh sách các lệnh
     */
    @Override
    public List<Order> findBySymbolAndDirectionAndStatus(Symbol symbol, OrderDirection direction, OrderStatus status) {
        log.info("Tìm các lệnh theo symbol, direction và status, symbol = {}, direction = {}, status = {}", symbol, direction, status);

        try {
            // Chuyển đổi OrderDirection từ domain entity sang OrderDirection trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderDirection jpaDirection =
                    com.icetea.lotus.core.domain.entity.OrderDirection.valueOf(direction.name());

            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                    com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findAllBySymbolAndDirectionAndStatus(symbol.getValue(), jpaDirection, jpaStatus).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo symbol, direction và status thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo symbol, direction và status với phân trang
     *
     * @param symbol      Symbol của hợp đồng
     * @param direction   Hướng của lệnh
     * @param status      Trạng thái lệnh
     * @param pageRequest Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    @Override
    public com.icetea.lotus.core.domain.valueobject.Page<Order> findBySymbolAndDirectionAndStatus(Symbol symbol, OrderDirection direction, OrderStatus status, com.icetea.lotus.core.domain.valueobject.PageRequest pageRequest) {
        log.info("Tìm các lệnh theo symbol, direction và status với phân trang, symbol = {}, direction = {}, status = {}, pageRequest = {}", symbol, direction, status, pageRequest);

        try {
            // Chuyển đổi OrderDirection từ domain entity sang OrderDirection trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderDirection jpaDirection =
                    com.icetea.lotus.core.domain.entity.OrderDirection.valueOf(direction.name());

            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                    com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            Pageable pageable = org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize());
            org.springframework.data.domain.Page<OrderJpaEntity> page = orderJpaRepository.findAllBySymbolAndDirectionAndStatus(symbol.getValue(), jpaDirection, jpaStatus, pageable);

            List<Order> content = mapToDomainEntities(page.getContent());

            return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, page.getTotalElements());
        } catch (Exception e) {
            log.error("Tìm các lệnh theo symbol, direction và status với phân trang thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo symbol, status và danh sách type
     *
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái lệnh
     * @param types  Danh sách loại lệnh
     * @return Danh sách các lệnh
     */
    @Override
    public List<Order> findBySymbolAndStatusAndTypeIn(Symbol symbol, OrderStatus status, List<OrderType> types) {
//        log.info("Tìm các lệnh theo symbol, status và danh sách type, symbol = {}, status = {}, types = {}", symbol, status, types);

        try {
            // Chuyển đổi danh sách OrderType thành danh sách String
            List<String> typeNames = types.stream().map(OrderType::name).toList();

            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                    com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findAllBySymbolAndStatusAndTypeIn(symbol.getValue(), jpaStatus, typeNames).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo symbol, status và danh sách type thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo symbol, direction và status, sắp xếp theo giá giảm dần
     *
     * @param symbol    Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status    Trạng thái lệnh
     * @return Danh sách các lệnh
     */
    @Override
    public List<Order> findBySymbolAndDirectionAndStatusOrderByPriceDesc(Symbol symbol, OrderDirection direction, OrderStatus status) {
//        log.info("Tìm các lệnh theo symbol, direction và status, sắp xếp theo giá giảm dần, symbol = {}, direction = {}, status = {}", symbol, direction, status);

        try {
            // Chuyển đổi OrderDirection từ domain entity sang OrderDirection trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderDirection jpaDirection =
                    com.icetea.lotus.core.domain.entity.OrderDirection.valueOf(direction.name());

            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                    com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findAllBySymbolAndDirectionAndStatusOrderByPriceDesc(symbol.getValue(), jpaDirection, jpaStatus).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo symbol, direction và status, sắp xếp theo giá giảm dần thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo symbol, direction và status, sắp xếp theo giá tăng dần
     *
     * @param symbol    Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status    Trạng thái lệnh
     * @return Danh sách các lệnh
     */
    @Override
    public List<Order> findBySymbolAndDirectionAndStatusOrderByPriceAsc(Symbol symbol, OrderDirection direction, OrderStatus status) {
//        log.info("Tìm các lệnh theo symbol, direction và status, sắp xếp theo giá tăng dần, symbol = {}, direction = {}, status = {}", symbol, direction, status);

        try {
            // Chuyển đổi OrderDirection từ domain entity sang OrderDirection trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderDirection jpaDirection =
                    com.icetea.lotus.core.domain.entity.OrderDirection.valueOf(direction.name());

            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                    com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findAllBySymbolAndDirectionAndStatusOrderByPriceAsc(symbol.getValue(), jpaDirection, jpaStatus).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo symbol, direction và status, sắp xếp theo giá tăng dần thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo symbol và status
     *
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái lệnh
     * @return Danh sách các lệnh
     */
    @Override
    @Cacheable(value = "ordersBySymbolAndStatus", key = "#symbol.value + '_' + #status")
    public List<Order> findBySymbolAndStatus(Symbol symbol, OrderStatus status) {
        log.info("Tìm các lệnh theo symbol và status, symbol = {}, status = {}", symbol, status);

        try {
            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                    com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            // Giới hạn số lượng kết quả trả về
            List<OrderJpaEntity> entities = orderJpaRepository.findBySymbolAndStatus(symbol.getValue(), jpaStatus).stream()
                    .limit(1000)
                    .toList();

            return mapToDomainEntities(entities);
        } catch (Exception e) {
            log.error("Tìm các lệnh theo symbol và status thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm các lệnh theo symbol và status với phân trang
     *
     * @param symbol      Symbol của hợp đồng
     * @param status      Trạng thái lệnh
     * @param pageRequest Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    @Override
    @Cacheable(value = "ordersBySymbolAndStatusPage", key = "#symbol.value + '_' + #status + '_' + #pageRequest.page + '_' + #pageRequest.size")
    public com.icetea.lotus.core.domain.valueobject.Page<Order> findBySymbolAndStatus(Symbol symbol, OrderStatus status, com.icetea.lotus.core.domain.valueobject.PageRequest pageRequest) {
        log.info("Tìm các lệnh theo symbol và status với phân trang, symbol = {}, status = {}, pageRequest = {}", symbol, status, pageRequest);

        try {
            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                    com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            Pageable pageable = org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize());
            org.springframework.data.domain.Page<OrderJpaEntity> page = orderJpaRepository.findBySymbolAndStatus(symbol.getValue(), jpaStatus, pageable);

            List<Order> content = mapToDomainEntities(page.getContent());

            return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, page.getTotalElements());
        } catch (Exception e) {
            log.error("Tìm các lệnh theo symbol và status với phân trang thất bại", e);
            throw e;
        }
    }
}
