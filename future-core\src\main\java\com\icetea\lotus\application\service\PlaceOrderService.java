package com.icetea.lotus.application.service;

import com.icetea.lotus.application.dto.OrderBookDto;
import com.icetea.lotus.application.dto.OrderDto;
import com.icetea.lotus.application.dto.PlaceOrderCommand;
import com.icetea.lotus.application.dto.PlaceOrderResult;
import com.icetea.lotus.application.mapper.OrderMapper;
import com.icetea.lotus.application.port.input.ManageOrderMatchingUseCase;
import com.icetea.lotus.application.port.input.PlaceOrderUseCase;
import com.icetea.lotus.application.port.output.ContractPersistencePort;
import com.icetea.lotus.application.port.output.OrderPersistencePort;
import com.icetea.lotus.application.port.output.WalletPersistencePort;
import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.OrderDirection;
import com.icetea.lotus.core.domain.entity.OrderStatus;
import com.icetea.lotus.core.domain.entity.OrderType;
import com.icetea.lotus.core.domain.exception.BusinessException;
import com.icetea.lotus.core.domain.exception.EntityNotFoundException;
import com.icetea.lotus.core.domain.exception.InsufficientBalanceException;
import com.icetea.lotus.core.domain.exception.ValidationException;
import com.icetea.lotus.core.domain.service.LeverageService;
import com.icetea.lotus.core.domain.service.PriceManagementService;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.api.dto.ApiResponse;
import com.icetea.lotus.infrastructure.api.request.FindOrderRequest;
import com.icetea.lotus.infrastructure.compensation.KafkaFailureCompensationService;
import com.icetea.lotus.infrastructure.messaging.producer.OrderCommandProducer;
import com.icetea.lotus.infrastructure.persistence.adapter.OrderPersistenceAdapter;
import com.icetea.lotus.infrastructure.util.SnowflakeIdGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service cho việc đặt lệnh
 * Triển khai use case PlaceOrderUseCase
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlaceOrderService implements PlaceOrderUseCase {

    private final OrderPersistencePort orderPersistencePort;
    private final ContractPersistencePort contractPersistencePort;
    private final WalletPersistencePort walletPersistencePort;
    private final OrderMapper orderMapper;
    private final OrderCommandProducer orderCommandProducer;
    private final KafkaFailureCompensationService compensationService;
    private final PriceManagementService priceManagementService;
    private final SnowflakeIdGenerator snowflakeIdGenerator;
    private final ManageOrderMatchingUseCase manageOrderMatchingUseCase;
    private final LeverageService leverageService;

    /**
     * Đặt lệnh mới với xử lý ngoại lệ và thử lại
     *
     * @param command Command chứa thông tin lệnh
     * @return Kết quả đặt lệnh
     */
    @Override
    @Transactional
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2),
            noRetryFor = {EntityNotFoundException.class, ValidationException.class}
    )
    public PlaceOrderResult placeOrder(PlaceOrderCommand command) {
        try {
            log.info("Đặt lệnh mới, command = {}", command);

            if (command == null) {
                throw new ValidationException("Command không được để trống");
            }

            if (command.getMemberId() == null) {
                throw new ValidationException("MemberId không được để trống");
            }

            if (command.getSymbol() == null || command.getSymbol().isEmpty()) {
                throw new ValidationException("Symbol không được để trống");
            }

            if (command.getType() == null) {
                throw new ValidationException("OrderType không được để trống");
            }

            if (command.getDirection() == null) {
                throw new ValidationException("OrderDirection không được để trống");
            }

            // Kiểm tra hợp đồng
            Optional<Contract> contractOpt = contractPersistencePort.findBySymbol(Symbol.of(command.getSymbol()));
            if (contractOpt.isEmpty()) {
                throw new EntityNotFoundException("Hợp đồng không tồn tại: " + command.getSymbol());
            }

            Contract contract = contractOpt.get();

            // Thiết lập contractId cho command
            command.setContractId(contract.getId());
            log.info("Đã thiết lập contractId = {} cho command", contract.getId());

            // Thêm thông tin về contract vào log
            log.info("Thông tin contract: id = {}, symbol = {}, baseCoin = {}, quoteCoin = {}",
                    contract.getId(), contract.getSymbol().getValue(), contract.getBaseCoin(), contract.getQuoteCoin());

            // Kiểm tra giá
            if (command.getType() != OrderType.MARKET && command.getPrice() == null) {
                throw new ValidationException("Giá không được để trống với lệnh giới hạn");
            }

            // Kiểm tra khối lượng
            if (command.getVolume() == null || command.getVolume().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidationException("Khối lượng phải lớn hơn 0");
            }

            if (command.getVolume().compareTo(contract.getMinTradeAmount().getValue()) < 0 ||
                    command.getVolume().compareTo(contract.getMaxTradeAmount().getValue()) > 0) {
                throw new IllegalArgumentException("Khối lượng phải nằm trong khoảng từ " + contract.getMinPrice() + " đến " + contract.getMaxPrice());
            }

            // Kiểm tra giá kích hoạt cho các loại stop order
            if ((command.getType() == OrderType.STOP_LOSS || command.getType() == OrderType.STOP_LOSS_LIMIT ||
                    command.getType() == OrderType.STOP_MARKET || command.getType() == OrderType.STOP_LIMIT ||
                    command.getType() == OrderType.TAKE_PROFIT || command.getType() == OrderType.TAKE_PROFIT_LIMIT ||
                    command.getType() == OrderType.TAKE_PROFIT_MARKET) &&
                    command.getTriggerPrice() == null) {
                throw new ValidationException("Giá kích hoạt không được để trống với lệnh stop hoặc take profit");
            }

            // Kiểm tra giá cho các loại lệnh khác nhau
            if (command.getType() == OrderType.STOP_MARKET || command.getType() == OrderType.TAKE_PROFIT_MARKET) {
                // STOP_MARKET và TAKE_PROFIT_MARKET không cần price, chỉ cần triggerPrice
                if (command.getPrice() != null) {
                    log.warn("Lệnh {} không cần giá, sẽ bỏ qua giá đã cung cấp: {}",
                            command.getType(), command.getPrice());
                }
            }

            // Kiểm tra và xử lý đòn bẩy
            BigDecimal leverage = command.getLeverage();
            log.info("Đòn bẩy từ command: {}", leverage);

            if (leverage == null) {
                // Nếu không có đòn bẩy, sử dụng đòn bẩy mặc định của user cho symbol này
                leverage = leverageService.getDefaultLeverage(command.getMemberId(), Symbol.of(command.getSymbol()));
                log.info("Không có đòn bẩy trong request, sử dụng đòn bẩy mặc định của user = {}", leverage);
            } else {
                log.info("Sử dụng đòn bẩy từ request = {}", leverage);
                // Kiểm tra xem đòn bẩy có nằm trong khoảng cho phép không
                BigDecimal minLeverage = BigDecimal.valueOf(contract.getLeverageMin());
                BigDecimal maxLeverage = BigDecimal.valueOf(contract.getLeverageMax());
                if (leverage.compareTo(minLeverage) < 0 || leverage.compareTo(maxLeverage) > 0) {
                    throw new ValidationException(
                            String.format("Đòn bẩy phải nằm trong khoảng từ %s đến %s",
                                    minLeverage.toPlainString(), maxLeverage.toPlainString()));
                }
            }

            // Xử lý giá cho các loại lệnh khác nhau
            BigDecimal effectivePrice = command.getPrice();
            Symbol symbol = Symbol.of(command.getSymbol());

            // Nếu là lệnh thị trường hoặc không có giá, lấy giá từ hệ thống
            // STOP_MARKET và TAKE_PROFIT_MARKET cũng cần giá để tính toán margin
            if (effectivePrice == null || command.getType() == OrderType.MARKET ||
                command.getType() == OrderType.STOP_MARKET || command.getType() == OrderType.TAKE_PROFIT_MARKET) {
                try {
                    // Lấy giá đánh dấu từ hệ thống
                    Money markPrice = priceManagementService.getCurrentMarkPrice(symbol);

                    if (markPrice != null && !markPrice.isZero()) {
                        effectivePrice = markPrice.getValue();
                        log.info("Sử dụng giá đánh dấu = {}", effectivePrice);
                    } else {
                        // Nếu không có giá đánh dấu, thử lấy giá chỉ số
                        Money indexPrice = priceManagementService.getCurrentIndexPrice(symbol);
                        if (indexPrice != null && !indexPrice.isZero()) {
                            effectivePrice = indexPrice.getValue();
                            log.info("Sử dụng giá chỉ số = {}", effectivePrice);
                        } else {
                            // Nếu không có cả giá đánh dấu và giá chỉ số, báo lỗi
                            throw new ValidationException("Không thể xác định giá cho lệnh, không tìm thấy giá đánh dấu hoặc giá chỉ số");
                        }
                    }
                } catch (ValidationException e) {
                    // Ném lại ngoại lệ xác thực
                    throw e;
                } catch (Exception e) {
                    // Nếu có lỗi khi lấy giá, báo lỗi
                    log.error("Lỗi khi lấy giá đánh dấu hoặc giá chỉ số", e);
                    throw new ValidationException("Không thể xác định giá cho lệnh: " + e.getMessage());
                }
            }

            // Tính toán margin cần thiết
            BigDecimal requiredMargin = calculateRequiredMargin(contract, command.getVolume(), effectivePrice, leverage);

            // Kiểm tra ví
            try {
                if (!walletPersistencePort.hasEnoughBalance(command.getMemberId(), contract.getQuoteCoin(), requiredMargin)) {
                    throw new InsufficientBalanceException("Số dư không đủ");
                }
            } catch (IllegalArgumentException e) {
                // Nếu ví không tồn tại, báo lỗi cụ thể và không thử lại
                if (e.getMessage().contains("Ví không tồn tại")) {
                    log.warn("Ví không tồn tại, không thử lại, memberId = {}, coin = {}", command.getMemberId(), contract.getQuoteCoin());
                    throw new EntityNotFoundException("Ví không tồn tại cho memberId = " + command.getMemberId() + " và coin = " + contract.getQuoteCoin());
                }
                // Ném lại các ngoại lệ khác
                throw e;
            }

            // Tạo orderId theo định dạng ORD + timestamp
            String orderId = generateOrderId();
            log.info("Đã tạo orderId mới: {}", orderId);

            // Chuyển đổi volume từ USDT sang BTC
            BigDecimal volumeInBTC = command.getVolume().divide(effectivePrice, 8, RoundingMode.DOWN);
            log.info("Chuyển đổi volume từ USDT sang BTC: {} USDT = {} BTC (giá = {})",
                    command.getVolume(), volumeInBTC, effectivePrice);

            // Tạo lệnh mới với thông tin từ contract
            Order order = Order.builder()
                    .orderId(OrderId.of(orderId))
                    .memberId(command.getMemberId())
                    .contractId(contract.getId())
                    .symbol(Symbol.of(command.getSymbol()))
                    .coinSymbol(contract.getBaseCoin())
                    .baseSymbol(contract.getQuoteCoin())
                    .direction(command.getDirection())
                    .type(command.getType())
                    // Sử dụng giá hiệu quả đã xử lý - MARKET, STOP_MARKET, TAKE_PROFIT_MARKET không có price
                    .price((command.getType() == OrderType.MARKET ||
                           command.getType() == OrderType.STOP_MARKET ||
                           command.getType() == OrderType.TAKE_PROFIT_MARKET) ? null : Money.of(effectivePrice))
                    .triggerPrice(command.getTriggerPrice() != null ? Money.of(command.getTriggerPrice()) : null)
                    .volume(command.getVolume()) // Sử dụng volume USDT
                    .dealVolume(BigDecimal.ZERO)
                    .dealMoney(Money.ZERO)
                    // Phí ban đầu là 0, sẽ được cập nhật khi lệnh được khớp
                    // Thông tin về tỷ lệ phí được lấy từ contract (takerFeeRate/makerFeeRate) khi khớp lệnh
                    .fee(Money.ZERO)
                    .status(OrderStatus.NEW)
                    .createTime(LocalDateTime.now())
                    .completeTime(null)
                    .timeInForce(command.getTimeInForce())
                    // Sử dụng đòn bẩy đã xử lý
                    .leverage(leverage)
                    .reduceOnly(command.getReduceOnly())
                    .callbackRate(command.getCallbackRate())
                    .activationPrice(null)
                    .postOnly(command.getPostOnly())
                    .cancelReason(null)
                    .maxSlippage(command.getMaxSlippage())
                    .fillOrKill(command.getFillOrKill())
                    .immediateOrCancel(command.getImmediateOrCancel())
                    .selfTradePreventionMode(command.getSelfTradePreventionMode())
                    .build();
            log.info("Đã tạo lệnh mới từ command và contract: {}", order);

            // Đóng băng margin
            walletPersistencePort.freezeBalance(command.getMemberId(), contract.getQuoteCoin(), requiredMargin);
            log.info("Đã đóng băng margin: {}, memberId = {}, coin = {}", requiredMargin, command.getMemberId(), contract.getQuoteCoin());

            // Lưu lệnh
            log.info("Bắt đầu lưu lệnh vào database, orderId = {}, contractId = {}", order.getOrderId().getValue(), order.getContractId());
            Order savedOrder = orderPersistencePort.save(order);
            log.info("Đã lưu lệnh vào database thành công, orderId = {}", savedOrder.getOrderId().getValue());

            // Gửi lệnh đến Kafka
            sendOrderToKafka(savedOrder);

            log.info("Đặt lệnh mới thành công, orderId = {}", savedOrder.getOrderId().getValue());

            // Trả về kết quả
            return PlaceOrderResult.builder()
                    .orderId(savedOrder.getOrderId().getValue())
                    .success(true)
                    .message("Order placed successfully")
                    .order(orderMapper.orderToDto(savedOrder))
                    .build();
        } catch (ValidationException e) {
            log.warn("Lỗi xác thực khi đặt lệnh mới: {}", e.getMessage());
            throw e;
        } catch (EntityNotFoundException e) {
            log.warn("Lỗi không tìm thấy entity khi đặt lệnh mới: {}", e.getMessage());
            throw e;
        } catch (BusinessException e) {
            log.warn("Lỗi nghiệp vụ khi đặt lệnh mới: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi đặt lệnh mới", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi đặt lệnh mới", e);
            throw new BusinessException("ERROR", "Lỗi khi đặt lệnh mới: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi đặt lệnh mới thất bại
     *
     * @param e       Ngoại lệ
     * @param command Command chứa thông tin lệnh
     * @return PlaceOrderResult
     */
    @Recover
    public PlaceOrderResult recoverPlaceOrder(Exception e, PlaceOrderCommand command) {
        log.error("Đã thử lại đặt lệnh mới 3 lần nhưng thất bại, memberId = {}, symbol = {}",
                command.getMemberId(), command.getSymbol(), e);

        // Xử lý thông báo lỗi dựa trên loại ngoại lệ
        String errorMessage;
        if (e instanceof DataAccessException || e instanceof TransactionException) {
            // Đây là các lỗi có thể thử lại
            errorMessage = "Đặt lệnh thất bại sau 3 lần thử lại do lỗi hệ thống. Vui lòng thử lại sau.";
        } else {
            // Các lỗi khác không nên hiển thị "sau 3 lần thử lại" vì chúng không được thử lại
            errorMessage = "Đặt lệnh thất bại: " + e.getMessage();
        }

        return PlaceOrderResult.builder()
                .orderId(null)
                .success(false)
                .message(errorMessage)
                .order(null)
                .build();
    }

    /**
     * Tính toán margin cần thiết
     *
     * @param contract Hợp đồng
     * @param volume   Khối lượng (tính theo USDT)
     * @param price    Giá
     * @param leverage Đòn bẩy
     * @return Margin cần thiết
     */
    private BigDecimal calculateRequiredMargin(Contract contract, BigDecimal volume, BigDecimal price, BigDecimal leverage) {
        if (volume == null || volume.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidationException("Khối lượng phải lớn hơn 0");
        }

        // Kiểm tra giá
        if (price == null) {
            throw new ValidationException("Giá không được để trống khi tính toán margin");
        }

        // Kiểm tra đòn bẩy
        BigDecimal effectiveLeverage = leverage;
        if (effectiveLeverage == null) {
            effectiveLeverage = contract.getDefaultLeverage();
            log.info("Sử dụng đòn bẩy mặc định = {} khi tính toán margin", effectiveLeverage);
        }

        if (effectiveLeverage.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidationException("Đòn bẩy phải lớn hơn 0");
        }

        // Volume đã tính theo USDT, nên notionalValue chính là volume
        log.debug("Giá trị danh nghĩa = {} (volume tính theo USDT)", volume);

        // Tính toán margin cần thiết (notional value / leverage)
        BigDecimal requiredMargin = volume.divide(effectiveLeverage, 8, RoundingMode.HALF_UP);
        log.debug("Margin cần thiết = {} (notionalValue = {} / leverage = {})", requiredMargin, volume, effectiveLeverage);

        return requiredMargin;
    }

    /**
     * Gửi lệnh đến OrderCommandProducer với compensation mechanism
     *
     * @param order Lệnh
     */
    private void sendOrderToKafka(Order order) {
        boolean kafkaSent = sendOrderToKafkaWithRetry(order);

        if (!kafkaSent) {
            // Schedule for compensation retry
            compensationService.scheduleKafkaRetry(order);
            log.warn("Kafka send failed for order {}, scheduled for compensation retry", order.getOrderId().getValue());
        }
    }

    /**
     * Thử gửi lệnh đến Kafka với immediate retry
     *
     * @param order Lệnh
     * @return true nếu gửi thành công
     */
    private boolean sendOrderToKafkaWithRetry(Order order) {
        int maxRetries = 3;
        for (int i = 0; i < maxRetries; i++) {
            try {
                orderCommandProducer.sendPlaceOrderCommand(order);
                log.info("Đã gửi lệnh đến OrderCommandProducer thành công, orderId = {}", order.getOrderId().getValue());
                return true;
            } catch (Exception e) {
                log.warn("Kafka send attempt {} failed for order {}: {}", i + 1, order.getOrderId().getValue(), e.getMessage());
                if (i < maxRetries - 1) {
                    try {
                        Thread.sleep(1000 * (i + 1)); // Exponential backoff: 1s, 2s, 3s
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("All Kafka send attempts failed for order {}", order.getOrderId().getValue());
        return false;
    }

    /**
     * Tạo orderId sử dụng Snowflake ID
     *
     * @return OrderId duy nhất với tiền tố "ORD"
     */
    private String generateOrderId() {
        // Sử dụng SnowflakeIdGenerator để tạo ID duy nhất với tiền tố "ORD"
        String orderId = snowflakeIdGenerator.nextIdWithPrefix("ORD");
        log.debug("Đã tạo orderId mới sử dụng Snowflake ID: {}", orderId);

        return orderId;
    }

    /**
     * Gửi lệnh hủy đến OrderCommandProducer
     *
     * @param order Lệnh
     */
    private void sendCancelOrderToKafka(Order order) {
        try {
            // Gửi lệnh hủy đến OrderCommandProducer
            orderCommandProducer.sendCancelOrderCommand(order);

            log.info("Đã gửi lệnh hủy đến OrderCommandProducer, orderId = {}", order.getOrderId().getValue());
        } catch (Exception e) {
            log.error("Gửi lệnh hủy đến OrderCommandProducer thất bại", e);
        }
    }

    /**
     * Hủy lệnh
     *
     * @param orderId  ID của lệnh
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @return OrderDto
     */
    @Override
    @Transactional
    public OrderDto cancelOrder(String orderId, Long memberId, String symbol) {
        log.info("Hủy lệnh bắt đầu, orderId = {}, memberId = {}, symbol = {}", orderId, memberId, symbol);

        try {
            Optional<Order> orderOpt = orderPersistencePort.findByOrderId(OrderId.of(orderId));
            if (orderOpt.isEmpty()) {
                log.warn("Lệnh không tồn tại, orderId = {}", orderId);
                throw new IllegalArgumentException("Lệnh không tồn tại");
            }

            Order order = orderOpt.get();
            log.info("Lệnh tìm thấy: orderId = {}, status = {}, volume = {}, dealVolume = {}, leverage = {}",
                    orderId, order.getStatus(), order.getVolume(), order.getDealVolume(), order.getLeverage());

            if (!order.getMemberId().equals(memberId)) {
                log.warn("Không có quyền hủy lệnh, memberId = {}, orderMemberId = {}", memberId, order.getMemberId());
                throw new IllegalArgumentException("Không có quyền hủy lệnh");
            }

            if (!order.getSymbol().getValue().equals(symbol)) {
                log.warn("Symbol không khớp, yêu cầu = {}, thực tế = {}", symbol, order.getSymbol().getValue());
                throw new IllegalArgumentException("Symbol không khớp");
            }

            if (order.isCompleted()) {
                log.warn("Lệnh đã hoàn thành, không thể hủy, orderId = {}", orderId);
                throw new IllegalArgumentException("Lệnh đã hoàn thành, không thể hủy");
            }

            if (order.getStatus() != OrderStatus.NEW && order.getStatus() != OrderStatus.PARTIALLY_FILLED) {
                log.warn("Trạng thái lệnh không cho phép hủy, status = {}", order.getStatus());
                throw new IllegalArgumentException("Lệnh không thể hủy do trạng thái không hợp lệ: " + order.getStatus());
            }

            Optional<Contract> contractOpt = contractPersistencePort.findBySymbol(order.getSymbol());
            if (contractOpt.isEmpty()) {
                log.warn("Không tìm thấy hợp đồng với symbol = {}", order.getSymbol().getValue());
                throw new IllegalArgumentException("Hợp đồng không tồn tại");
            }

            Contract contract = contractOpt.get();

            BigDecimal remainingVolumeInUSDT = order.getVolume().subtract(order.getDealVolume());
            log.info("Khối lượng còn lại chưa khớp = {} (volume = {}, dealVolume = {})",
                    remainingVolumeInUSDT, order.getVolume(), order.getDealVolume());

//            BigDecimal price = null;
//            try {
//                if (order.getPrice() != null) {
//                    price = order.getPrice().getValue();
//                    log.info("Lệnh có giá cụ thể: {}", price);
//                } else {
//                    Money markPrice = priceManagementService.getCurrentMarkPrice(order.getSymbol());
//                    if (markPrice != null && !markPrice.isZero()) {
//                        price = markPrice.getValue();
//                        log.info("Giá đánh dấu được sử dụng: {}", price);
//                    } else {
//                        Money indexPrice = priceManagementService.getCurrentIndexPrice(order.getSymbol());
//                        if (indexPrice != null && !indexPrice.isZero()) {
//                            price = indexPrice.getValue();
//                            log.info("Giá chỉ số được sử dụng: {}", price);
//                        } else {
//                            price = BigDecimal.ONE;
//                            log.warn("Không có giá đánh dấu hoặc chỉ số, sử dụng giá mặc định: {}", price);
//                        }
//                    }
//                }
//            } catch (Exception e) {
//                price = BigDecimal.ONE;
//                log.error("Lỗi khi lấy giá thị trường, sử dụng giá mặc định = {}", price, e);
//            }
//
//            BigDecimal remainingVolumeInUSDT = order.getVolume();
//            log.info("Lấy khối lượng sang USDT: {}", remainingVolumeInUSDT);

            BigDecimal remainingMargin = calculateRequiredMargin(contract, remainingVolumeInUSDT, order.getPrice().getValue(), order.getLeverage());
            log.info("Margin cần giải phóng: {}", remainingMargin);

            // Cập nhật trạng thái lệnh
            Order canceledOrder = order.toBuilder()
                    .status(OrderStatus.CANCELED)
                    .completeTime(LocalDateTime.now())
                    .cancelReason("Canceled by user")
                    .build();

            Order savedOrder = orderPersistencePort.save(canceledOrder);
            log.info("Lệnh đã được hủy và lưu lại, orderId = {}", savedOrder.getOrderId().getValue());

            // Giải phóng margin
            if (remainingMargin.compareTo(BigDecimal.ZERO) > 0) {
                log.info("Thực hiện giải phóng margin cho memberId = {}, coin = {}, amount = {}",
                        order.getMemberId(), contract.getQuoteCoin(), remainingMargin);
                walletPersistencePort.unfreezeBalance(order.getMemberId(), contract.getQuoteCoin(), remainingMargin);
            } else {
                log.warn("Không có margin để giải phóng (remainingMargin = 0)");
            }

            // Gửi thông tin hủy đến Kafka
            sendCancelOrderToKafka(canceledOrder);

            return orderMapper.orderToDto(savedOrder);
        } catch (Exception e) {
            log.error("Hủy lệnh thất bại, orderId = {}", orderId, e);
            throw e;
        }
    }

    /**
     * Hủy tất cả các lệnh của một thành viên
     *
     * @param memberId ID của thành viên
     * @return Số lượng lệnh đã hủy
     */
    @Override
    @Transactional
    public int cancelAllOrders(Long memberId) {
        log.info("Hủy tất cả các lệnh, memberId = {}", memberId);

        try {
            // Lấy danh sách các lệnh đang hoạt động (NEW hoặc PARTIALLY_FILLED)
            List<OrderStatus> activeStatuses = Arrays.asList(OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED);
            List<Order> activeOrders = new ArrayList<>();

            // Lấy tất cả các lệnh của thành viên
            List<Order> allOrders = orderPersistencePort.findByMemberId(memberId);

            // Lọc ra các lệnh đang hoạt động
            for (Order order : allOrders) {
                if (activeStatuses.contains(order.getStatus())) {
                    activeOrders.add(order);
                }
            }

            if (activeOrders.isEmpty()) {
                log.info("Không có lệnh nào cần hủy, memberId = {}", memberId);
                return 0;
            }

            log.info("Tìm thấy {} lệnh đang hoạt động cần hủy, memberId = {}", activeOrders.size(), memberId);

            // Nhóm các lệnh theo symbol để xử lý hiệu quả
            Map<Symbol, List<Order>> ordersBySymbol = activeOrders.stream()
                    .collect(Collectors.groupingBy(Order::getSymbol));

            int canceledCount = 0;

            // Xử lý từng nhóm lệnh theo symbol
            for (Map.Entry<Symbol, List<Order>> entry : ordersBySymbol.entrySet()) {
                Symbol symbol = entry.getKey();
                List<Order> symbolOrders = entry.getValue();

                // Lấy thông tin hợp đồng
                Optional<Contract> contractOpt = contractPersistencePort.findBySymbol(symbol);
                if (contractOpt.isEmpty()) {
                    log.warn("Hợp đồng không tồn tại, symbol = {}, bỏ qua {} lệnh", symbol.getValue(), symbolOrders.size());
                    continue;
                }

                Contract contract = contractOpt.get();
                BigDecimal totalMarginToUnfreeze = BigDecimal.ZERO;
                List<Order> canceledOrders = new ArrayList<>();

                // Cập nhật trạng thái và tính toán margin cần giải phóng
                for (Order order : symbolOrders) {
                    try {
                        // Tính toán margin cần giải phóng
                        BigDecimal remainingVolume = order.getVolume().subtract(order.getDealVolume());

                        // Xử lý trường hợp order.getPrice() là null (lệnh thị trường)
                        BigDecimal price = null;
                        if (order.getPrice() != null) {
                            price = order.getPrice().getValue();
                        } else {
                            try {
                                Money markPrice = priceManagementService.getCurrentMarkPrice(symbol);
                                if (markPrice != null && !markPrice.isZero()) {
                                    price = markPrice.getValue();
                                } else {
                                    Money indexPrice = priceManagementService.getCurrentIndexPrice(symbol);
                                    if (indexPrice != null && !indexPrice.isZero()) {
                                        price = indexPrice.getValue();
                                    } else {
                                        price = BigDecimal.ONE;
                                    }
                                }
                            } catch (Exception e) {
                                price = BigDecimal.ONE;
                            }
                        }

                        // Chuyển đổi volume từ BTC sang USDT
                        BigDecimal remainingVolumeInUSDT = remainingVolume.multiply(price);
                        log.debug("Chuyển đổi volume từ BTC sang USDT: {} BTC = {} USDT (giá = {})",
                                remainingVolume, remainingVolumeInUSDT, price);

                        BigDecimal orderMargin = calculateRequiredMargin(contract, remainingVolumeInUSDT, price, order.getLeverage());
                        totalMarginToUnfreeze = totalMarginToUnfreeze.add(orderMargin);

                        // Cập nhật trạng thái lệnh
                        Order canceledOrder = order.toBuilder()
                                .status(OrderStatus.CANCELED)
                                .completeTime(java.time.LocalDateTime.now())
                                .cancelReason("Canceled by batch operation")
                                .build();

                        canceledOrders.add(canceledOrder);
                    } catch (Exception e) {
                        log.warn("Lỗi khi tính toán margin cho lệnh, orderId = {}, error = {}",
                                order.getOrderId().getValue(), e.getMessage());
                    }
                }

                // Lưu tất cả các lệnh đã hủy trong một thao tác
                if (!canceledOrders.isEmpty()) {
                    try {
                        // Lưu các lệnh đã hủy
                        for (Order canceledOrder : canceledOrders) {
                            orderPersistencePort.save(canceledOrder);

                            // Gửi lệnh hủy đến Kafka
                            sendCancelOrderToKafka(canceledOrder);
                        }

                        // Giải phóng margin cho tất cả các lệnh cùng một lúc
                        if (totalMarginToUnfreeze.compareTo(BigDecimal.ZERO) > 0) {
                            walletPersistencePort.unfreezeBalance(memberId, contract.getQuoteCoin(), totalMarginToUnfreeze);
                        }

                        canceledCount += canceledOrders.size();
                    } catch (Exception e) {
                        log.error("Lỗi khi lưu các lệnh đã hủy hoặc giải phóng margin, symbol = {}, error = {}",
                                symbol.getValue(), e.getMessage());
                    }
                }
            }

            log.info("Đã hủy {} lệnh, memberId = {}", canceledCount, memberId);
            return canceledCount;
        } catch (Exception e) {
            log.error("Hủy tất cả các lệnh thất bại", e);
            throw e;
        }
    }

    /**
     * Hủy tất cả các lệnh của một thành viên cho một symbol
     *
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @return List<OrderDto>
     */
    @Override
    @Transactional
    public List<OrderDto> cancelAllOrders(Long memberId, String symbol) {
        log.info("Hủy tất cả các lệnh, memberId = {}, symbol = {}", memberId, symbol);

        try {
            Symbol symbolObj = Symbol.of(symbol);

            // Lấy danh sách các lệnh đang hoạt động (NEW hoặc PARTIALLY_FILLED)
            List<OrderStatus> activeStatuses = Arrays.asList(OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED);

            // Lấy tất cả các lệnh của thành viên cho symbol theo trạng thái
            List<Order> activeOrders = orderPersistencePort.findByMemberIdAndSymbolAndStatusIn(memberId, symbolObj, activeStatuses);

            if (activeOrders.isEmpty()) {
                log.info("Không có lệnh nào cần hủy, memberId = {}, symbol = {}", memberId, symbol);
                return new ArrayList<>();
            }

            log.info("Tìm thấy {} lệnh đang hoạt động cần hủy, memberId = {}, symbol = {}",
                    activeOrders.size(), memberId, symbol);

            // Lấy thông tin hợp đồng
            Optional<Contract> contractOpt = contractPersistencePort.findBySymbol(symbolObj);
            if (contractOpt.isEmpty()) {
                log.warn("Hợp đồng không tồn tại, symbol = {}", symbol);
                throw new IllegalArgumentException("Hợp đồng không tồn tại: " + symbol);
            }

            Contract contract = contractOpt.get();
            BigDecimal totalMarginToUnfreeze = BigDecimal.ZERO;
            List<Order> canceledOrders = new ArrayList<>();

            // Cập nhật trạng thái và tính toán margin cần giải phóng
            for (Order order : activeOrders) {
                try {
                    // Volume đã là USDT, chỉ cần lấy volume còn lại chưa khớp
                    BigDecimal remainingVolumeInUSDT = order.getVolume().subtract(order.getDealVolume());
                    log.info("Khối lượng còn lại chưa khớp = {} USDT (volume = {} USDT, dealVolume = {} USDT)",
                            remainingVolumeInUSDT, order.getVolume(), order.getDealVolume());

                    // Tính toán margin cần giải phóng (giống với cancelOrder)
                    BigDecimal orderMargin = calculateRequiredMargin(contract, remainingVolumeInUSDT, order.getPrice().getValue(), order.getLeverage());
                    totalMarginToUnfreeze = totalMarginToUnfreeze.add(orderMargin);
                    log.info("Margin cần giải phóng cho lệnh {}: {}", order.getOrderId().getValue(), orderMargin);

                    // Cập nhật trạng thái lệnh
                    Order canceledOrder = order.toBuilder()
                            .status(OrderStatus.CANCELED)
                            .completeTime(java.time.LocalDateTime.now())
                            .cancelReason("Canceled by batch operation")
                            .build();

                    canceledOrders.add(canceledOrder);
                } catch (Exception e) {
                    log.warn("Lỗi khi tính toán margin cho lệnh, orderId = {}, error = {}",
                            order.getOrderId().getValue(), e.getMessage());
                }
            }

            List<Order> savedOrders = new ArrayList<>();

            // Lưu tất cả các lệnh đã hủy trong một thao tác
            if (!canceledOrders.isEmpty()) {
                try {
                    // Lưu các lệnh đã hủy
                    for (Order canceledOrder : canceledOrders) {
                        Order savedOrder = orderPersistencePort.save(canceledOrder);
                        savedOrders.add(savedOrder);

                        // Gửi lệnh hủy đến Kafka
                        sendCancelOrderToKafka(canceledOrder);
                    }

                    // Giải phóng margin cho tất cả các lệnh cùng một lúc
                    if (totalMarginToUnfreeze.compareTo(BigDecimal.ZERO) > 0) {
                        log.info("Thực hiện giải phóng margin cho memberId = {}, coin = {}, amount = {}",
                                memberId, contract.getQuoteCoin(), totalMarginToUnfreeze);
                        walletPersistencePort.unfreezeBalance(memberId, contract.getQuoteCoin(), totalMarginToUnfreeze);
                    } else {
                        log.warn("Không có margin để giải phóng (totalMarginToUnfreeze = 0)");
                    }
                } catch (Exception e) {
                    log.error("Lỗi khi lưu các lệnh đã hủy hoặc giải phóng margin, symbol = {}, error = {}",
                            symbol, e.getMessage());
                    throw e;
                }
            }

            log.info("Đã hủy {} lệnh, memberId = {}, symbol = {}", savedOrders.size(), memberId, symbol);

            // Chuyển đổi các lệnh đã lưu thành DTO
            return savedOrders.stream()
                    .map(orderMapper::orderToDto)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Hủy tất cả các lệnh thất bại", e);
            throw e;
        }
    }

    /**
     * Lấy thông tin lệnh
     *
     * @param orderId ID của lệnh
     * @return Thông tin lệnh
     */
    @Override
    public OrderDto getOrder(String orderId) {
        log.info("Lấy thông tin lệnh, orderId = {}", orderId);
        log.info("OrderPersistencePort implementation: {}", orderPersistencePort.getClass().getName());

        try {
            OrderId orderIdObj = OrderId.of(orderId);
            log.info("Tìm lệnh theo orderId = {}", orderIdObj.getValue());

            Optional<Order> orderOpt = orderPersistencePort.findByOrderId(orderIdObj);
            log.info("Kết quả tìm lệnh: {}", orderOpt.isPresent() ? "Tìm thấy" : "Không tìm thấy");

            if (orderOpt.isEmpty()) {
                log.warn("Lệnh không tìm thấy trong cache, thử tìm trực tiếp từ database, orderId = {}", orderId);

                // Thử refresh cache và tìm lại
                if (orderPersistencePort instanceof OrderPersistenceAdapter) {
                    OrderPersistenceAdapter adapter = (OrderPersistenceAdapter) orderPersistencePort;
                    orderOpt = adapter.refreshOrderCache(orderIdObj);
                    log.info("Kết quả tìm lệnh sau khi refresh cache: {}", orderOpt.isPresent() ? "Tìm thấy" : "Không tìm thấy");
                }

                if (orderOpt.isEmpty()) {
                    log.warn("Lệnh không tồn tại, orderId = {}", orderId);
                    return null;
                }
            }

            Order order = orderOpt.get();
            log.info("Đã lấy được lệnh: {}", order);

            OrderDto orderDto = orderMapper.orderToDto(order);
            log.info("Đã chuyển đổi lệnh sang DTO: {}", orderDto);

            return orderDto;
        } catch (Exception e) {
            log.error("Lấy thông tin lệnh thất bại, orderId = {}", orderId, e);
            throw e;
        }
    }

    /**
     * Lấy danh sách lệnh theo memberId và symbol
     *
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @param status   Trạng thái lệnh
     * @param page     Số trang
     * @param size     Kích thước trang
     * @return Danh sách lệnh
     */
    @Override
    public List<OrderDto> getOrders(Long memberId, String symbol, String status, int page, int size) {
        log.info("Lấy danh sách lệnh, memberId = {}, symbol = {}, status = {}, page = {}, size = {}", memberId, symbol, status, page, size);

        try {
            List<Order> orders;

            // Lấy tất cả các lệnh theo memberId và symbol
            orders = orderPersistencePort.findByMemberIdAndSymbol(memberId, Symbol.of(symbol));

            // Lọc theo trạng thái nếu có
            if (status != null && !status.isEmpty()) {
                OrderStatus orderStatus = OrderStatus.valueOf(status.toUpperCase());
                orders = orders.stream()
                        .filter(order -> order.getStatus() == orderStatus)
                        .toList();
            }

            // Phân trang
            int start = page * size;
            int end = Math.min(start + size, orders.size());

            if (start >= orders.size()) {
                return java.util.Collections.emptyList();
            }

            return orders.subList(start, end).stream()
                    .map(orderMapper::orderToDto)
                    .toList();
        } catch (Exception e) {
            log.error("Lấy danh sách lệnh thất bại", e);
            throw e;
        }
    }

    /**
     * Lấy danh sách lệnh đang hoạt động theo memberId và symbol
     *
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @return Danh sách lệnh
     */
    @Override
    public List<OrderDto> getActiveOrders(Long memberId, String symbol) {
        log.info("Lấy danh sách lệnh đang hoạt động, memberId = {}, symbol = {}", memberId, symbol);

        try {
            List<OrderStatus> activeStatuses = Arrays.asList(OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED);

            List<Order> orders = orderPersistencePort.findByMemberIdAndSymbolAndStatusIn(
                    memberId, Symbol.of(symbol), activeStatuses);

            return orders.stream()
                    .map(orderMapper::orderToDto)
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.error("Lấy danh sách lệnh đang hoạt động thất bại", e);
            throw e;
        }
    }

    /**
     * Cập nhật lệnh
     *
     * @param orderId ID của lệnh
     * @param price   Giá mới
     * @param volume  Khối lượng mới
     * @return Kết quả cập nhật lệnh
     */
    @Override
    public PlaceOrderResult updateOrder(String orderId, BigDecimal price, BigDecimal volume) {
        log.info("Cập nhật lệnh, orderId = {}, price = {}, volume = {}", orderId, price, volume);

        try {
            // Tìm lệnh trong cơ sở dữ liệu
            Optional<Order> orderOpt = orderPersistencePort.findByOrderId(OrderId.of(orderId));

            if (orderOpt.isEmpty()) {
                log.warn("Lệnh không tồn tại, orderId = {}", orderId);
                throw new EntityNotFoundException("Lệnh không tồn tại: " + orderId);
            }

            Order order = orderOpt.get();

            // Kiểm tra xem lệnh có thể cập nhật không
            if (order.getStatus() != OrderStatus.NEW && order.getStatus() != OrderStatus.PARTIALLY_FILLED) {
                log.warn("Lệnh không thể cập nhật do trạng thái không hợp lệ, orderId = {}, status = {}", orderId, order.getStatus());
                throw new ValidationException("Lệnh không thể cập nhật do trạng thái không hợp lệ: " + order.getStatus());
            }

            // Tạo lệnh mới với thông tin cập nhật
            Order updatedOrder = Order.builder()
                    .orderId(order.getOrderId())
                    .memberId(order.getMemberId())
                    .symbol(order.getSymbol())
                    .direction(order.getDirection())
                    .type(order.getType())
                    .price(Money.of(price))
                    .volume(volume)
                    .dealVolume(order.getDealVolume())
                    .status(order.getStatus())
                    .createTime(order.getCreateTime())
                    .executeTime(LocalDateTime.now()) // Sử dụng executeTime thay cho updateTime
                    .build();

            // Cập nhật lệnh trong cơ sở dữ liệu
            Order savedOrder = orderPersistencePort.save(updatedOrder);

            // Gửi lệnh cập nhật đến OrderCommandProducer
            sendUpdateOrderToKafka(savedOrder);

            return PlaceOrderResult.builder()
                    .success(true)
                    .orderId(savedOrder.getOrderId().getValue())
                    .message("Cập nhật lệnh thành công")
                    .build();
        } catch (EntityNotFoundException | ValidationException e) {
            log.warn("Cập nhật lệnh thất bại: {}", e.getMessage());
            return PlaceOrderResult.builder()
                    .success(false)
                    .message(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("Cập nhật lệnh thất bại", e);
            return PlaceOrderResult.builder()
                    .success(false)
                    .message("Cập nhật lệnh thất bại: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public OrderBookDto findOrder(FindOrderRequest request) {
        log.info("findOrder==> FindOrderRequest: request = {}", request);
        if (ObjectUtils.isEmpty(request.getStatus())) {
            throw new ValidationException("Status không được để trống");
        }

        request.sanitize();

        // Kiểm tra hợp đồng
        Optional<Contract> contractOpt = contractPersistencePort.findBySymbol(Symbol.of(request.getSymbol()));
        if (contractOpt.isEmpty()) {
            throw new EntityNotFoundException("Hợp đồng không tồn tại: " + request.getSymbol());
        }

        Pageable pageable = PageRequest.of(request.getPageNo(), request.getPageSize());

        Page<OrderBookDto.PriceLevelDto> asks = orderPersistencePort.findOrder(request, OrderDirection.SELL, pageable)
                .map(entity -> OrderBookDto.PriceLevelDto.builder()
                        .volume(entity.getVolume())
                        .price(entity.getPrice())
                        .build());

        Page<OrderBookDto.PriceLevelDto> bids = orderPersistencePort.findOrder(request, OrderDirection.BUY, pageable)
                .map(entity -> OrderBookDto.PriceLevelDto.builder()
                        .volume(entity.getVolume())
                        .price(entity.getPrice())
                        .build());

        log.info("FindOrder===> asks= {}, bids = {}", asks, bids);

        return OrderBookDto.builder()
                .symbol(contractOpt.get().getSymbol().getValue())
                .asks(asks.getContent())
                .bids(bids.getContent())
                .markPrice(manageOrderMatchingUseCase.getMarkPrice(request.getSymbol()))
                .lastPrice(manageOrderMatchingUseCase.getLastPrice(request.getSymbol()))
                .build();
    }

    /**
     * Gửi lệnh cập nhật đến OrderCommandProducer
     *
     * @param order Lệnh
     */
    private void sendUpdateOrderToKafka(Order order) {
        try {
            // Gửi lệnh cập nhật đến OrderCommandProducer
            orderCommandProducer.sendUpdateOrderCommand(order);

            log.info("Đã gửi lệnh cập nhật đến OrderCommandProducer, orderId = {}", order.getOrderId().getValue());
        } catch (Exception e) {
            log.error("Gửi lệnh cập nhật đến OrderCommandProducer thất bại", e);
        }
    }
}
