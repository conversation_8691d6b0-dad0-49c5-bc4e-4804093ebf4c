Index: src/main/resources/application.yaml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>server:\r\n  port: 6060\r\n  servlet:\r\n    context-path: /future\r\n  # Cấu hình Netty\r\n  netty:\r\n    connection-timeout: 3000\r\n    max-connections: 1000\r\n    max-idle-time: 10000\r\n  # Cấu hình cơ sở dữ liệu\r\n\r\n# Cấu hình Spring\r\nspring:\r\n  application:\r\n    name: futures-core\r\n\r\n  # Cấu hình bộ nhớ đệm (từ application-no-cache.yaml)\r\n  cache:\r\n    type: none\r\n    jcache:\r\n      config:\r\n    caffeine:\r\n      spec:\r\n\r\n  datasource:\r\n    url: ${DATASOURCE_URL:**********************************************}\r\n    username: ${DATASOURCE_USERNAME:future_user}\r\n    password: ${DATASOURCE_PASSWORD:OZrB4ysfaHJkWApoTg5EAHlbIkXYhJE97mTX70pTEL1uyEw9yNYH8MA3Gk3gRPFu}\r\n    driver-class-name: org.postgresql.Driver\r\n    hikari:\r\n      # Cấu hình nhóm kết nối cơ bản - tối ưu cho thông lượng cao (từ application-optimized-db.yaml)\r\n      minimum-idle: 15\r\n      maximum-pool-size: 50\r\n      pool-name: HikariCP\r\n      schema: public\r\n\r\n      # Cấu hình vòng đời kết nối (từ application-optimized-db.yaml)\r\n      idle-timeout: 180000 # 3 phút\r\n      max-lifetime: 600000 # 10 phút\r\n\r\n      # Cấu hình lấy kết nối (từ application-optimized-db.yaml)\r\n      connection-timeout: 30000 # 30 giây\r\n      initialization-fail-timeout: 2000 # 2 giây\r\n\r\n      # Cấu hình xác thực kết nối (từ application-optimized-db.yaml)\r\n      validation-timeout: 3000 # 3 giây\r\n      keepalive-time: 60000 # 1 phút\r\n\r\n      # Cấu hình chất lượng kết nối (từ application-optimized-db.yaml)\r\n      auto-commit: false\r\n      transaction-isolation: TRANSACTION_READ_COMMITTED\r\n\r\n      # Giám sát và gỡ lỗi (từ application-optimized-db.yaml)\r\n      leak-detection-threshold: 30000 # 30 giây\r\n      register-mbeans: true\r\n\r\n      # Kiểm tra kết nối (từ application-optimized-db.yaml)\r\n      connection-test-query: SELECT 1\r\n      test-while-idle: true\r\n      test-on-borrow: true\r\n\r\n      # Khởi tạo kết nối (từ application-optimized-db.yaml)\r\n      connection-init-sql: SET statement_timeout = 30000; SET lock_timeout = 10000;\r\n\r\n      # Tối ưu hóa đặc biệt cho PostgreSQL (từ application-optimized-db.yaml)\r\n      data-source-properties:\r\n        # Lưu trữ câu lệnh\r\n        cachePrepStmts: true\r\n        prepStmtCacheSize: 500\r\n        prepStmtCacheSqlLimit: 4096\r\n\r\n        # Chuẩn bị câu lệnh phía server\r\n        useServerPrepStmts: true\r\n\r\n        # Xử lý hàng loạt\r\n        rewriteBatchedStatements: true\r\n\r\n        # Lưu trữ siêu dữ liệu\r\n        cacheResultSetMetadata: true\r\n        cacheServerConfiguration: true\r\n\r\n        # Quản lý kết nối\r\n        useLocalSessionState: true\r\n        elideSetAutoCommits: true\r\n        maintainTimeStats: false\r\n\r\n        # Cấu hình mạng\r\n        tcpKeepAlive: true\r\n        socketTimeout: 30\r\n        connectTimeout: 10\r\n\r\n        # Tên ứng dụng cho giám sát\r\n        ApplicationName: futures-core\r\n\r\n  jpa:\r\n    database-platform: org.hibernate.dialect.PostgreSQLDialect\r\n    hibernate:\r\n      ddl-auto: none\r\n    database: postgresql\r\n    show-sql: ${JPA_SHOW_SQL:false}\r\n    open-in-view: false\r\n    properties:\r\n      hibernate:\r\n        # Định dạng SQL\r\n        format_sql: ${JPA_FORMAT_SQL:false}\r\n\r\n        # Cấu hình JDBC (từ application-optimized-db.yaml)\r\n        jdbc:\r\n          lob:\r\n            non_contextual_creation: true\r\n          batch_size: 100\r\n          batch_versioned_data: true\r\n          time_zone: UTC\r\n          # Statement timeout (in milliseconds)\r\n          statement_timeout: 30000\r\n\r\n        # Cấu hình phương ngữ\r\n        dialect: org.hibernate.dialect.PostgreSQLDialect\r\n        legacy_limit_handler: true\r\n        default_schema: public\r\n\r\n        # Xử lý hàng loạt (từ application-optimized-db.yaml)\r\n        order_inserts: true\r\n        order_updates: true\r\n        default_batch_fetch_size: 200\r\n\r\n        # Tối ưu hóa truy vấn (từ application-optimized-db.yaml)\r\n        query:\r\n          in_clause_parameter_padding: true\r\n          fail_on_pagination_over_collection_fetch: true\r\n          plan_cache_max_size: 4096\r\n          plan_parameter_metadata_max_size: 256\r\n\r\n        # Quản lý kết nối - tối ưu cho độ ổn định (từ application-optimized-db.yaml)\r\n        connection:\r\n          provider_disables_autocommit: false\r\n          handling_mode: DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION\r\n          autoReconnect: true\r\n          autoReconnectForPools: true\r\n          is-connection-validation-required: true\r\n          # Aggressive timeout checking\r\n          checkout_timeout: 10000\r\n          # Retry failed connections\r\n          retry_attempts: 3\r\n          retry_delay: 1000\r\n\r\n        # Cấu hình hiệu suất (từ application-optimized-db.yaml)\r\n        generate_statistics: false\r\n\r\n        # Giám sát phiên\r\n        session:\r\n          events:\r\n            log:\r\n              LOG_QUERIES_SLOWER_THAN_MS: 1000\r\n\r\n        # Bộ nhớ đệm cấp hai (từ application-no-cache.yaml)\r\n        cache:\r\n          use_second_level_cache: false\r\n          use_query_cache: false\r\n          region:\r\n            factory_class:\r\n\r\n        # Chiến lược đặt tên vật lý\r\n        physical_naming_strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl\r\n\r\n        # Tối ưu hóa lấy dữ liệu (từ application-optimized-db.yaml)\r\n        max_fetch_depth: 3\r\n\r\n        # Gộp câu lệnh\r\n        jdbc.batch_versioned_data: true\r\n\r\n  main:\r\n    allow-bean-definition-overriding: true\r\n\r\n  # Cấu hình Redis\r\n  data:\r\n    redis:\r\n      host: ${REDIS_HOST:************}\r\n      port: ${REDIS_PORT:30679}\r\n#      password: 2m0881Xc30Wh\r\n      database: ${REDIS_DATABASE:0}\r\n      timeout: ${REDIS_TIMEOUT:5000}\r\n      cache:\r\n        enabled: false # từ application-no-cache.yaml\r\n    mongodb:\r\n      uri: ${SPRING_MONGODB_URI:**************************************************}\r\n      database: future\r\n\r\n  # Bộ nhớ đệm Freemarker (từ application-no-cache.yaml)\r\n  freemarker:\r\n    cache: false\r\n\r\n  kafka:\r\n    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:30092}\r\n    properties:\r\n      security.protocol: PLAINTEXT\r\n    listener:\r\n      type: single\r\n      concurrency: 9\r\n      ack-mode: manual\r\n    producer:\r\n      retries: ${KAFKA_PRODUCER_RETRIES:3}\r\n      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:256}\r\n      linger: ${KAFKA_PRODUCER_LINGER:1}\r\n      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}\r\n      key-serializer: org.apache.kafka.common.serialization.StringSerializer\r\n      value-serializer: org.apache.kafka.common.serialization.StringSerializer\r\n    consumer:\r\n      group-id: ${KAFKA_CONSUMER_GROUP_ID:future-group}\r\n      enable-auto-commit: false  # Disable auto-commit để sử dụng manual ack\r\n      session-timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}\r\n      auto-commit-interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}\r\n      auto-offset-reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}\r\n      max-poll-records: 50\r\n      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer\r\n      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer\r\n    topic:\r\n      replication-factor: ${KAFKA_REPLICATION_FACTOR:1}\r\n      partitions: ${KAFKA_PARTITIONS:1}\r\n\r\n  security:\r\n    oauth2:\r\n      resource-server:\r\n        jwt:\r\n          issuer-uri: http://************:32082/realms/cex-lotus\r\n      client:\r\n        registration:\r\n          keycloak:\r\n            client-id: internal-service\r\n            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e\r\n            authorization-grant-type: client_credentials\r\n            scope: openid\r\n        provider:\r\n          keycloak:\r\n            issuer-uri: http://************:32082/realms/cex-lotus\r\n            token-uri: http://************:32082/realms/cex-lotus/protocol/openid-connect/token\r\n\r\n# Snapshot management configuration\r\nsnapshot:\r\n  # Change-based snapshot triggers\r\n  change:\r\n    threshold: 100                    # Save snapshot after N order changes\r\n  time:\r\n    threshold:\r\n      minutes: 5                      # Save snapshot after N minutes regardless\r\n  enabled: true                       # Enable/disable change-based snapshots\r\n\r\n  # Cleanup configuration\r\n  cleanup:\r\n    enabled: true                     # Enable/disable automatic cleanup\r\n    retention:\r\n      days: 7                         # Keep snapshots for N days\r\n    max:\r\n      per:\r\n        symbol: 100                   # Maximum snapshots per symbol\r\n    batch:\r\n      size: 50                        # Delete N snapshots per batch\r\n\r\n# Chủ đề Kafka\r\ntopic-kafka:\r\n  contract:\r\n    order-new: contract-order-new\r\n    order-cancel: contract-order-cancel\r\n    order-completed: contract-order-completed\r\n    trade: contract-trade\r\n    trade-plate: contract-trade-plate\r\n    position: contract-position\r\n    mark-price: contract-mark-price\r\n    index-price: contract-index-price\r\n    funding-rate: contract-funding-rate\r\n    liquidation: contract-liquidation\r\n    order-commands: order-commands\r\n    order-events: order-events\r\n    order-routing: order-routing  # NEW - Topic cho intelligent sharding\r\n    last-price: contract-last-price\r\n  minus:\r\n    wallet-spot: minus-balance-wallet-spot\r\n\r\n# Cấu hình định giá hợp đồng\r\ncontract:\r\n  pricing:\r\n    cache-update-interval: 5000\r\n    slow-cache-update-interval: 5000\r\n    realtime-update-enabled: true\r\n    realtime-update-interval: 5000\r\n    price-fluctuation-range: 0.05\r\n    price-change-probability: 30\r\n    cache-update-enabled: true\r\n    index-price-update-interval: 1000\r\n    mark-price-update-interval: 1000\r\n    funding-rate-update-interval: 60000\r\n    market-data-update-cron: 0 0 * * * *\r\n  kline:\r\n    realtime-update-enabled: true\r\n    realtime-update-interval: 20\r\n    # Danh sách các khung thời gian được cập nhật theo thời gian thực\r\n    realtime-update-periods: 1min,5min,15min,30min,60min,4hour,1day,1week,1mon\r\n  special-order:\r\n    trigger-process-interval: 1000\r\n    time-process-interval: 5000\r\n  liquidation:\r\n    check-interval: 5000\r\n    max-positions-per-batch: 100\r\n    enabled: false  # TẮT THANH LÝ TỰ ĐỘNG\r\n  last-price:\r\n    # Cấu hình đồng bộ giá cuối cùng giữa các instance\r\n    sync-enabled: true\r\n    # Thời gian cache Redis (giờ)\r\n    redis-cache-ttl: 24\r\n    # Khoảng thời gian lưu định kỳ vào database (phút)\r\n    periodic-save-interval: 5\r\n    # Bật/tắt tự động khôi phục giá cuối cùng khi khởi động\r\n    auto-recovery-enabled: true\r\n    # Timeout cho Redis operations (ms)\r\n    redis-timeout: 3000\r\n\r\n# Cấu hình nhóm luồng\r\nthread-pool:\r\n  core-size: 10\r\n  max-size: 20\r\n  queue-capacity: 100\r\n  keep-alive-seconds: 60\r\n\r\n# Cấu hình bộ ngắt mạch\r\ncircuit-breaker:\r\n  enabled: true\r\n  failure-threshold: 5\r\n  reset-timeout-seconds: 60\r\n\r\n# Cấu hình bộ tạo ID Snowflake\r\nsnowflake:\r\n  worker-id: ${SNOWFLAKE_WORKER_ID:-1}\r\n\r\n# Cấu hình phân tích truy vấn\r\nquery:\r\n  analyzer:\r\n    enabled: true\r\n    n-plus-one-threshold: 10\r\n    slow-query-threshold: 1000\r\n  limiter:\r\n    enabled: true\r\n    default-limit: 1000\r\n    max-limit: 10000\r\n\r\n# Cấu hình WebClient\r\nwebclient:\r\n  timeout:\r\n    connect: 5000\r\n    read: 5000\r\n    write: 5000\r\n    response: 10000\r\n  max-in-memory-size: 10485760\r\n  max-connections: 500\r\n  max-idle-time: 30\r\n  acquire-timeout: 45\r\n\r\n# Cấu hình thử lại\r\nretry:\r\n  max-attempts: 3\r\n  wait-duration: 1000\r\n  max-wait-duration: 5000\r\n  wait-duration-multiplier: 1.5\r\n\r\n# Cấu hình ghi log\r\nlogging:\r\n  level:\r\n    root: INFO\r\n    com.icetea.lotus: INFO\r\n    org.springframework.kafka: INFO\r\n    org.apache.kafka: INFO\r\n    org.hibernate: INFO\r\n    org.springframework.data.redis: INFO # từ application-no-cache.yaml\r\n    org.springframework.cache: INFO # từ application-no-cache.yaml\r\n    com.icetea.lotus.infrastructure.cache.RedisDataService: INFO # từ application-no-cache.yaml\r\n    # Optimization components logging\r\n    com.icetea.lotus.infrastructure.pool: INFO\r\n    com.icetea.lotus.infrastructure.batch: INFO\r\n    com.icetea.lotus.infrastructure.monitoring: INFO\r\n    com.icetea.lotus.infrastructure.matching.SegmentedMatchingEngine: INFO\r\n  file:\r\n    name: logs/contract-perpetual-futures-core.log\r\n  pattern:\r\n    console: \"%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n\"\r\n    file: \"%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n\"\r\n    level: \"%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]\"\r\n\r\n# Cấu hình Actuator\r\nmanagement:\r\n  tracing:\r\n    sampling:\r\n      probability: 1.0\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: health,info,prometheus,metrics\r\n  metrics:\r\n    distribution:\r\n      percentiles-histogram:\r\n        http:\r\n          server:\r\n            request: true\r\n    tags:\r\n      application: ${spring.application.name}\r\n  endpoint:\r\n    health:\r\n      show-details: always\r\n      probes:\r\n        enabled: true\r\n    livenessState:\r\n      enabled: true\r\n    readinessState:\r\n      enabled: true\r\n  prometheus:\r\n    metrics:\r\n      export:\r\n        enabled: true\r\n\r\n# Cấu hình Resilience4j Circuit Breaker\r\nresilience4j:\r\n  circuitbreaker:\r\n    instances:\r\n      orderService:\r\n        registerHealthIndicator: true\r\n        slidingWindowSize: 100\r\n        minimumNumberOfCalls: 10\r\n        permittedNumberOfCallsInHalfOpenState: 5\r\n        automaticTransitionFromOpenToHalfOpenEnabled: true\r\n        waitDurationInOpenState: 5s\r\n        failureRateThreshold: 50\r\n        eventConsumerBufferSize: 10\r\n  ratelimiter:\r\n    instances:\r\n      orderService:\r\n        registerHealthIndicator: true\r\n        limitForPeriod: 100\r\n        limitRefreshPeriod: 1s\r\n        timeoutDuration: 0s\r\n        eventConsumerBufferSize: 10\r\n  retry:\r\n    instances:\r\n      orderService:\r\n        maxAttempts: 3\r\n        waitDuration: 1s\r\n        enableExponentialBackoff: true\r\n        exponentialBackoffMultiplier: 2\r\n        retryExceptions: org.springframework.web.client.HttpServerErrorException\r\n        ignoreExceptions: org.springframework.web.client.HttpClientErrorException\r\n        eventConsumerBufferSize: 10\r\n\r\n# Cấu hình dịch vụ thị trường\r\nmarket:\r\n  service:\r\n    name: market\r\n  websocket:\r\n    path: /market/market-ws\r\n\r\n# Cấu hình thị trường giao ngay\r\nspot:\r\n  market:\r\n    tracked-symbols: BTC/USDT\r\n\r\n# Cấu hình symbol sharding cho future-core\r\nfuture:\r\n  sharding:\r\n    # Danh sách symbols được gán cho pod này\r\n    pod-symbols: ${FUTURE_POD_SYMBOLS:BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,XRP/USDT,SOL/USDT,DOT/USDT,DOGE/USDT,AVAX/USDT,MATIC/USDT,LINK/USDT,UNI/USDT,LTC/USDT,EOS/ETH,BZB/USDT,HT/USDT}\r\n    # Tự động gán symbols từ cấu hình khi không có trong Redis\r\n    auto-assign-from-config: ${FUTURE_AUTO_ASSIGN_FROM_CONFIG:true}\r\n\r\n# Cấu hình bảo mật CEX\r\ncex-security:\r\n  resource-server-enabled: true  # Enable lại để test fix\r\n  default-principal-name: \"internal-service\"\r\n  permit-all-endpoints:\r\n    - \"/contract-ws/**\"\r\n    - \"/future/contract-ws/**\"\r\n    - \"/api/v1/prices/history-kline/**\"\r\n    - \"/api/v1/prices/mark/**\"\r\n    - \"/api/v1/prices/last-price/**\"\r\n\r\n# Cấu hình Keycloak\r\nkeycloak:\r\n  auth-server-url: http://************:32082\r\n  realm: cex-lotus\r\n  resource: cex-future\r\n  credentials:\r\n    secret: T63reTvMuOAr1JDVO7Jn1yRvGr0aKhOl\r\n\r\nHOSTNAME: 1\r\n\r\n# ===================================================================\r\n# FUTURE-CORE OPTIMIZATION CONFIGURATION\r\n# Performance optimization settings for high-frequency trading\r\n# ===================================================================\r\n\r\n# Object Pool Configuration\r\nobject:\r\n  pool:\r\n    enabled: true\r\n    order:\r\n      initial-size: 10000\r\n      max-size: 50000\r\n      pre-populate: true\r\n    trade:\r\n      initial-size: 10000\r\n      max-size: 50000\r\n      pre-populate: true\r\n    list:\r\n      initial-size: 5000\r\n      max-size: 25000\r\n      pre-populate: true\r\n\r\n# Batch Processing Configuration\r\nbatch:\r\n  processor:\r\n    enabled: true\r\n    batch-size: 100\r\n    queue-size: 10000\r\n    processing-interval: 1  # milliseconds\r\n    overflow-strategy: DIRECT_PROCESSING  # DIRECT_PROCESSING, DROP, BLOCK\r\n    symbol-grouping: true\r\n    metrics-enabled: true\r\n\r\n# Segmented Matching Engine Configuration\r\nsegmented:\r\n  matching:\r\n    engine:\r\n      enabled: true\r\n      segment-count: 16\r\n      fair-locking: true\r\n      load-balancing: HASH_BASED  # HASH_BASED, ROUND_ROBIN, LEAST_LOADED\r\n      metrics-enabled: true\r\n\r\n# Memory Optimization Configuration\r\nmemory:\r\n  optimization:\r\n    enabled: true\r\n    use-order-queue: true  # Use custom OrderQueue instead of CopyOnWriteArrayList\r\n    gc-optimization: true\r\n    memory-monitoring: true\r\n\r\n# Performance Monitoring Configuration\r\nperformance:\r\n  monitoring:\r\n    enabled: true\r\n    metrics-interval: 1000  # milliseconds\r\n    symbol-metrics-interval: 5000  # milliseconds\r\n    detailed-logging: false\r\n    export-to-micrometer: true\r\n\r\n    # TPS Targets\r\n    targets:\r\n      order-tps: 1200000  # 1.2M orders per second\r\n      trade-tps: 600000   # 600K trades per second\r\n      latency-p95: 0.1    # 0.1ms P95 latency\r\n\r\n    # Alerting thresholds\r\n    alerts:\r\n      low-tps-threshold: 500000  # Alert if TPS < 500K\r\n      high-latency-threshold: 1.0  # Alert if latency > 1ms\r\n      high-error-rate-threshold: 0.01  # Alert if error rate > 1%\r\n      queue-full-threshold: 0.8  # Alert if queue > 80% full\r\n\r\n# Data Structure Optimization\r\ndata:\r\n  structure:\r\n    use-concurrent-skip-list-map: true\r\n    use-timestamped-keys: true\r\n    early-exit-optimization: true\r\n    fifo-ordering: true\r\n\r\n# Concurrency Configuration\r\nconcurrency:\r\n  optimization:\r\n    enabled: true\r\n    reduce-lock-contention: true\r\n    use-local-locks: true  # Instead of distributed locks\r\n    lock-timeout: 10000  # milliseconds\r\n    fair-locks: true\r\n\r\n# Cache Configuration\r\ncache:\r\n  optimization:\r\n    enabled: true\r\n    symbol-segment-mapping: true\r\n    order-lookup-cache: true\r\n    price-level-cache: true\r\n    cache-size: 100000\r\n\r\n# JVM Optimization Hints\r\njvm:\r\n  optimization:\r\n    gc-tuning: true\r\n    heap-size: \"4g\"\r\n    young-gen-size: \"1g\"\r\n    gc-algorithm: \"G1GC\"\r\n    gc-threads: 8\r\n\r\n    # JVM flags suggestions\r\n    flags:\r\n      - \"-XX:+UseG1GC\"\r\n      - \"-XX:MaxGCPauseMillis=10\"\r\n      - \"-XX:G1HeapRegionSize=16m\"\r\n      - \"-XX:+UnlockExperimentalVMOptions\"\r\n      - \"-XX:+UseStringDeduplication\"\r\n      - \"-XX:+OptimizeStringConcat\"\r\n\r\n# Circuit Breaker Configuration\r\ncircuit:\r\n  breaker:\r\n    enabled: true\r\n    failure-threshold: 50\r\n    timeout: 30000  # milliseconds\r\n    reset-timeout: 60000  # milliseconds\r\n\r\n# Health Check Configuration\r\nhealth:\r\n  check:\r\n    enabled: true\r\n    interval: 5000  # milliseconds\r\n    thresholds:\r\n      max-queue-size: 8000\r\n      max-latency: 5.0  # milliseconds\r\n      min-tps: 100000\r\n      max-error-rate: 0.05  # 5%\r\n\r\n# Feature Flags\r\nfeatures:\r\n  experimental:\r\n    advanced-matching-algorithms: true\r\n    predictive-caching: false\r\n    machine-learning-optimization: false\r\n    adaptive-batching: true\r\n    dynamic-segmentation: false\r\n\r\n# Production Optimization\r\nproduction:\r\n  mode: true\r\n  aggressive-optimization: true\r\n  strict-monitoring: true\r\n  auto-tuning: false  # Manual tuning preferred initially\r\n\r\n# ===================================================================\r\n# INTELLIGENT SHARDING CONFIGURATION\r\n# Advanced sharding system for high-performance trading\r\n# ===================================================================\r\n\r\n# Intelligent Sharding Configuration\r\nsharding:\r\n  intelligent:\r\n    enabled: true\r\n\r\n    # Partition Configuration\r\n    partition:\r\n      max-partitions: 8\r\n      min-partitions: 1\r\n      rebalance-interval: 60000  # 1 minute\r\n      auto-rebalance-enabled: true\r\n\r\n    # Load Balancing Configuration\r\n    load:\r\n      overload-threshold: 0.8    # 80%\r\n      underload-threshold: 0.3   # 30%\r\n      monitoring-interval: 10000 # 10 seconds\r\n      history-retention: 3600000 # 1 hour\r\n\r\n    # Routing Configuration\r\n    routing:\r\n      large-order-threshold: 10000\r\n      market-order-sequential: true\r\n      routing-timeout: 30000     # 30 seconds\r\n      max-retry-attempts: 3\r\n      price-volatility-threshold: 0.05  # 5%\r\n      market-order-ratio-threshold: 0.3 # 30%\r\n\r\n    # Monitoring Configuration\r\n    monitoring:\r\n      metrics-interval: 60000    # 1 minute\r\n      health-check-interval: 30000 # 30 seconds\r\n      statistics-retention: ******** # 24 hours\r\n      detailed-logging: false\r\n      performance-metrics: true\r\n\r\n# Pod Configuration\r\npod:\r\n  name: ${POD_NAME:default-pod}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/resources/application.yaml b/src/main/resources/application.yaml
--- a/src/main/resources/application.yaml	(revision 84040975994bed1ba285d189ae32a79dd134461a)
+++ b/src/main/resources/application.yaml	(date *************)
@@ -467,6 +467,15 @@
     # Tự động gán symbols từ cấu hình khi không có trong Redis
     auto-assign-from-config: ${FUTURE_AUTO_ASSIGN_FROM_CONFIG:true}
 
+# Cấu hình future-core
+future-core:
+  order-command-consumer:
+    # Kiểm tra ownership của symbol trước khi xử lý command (false = xử lý tất cả)
+    check-ownership: ${FUTURE_CORE_CHECK_OWNERSHIP:false}
+  order-event-consumer:
+    # Kiểm tra ownership của symbol trước khi xử lý event (false = xử lý tất cả)
+    check-ownership: ${FUTURE_CORE_EVENT_CHECK_OWNERSHIP:false}
+
 # Cấu hình bảo mật CEX
 cex-security:
   resource-server-enabled: true  # Enable lại để test fix
Index: src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderEventConsumer.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.icetea.lotus.infrastructure.messaging.consumer;\r\n\r\nimport com.fasterxml.jackson.databind.ObjectMapper;\r\nimport com.icetea.lotus.core.domain.entity.Contract;\r\nimport com.icetea.lotus.core.domain.entity.Order;\r\n\r\nimport com.icetea.lotus.core.domain.entity.Position;\r\nimport com.icetea.lotus.core.domain.entity.PositionDirection;\r\nimport com.icetea.lotus.core.domain.entity.PositionStatus;\r\nimport com.icetea.lotus.core.domain.entity.Trade;\r\nimport com.icetea.lotus.core.domain.entity.Transaction;\r\nimport com.icetea.lotus.core.domain.entity.Wallet;\r\nimport com.icetea.lotus.core.domain.repository.ContractRepository;\r\nimport com.icetea.lotus.core.domain.repository.PositionRepository;\r\nimport com.icetea.lotus.core.domain.repository.TradeRepository;\r\nimport com.icetea.lotus.core.domain.repository.TransactionRepository;\r\nimport com.icetea.lotus.core.domain.service.OrderMatchingEngineService;\r\nimport com.icetea.lotus.core.domain.service.PositionService;\r\nimport com.icetea.lotus.core.domain.service.TransactionService;\r\nimport com.icetea.lotus.core.domain.service.WalletService;\r\nimport com.icetea.lotus.core.domain.valueobject.Money;\r\nimport com.icetea.lotus.core.domain.valueobject.Symbol;\r\nimport com.icetea.lotus.core.domain.valueobject.TransactionType;\r\nimport com.icetea.lotus.infrastructure.messaging.event.OrderEvent;\r\nimport com.icetea.lotus.infrastructure.service.LiquidationCheckService;\r\nimport com.icetea.lotus.infrastructure.service.MarketDataService;\r\nimport com.icetea.lotus.infrastructure.sharding.SymbolShardingManager;\r\nimport lombok.RequiredArgsConstructor;\r\nimport lombok.SneakyThrows;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.springframework.beans.factory.annotation.Qualifier;\r\nimport org.springframework.beans.factory.annotation.Value;\r\nimport org.springframework.kafka.annotation.KafkaListener;\r\nimport org.springframework.stereotype.Component;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.math.RoundingMode;\r\nimport java.util.List;\r\nimport java.util.Optional;\r\n\r\n/**\r\n * Consumer cho OrderEvent\r\n */\r\n@Slf4j\r\n@Component\r\n@RequiredArgsConstructor\r\npublic class OrderEventConsumer {\r\n\r\n    private final SymbolShardingManager shardingManager;\r\n    private final OrderMatchingEngineService orderMatchingEngineService;\r\n\r\n    private final TradeRepository tradeRepository;\r\n\r\n    @Qualifier(\"positionPersistenceAdapter\")\r\n    private final PositionRepository positionRepository;\r\n\r\n    private final ContractRepository contractRepository;\r\n    private final TransactionRepository transactionRepository;\r\n\r\n    private final PositionService positionService;\r\n    private final WalletService walletService;\r\n    private final TransactionService transactionService;\r\n    private final MarketDataService marketDataService;\r\n    private final LiquidationCheckService liquidationCheckService;\r\n\r\n    private final ObjectMapper objectMapper;\r\n\r\n    @Value(\"${topic-kafka.contract.order-events}\")\r\n    private String orderEventsTopic;\r\n\r\n    /**\r\n     * Xử lý OrderEvent\r\n     *\r\n     * @param message OrderEvent\r\n     */\r\n    @KafkaListener(\r\n            topics = \"${topic-kafka.contract.order-events}\",\r\n            containerFactory = \"kafkaListenerContainerFactory\",\r\n            groupId = \"contract-perpetual-futures-core-event\"\r\n    )\r\n    @SneakyThrows\r\n    public void handleOrderEvent(String message) {\r\n        OrderEvent event = objectMapper.readValue(message, OrderEvent.class);\r\n\r\n        if (event == null || event.getOrder() == null || event.getOrder().getSymbol() == null) {\r\n            log.error(\"Nhận event không hợp lệ từ topic {}, event = {}\", orderEventsTopic, event);\r\n            return;\r\n        }\r\n\r\n        String symbol = event.getOrder().getSymbol().getValue();\r\n\r\n        log.debug(\"Nhận event từ topic {}, type = {}, symbol = {}, orderId = {}\",\r\n                orderEventsTopic, event.getType(), symbol, event.getOrder().getOrderId());\r\n\r\n        // Chỉ xử lý event nếu symbol được gán cho instance này\r\n        if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {\r\n            log.debug(\"Bỏ qua event: {}, symbol: {} không được gán cho pod này\", event.getType(), symbol);\r\n            return;\r\n        }\r\n\r\n        // Không cần lấy matching engine vì chúng ta sẽ sử dụng OrderMatchingEngineService\r\n\r\n        // Xử lý event theo loại\r\n        switch (event.getType()) {\r\n            case ORDER_PLACED:\r\n                log.debug(\"Xử lý event ORDER_PLACED, symbol: {}\", symbol);\r\n                // Xử lý event ORDER_PLACED\r\n                try {\r\n                    handleOrderPlacedEvent(event, symbol);\r\n                } catch (Exception e) {\r\n                    log.error(\"Lỗi khi xử lý event ORDER_PLACED, symbol = {}\", symbol, e);\r\n                }\r\n                break;\r\n\r\n            case ORDER_CANCELLED:\r\n                log.debug(\"Xử lý event ORDER_CANCELLED, symbol: {}\", symbol);\r\n                // Order đã được hủy trong matching engine, không cần gọi cancelOrder lại\r\n                // Chỉ cần log để theo dõi event\r\n                log.info(\"Order đã được hủy thành công trong matching engine, orderId: {}, symbol: {}\", \r\n                        event.getOrder().getOrderId().getValue(), symbol);\r\n                break;\r\n\r\n            case ORDER_UPDATED:\r\n                log.debug(\"Xử lý event ORDER_UPDATED, symbol: {}\", symbol);\r\n                // Cập nhật lệnh trong matching engine\r\n                // Hiện tại chưa hỗ trợ cập nhật lệnh\r\n                break;\r\n\r\n            default:\r\n                log.warn(\"Không hỗ trợ loại event: {}\", event.getType());\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Xử lý event ORDER_PLACED\r\n     *\r\n     * @param event  OrderEvent\r\n     * @param symbol Symbol\r\n     */\r\n    private void handleOrderPlacedEvent(OrderEvent event, String symbol) {\r\n        try {\r\n            // Lấy thông tin lệnh và giao dịch\r\n            Order order = event.getOrder();\r\n            List<Trade> trades = event.getTrades();\r\n\r\n            if (trades == null || trades.isEmpty()) {\r\n                log.debug(\"Không có giao dịch nào được tạo ra, orderId = {}\", order.getOrderId());\r\n                return;\r\n            }\r\n\r\n            log.debug(\"Xử lý {} giao dịch cho lệnh {}, symbol = {}\", trades.size(), order.getOrderId(), symbol);\r\n\r\n            // 1. Lưu giao dịch vào cơ sở dữ liệu (nếu chưa được lưu)\r\n            tradeRepository.saveAll(trades);\r\n\r\n            // 2. Cập nhật vị thế\r\n            for (Trade trade : trades) {\r\n                // Lấy hợp đồng\r\n                Optional<Contract> contractOpt = contractRepository.findBySymbolOptional(trade.getSymbol());\r\n                if (contractOpt.isEmpty()) {\r\n                    log.warn(\"Không tìm thấy hợp đồng cho symbol: {}\", trade.getSymbol());\r\n                    continue;\r\n                }\r\n                Contract contract = contractOpt.get();\r\n\r\n                // Cập nhật vị thế cho người mua\r\n                updatePositionForTrade(trade, trade.getBuyMemberId(), contract, PositionDirection.LONG);\r\n\r\n                // Cập nhật vị thế cho người bán\r\n                updatePositionForTrade(trade, trade.getSellMemberId(), contract, PositionDirection.SHORT);\r\n\r\n                // 3. Cập nhật ví\r\n//                updateWalletForTrade(trade, contract);\r\n\r\n                // 4. Tạo giao dịch tài chính\r\n                createTransactionsForTrade(trade);\r\n\r\n                // 5. Cập nhật dữ liệu thị trường\r\n                marketDataService.updateMarkPriceFromTrade(trade);\r\n            }\r\n\r\n            // 6. Kiểm tra thanh lý - TẮT THANH LÝ TỰ ĐỘNG\r\n            log.debug(\"Bỏ qua kiểm tra thanh lý cho symbol: {} (đã tắt thanh lý tự động)\", symbol);\r\n\r\n            // TẮT THANH LÝ TỰ ĐỘNG - Comment liquidation check\r\n            // liquidationCheckService.checkLiquidationForSymbol(symbol);\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi xử lý event ORDER_PLACED, symbol = {}\", symbol, e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Cập nhật vị thế cho giao dịch\r\n     *\r\n     * @param trade    Giao dịch\r\n     * @param memberId ID của thành viên\r\n     * @param contract Hợp đồng\r\n     */\r\n    private void updatePositionForTrade(Trade trade, Long memberId, Contract contract, PositionDirection direction) {\r\n        try {\r\n            Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(\r\n                    memberId, trade.getSymbol(), PositionStatus.OPEN);\r\n\r\n            Position updatedPosition;\r\n            if (positionOpt.isPresent()) {\r\n                Position position = positionOpt.get();\r\n\r\n                boolean isSameDirection = position.getDirection() == direction;\r\n\r\n                if (isSameDirection) {\r\n                    // Mở thêm cùng chiều: cập nhật lại entry price, volume, liquidation price\r\n                    updatedPosition = positionService.updatePositionAfterTrade(position, trade, contract);\r\n                } else {\r\n                    // Đóng bớt hoặc đóng hết\r\n                    BigDecimal currentVolume = position.getVolume();\r\n                    BigDecimal tradeVolume = trade.getVolume();\r\n\r\n                    if (tradeVolume.compareTo(currentVolume) >= 0) {\r\n                        // Đóng hết vị thế\r\n                        position.setVolume(BigDecimal.ZERO);\r\n                        position.setStatus(PositionStatus.CLOSED);\r\n                        updatedPosition = position;\r\n                    } else {\r\n                        // Đóng bớt vị thế\r\n                        updatedPosition = updatePositionAfterPartialClose(position, trade, contract);\r\n                    }\r\n\r\n                    // Tính realized PnL và cập nhật vào Wallet\r\n                    Money closePrice = trade.getPrice();\r\n                    BigDecimal closeVolumeUsdt = trade.getVolume().multiply(closePrice.getValue());\r\n                    Money realizedPnl = calculateRealizedPnl(position, closePrice, closeVolumeUsdt);\r\n\r\n                    updateWalletAfterPositionClose(\r\n                            memberId,\r\n                            realizedPnl,\r\n                            trade.getSellFee().add(trade.getBuyFee()),\r\n                            closeVolumeUsdt.divide(position.getLeverage(), 8, RoundingMode.HALF_UP)\r\n                    );\r\n                }\r\n            } else {\r\n                // Không có vị thế mở, tạo mới\r\n                log.info(\"Tạo vị thế mới cho memberId = {}, symbol = {}, direction = {}, volume = {}\",\r\n                        memberId, trade.getSymbol().getValue(), direction, trade.getVolume());\r\n                updatedPosition = positionService.createPositionFromTrade(trade, memberId, contract);\r\n                log.info(\"Đã tạo vị thế mới: id = {}, direction = {}, volume = {}, status = {}\",\r\n                        updatedPosition.getId(), updatedPosition.getDirection(),\r\n                        updatedPosition.getVolume(), updatedPosition.getStatus());\r\n            }\r\n\r\n            // Lưu vị thế\r\n            Position savedPosition = positionRepository.save(updatedPosition);\r\n            log.info(\"Đã lưu vị thế vào database: id = {}, memberId = {}, symbol = {}, direction = {}, volume = {}, status = {}\",\r\n                    savedPosition.getId(), memberId, trade.getSymbol().getValue(),\r\n                    savedPosition.getDirection(), savedPosition.getVolume(), savedPosition.getStatus());\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi cập nhật vị thế cho giao dịch, tradeId = {}, memberId = {}\", trade.getId(), memberId, e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n\r\n    public Position updatePositionAfterPartialClose(Position position, Trade trade, Contract contract) {\r\n        BigDecimal currentVolume = position.getVolume();\r\n        BigDecimal tradeVolume = trade.getVolume();\r\n\r\n        // Volume còn lại sau khi đóng bớt\r\n        BigDecimal remainingVolume = currentVolume.subtract(tradeVolume);\r\n\r\n        if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {\r\n            // Nếu đóng hết vị thế, đánh dấu đóng\r\n            position.setVolume(BigDecimal.ZERO);\r\n            position.setStatus(PositionStatus.CLOSED);\r\n            return position;\r\n        }\r\n\r\n        // Đóng bớt: KHÔNG cập nhật entry price!\r\n        // Giữ nguyên entry price cũ, chỉ giảm volume và cập nhật liquidation price\r\n        position.setVolume(remainingVolume);\r\n\r\n        // Cập nhật liquidation price dựa trên entry price cũ, leverage, contract\r\n        Money newLiquidationPrice = calculateLiquidationPrice(\r\n                position.getOpenPrice(),\r\n                position.getDirection(),\r\n                position.getLeverage(),\r\n                contract\r\n        );\r\n        position.setLiquidationPrice(newLiquidationPrice);\r\n\r\n        // Nếu có break even price, có thể tính lại ở đây nếu cần\r\n\r\n        return position;\r\n    }\r\n\r\n    public void updateWalletAfterPositionClose(Long memberId, Money realizedPnl, Money closeFee, BigDecimal marginReleased) {\r\n        // 1. Lấy ví của member\r\n        Wallet wallet = walletService.findOrCreateWallet(memberId, \"USDT\");\r\n        if (wallet == null) {\r\n            throw new RuntimeException(\"Không tìm thấy ví cho memberId: \" + memberId);\r\n        }\r\n\r\n        // 2. Cộng realized PnL vào ví\r\n        wallet.setBalance(wallet.getBalance().add(realizedPnl));\r\n\r\n        // 3. Trừ phí đóng lệnh\r\n        wallet.setBalance(wallet.getBalance().subtract(closeFee));\r\n\r\n        // 4. Cộng lại phần margin đã giải phóng (marginReleased)\r\n        wallet.setBalance(wallet.getBalance().add(Money.of(marginReleased)));\r\n\r\n        // 5. Lưu lại ví\r\n        walletService.save(wallet);\r\n    }\r\n\r\n\r\n//    private void updatePositionForTrade(Trade trade, Long memberId, Contract contract) {\r\n//        try {\r\n//            log.debug(\"Cập nhật vị thế cho giao dịch, tradeId = {}, memberId = {}, symbol = {}\",\r\n//                    trade.getId(), memberId, trade.getSymbol());\r\n//\r\n//            // 1. Tìm vị thế đang mở hiện tại của thành viên\r\n//            Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(\r\n//                    memberId, trade.getSymbol(), PositionStatus.OPEN);\r\n//\r\n//            Position updatedPosition;\r\n//            if (positionOpt.isPresent()) {\r\n//                // 2. Nếu có vị thế đang mở, cập nhật vị thế dựa trên giao dịch mới\r\n//                Position position = positionOpt.get();\r\n//\r\n//                // Ghi log thông tin chi tiết về vị thế trước khi cập nhật ở cấp độ debug\r\n//                if (log.isDebugEnabled()) {\r\n//                    log.debug(\"Chi tiết vị thế trước khi cập nhật: memberId={}, symbol={}, direction={}, volume={}, \" +\r\n//                                    \"entryPrice={}, liquidationPrice={}, leverage={}\",\r\n//                            memberId, trade.getSymbol().getValue(), position.getDirection(),\r\n//                            position.getVolume(), position.getOpenPrice().getValue(),\r\n//                            position.getLiquidationPrice() != null ? position.getLiquidationPrice().getValue() : \"N/A\",\r\n//                            position.getLeverage());\r\n//                }\r\n//\r\n//                // Cập nhật vị thế dựa trên giao dịch mới\r\n//                // PositionServiceImpl sẽ tự động tính toán entry price, break even price, và các giá trị khác\r\n//\r\n//                updatedPosition = positionService.updatePositionAfterTrade(position, trade, contract);\r\n//                log.debug(\"Đã cập nhật vị thế hiện tại cho memberId = {}, symbol = {}, volume = {}, status = {}\",\r\n//                        memberId, trade.getSymbol(), updatedPosition.getVolume(), updatedPosition.getStatus());\r\n//\r\n//                // Ghi log thông tin chi tiết về vị thế đã cập nhật\r\n//                if (updatedPosition.getStatus() == PositionStatus.OPEN && log.isDebugEnabled()) {\r\n//                    // Tính toán break even price với phí giao dịch (giả sử 0.1%)\r\n//                    BigDecimal feeRate = new BigDecimal(\"0.001\"); // 0.1%\r\n//                    Money breakEvenPrice = updatedPosition.calculateBreakEvenPrice(feeRate);\r\n//\r\n//                    log.debug(\"Chi tiết vị thế sau khi cập nhật: memberId={}, symbol={}, direction={}, volume={}, \" +\r\n//                                    \"entryPrice={}, liquidationPrice={}, breakEvenPrice={}, leverage={}\",\r\n//                            memberId, trade.getSymbol().getValue(), updatedPosition.getDirection(),\r\n//                            updatedPosition.getVolume(), updatedPosition.getOpenPrice().getValue(),\r\n//                            updatedPosition.getLiquidationPrice().getValue(), breakEvenPrice.getValue(),\r\n//                            updatedPosition.getLeverage());\r\n//                }\r\n//            } else {\r\n//                // 3. Nếu không có vị thế đang mở, tạo vị thế mới\r\n//                updatedPosition = positionService.createPositionFromTrade(trade, memberId, contract);\r\n//                log.debug(\"Đã tạo vị thế mới cho memberId = {}, symbol = {}, volume = {}, status = {}\",\r\n//                        memberId, trade.getSymbol(), updatedPosition.getVolume(), updatedPosition.getStatus());\r\n//\r\n//                // Ghi log thông tin chi tiết về vị thế mới\r\n//                if (updatedPosition.getStatus() == PositionStatus.OPEN) {\r\n//                    // Tính toán break even price với phí giao dịch (giả sử 0.1%)\r\n//                    BigDecimal feeRate = new BigDecimal(\"0.001\"); // 0.1%\r\n//                    Money breakEvenPrice = updatedPosition.calculateBreakEvenPrice(feeRate);\r\n//\r\n//                    log.debug(\"Chi tiết vị thế mới: memberId={}, symbol={}, direction={}, volume={}, \" +\r\n//                                    \"entryPrice={}, liquidationPrice={}, breakEvenPrice={}, leverage={}\",\r\n//                            memberId, trade.getSymbol().getValue(), updatedPosition.getDirection(),\r\n//                            updatedPosition.getVolume(), updatedPosition.getOpenPrice().getValue(),\r\n//                            updatedPosition.getLiquidationPrice().getValue(), breakEvenPrice.getValue(),\r\n//                            updatedPosition.getLeverage());\r\n//                }\r\n//            }\r\n//\r\n//            // 4. Lưu vị thế vào cơ sở dữ liệu\r\n//            Position savedPosition = positionRepository.save(updatedPosition);\r\n//            log.debug(\"Đã lưu vị thế vào cơ sở dữ liệu, id = {}, memberId = {}, symbol = {}, status = {}\",\r\n//                    savedPosition.getId(), memberId, trade.getSymbol(), savedPosition.getStatus());\r\n//\r\n//        } catch (Exception e) {\r\n//            log.error(\"Lỗi khi cập nhật vị thế cho giao dịch, tradeId = {}, memberId = {}\", trade.getId(), memberId, e);\r\n//            throw e;\r\n//        }\r\n//    }\r\n\r\n    /**\r\n     * Tính toán giá thanh lý (Liquidation Price) dựa trên công thức chuẩn.\r\n     * <p>\r\n     * Công thức:\r\n     * <ul>\r\n     *   <li>Đối với vị thế <b>LONG</b>:\r\n     *     <pre>\r\n     *     liquidationPrice = entryPrice × (1 - 1 / leverage)\r\n     *     </pre>\r\n     *   </li>\r\n     *   <li>Đối với vị thế <b>SHORT</b>:\r\n     *     <pre>\r\n     *     liquidationPrice = entryPrice × (1 + 1 / leverage)\r\n     *     </pre>\r\n     *   </li>\r\n     * </ul>\r\n     *\r\n     * @param entryPrice Giá vào lệnh (Entry Price)\r\n     * @param direction Hướng vị thế (LONG hoặc SHORT)\r\n     * @param leverage Đòn bẩy sử dụng\r\n     * @param contract Thông tin hợp đồng, có thể sử dụng thêm để điều chỉnh logic (nếu cần)\r\n     * @return Giá thanh lý (Liquidation Price) dưới dạng {@link Money}\r\n     */\r\n    private Money calculateLiquidationPrice(Money entryPrice, PositionDirection direction, BigDecimal leverage, Contract contract) {\r\n        // Kiểm tra leverage không bằng 0 để tránh lỗi chia cho 0\r\n        if (leverage == null || leverage.compareTo(BigDecimal.ZERO) == 0) {\r\n            throw new IllegalArgumentException(\"Leverage must be greater than zero\");\r\n        }\r\n\r\n        // ✅ SỬ DỤNG công thức đúng từ Position entity thay vì công thức sai\r\n        BigDecimal volume = BigDecimal.ONE; // Normalized volume\r\n\r\n        // Tính margin = (Entry Price × Volume) / Leverage\r\n        Money margin = Money.of(entryPrice.getValue().multiply(volume).divide(leverage, 8, RoundingMode.HALF_UP));\r\n\r\n        // Tạo temporary position để sử dụng công thức đúng\r\n        Position tempPosition = Position.builder()\r\n                .direction(direction)\r\n                .openPrice(entryPrice)\r\n                .volume(volume)\r\n                .margin(margin)\r\n                .build();\r\n\r\n        // Sử dụng công thức chính xác từ Position entity\r\n        return tempPosition.calculateLiquidationPrice(contract.getMaintenanceMarginRate().getValue());\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * Cập nhật ví cho giao dịch futures theo cơ chế Binance\r\n     *\r\n     * @param trade    Giao dịch đã khớp\r\n     * @param contract Hợp đồng futures\r\n     */\r\n//    private void updateWalletForTrade(Trade trade, Contract contract) {\r\n//        try {\r\n//            log.info(\"Bắt đầu cập nhật ví cho giao dịch futures, tradeId = {}\", trade.getId());\r\n//\r\n//            // Cập nhật ví cho bên mua (Long)\r\n//            updateWalletForMember(trade, trade.getBuyMemberId(), trade.getBuyFee(), contract, OrderDirection.BUY);\r\n//\r\n//            // Cập nhật ví cho bên bán (Short)\r\n//            updateWalletForMember(trade, trade.getSellMemberId(), trade.getSellFee(), contract, OrderDirection.SELL);\r\n//\r\n//            log.info(\"Hoàn thành cập nhật ví cho giao dịch futures, tradeId = {}\", trade.getId());\r\n//        } catch (Exception e) {\r\n//            log.error(\"Lỗi khi cập nhật ví cho giao dịch futures, tradeId = {}\", trade.getId(), e);\r\n//            throw new RuntimeException(\"Failed to update wallet for futures trade\", e);\r\n//        }\r\n//    }\r\n\r\n    /**\r\n     * Cập nhật ví cho thành viên theo cơ chế Binance Futures\r\n     *\r\n     * @param trade     Giao dịch\r\n     * @param memberId  ID của thành viên\r\n     * @param fee       Phí giao dịch\r\n     * @param contract  Hợp đồng\r\n     * @param orderSide BUY hoặc SELL\r\n     */\r\n//    private void updateWalletForMember(Trade trade, Long memberId, Money fee, Contract contract, OrderDirection orderSide) {\r\n//        try {\r\n//            log.debug(\"Cập nhật ví futures cho thành viên, tradeId = {}, memberId = {}, side = {}, fee = {}\",\r\n//                    trade.getId(), memberId, orderSide, fee);\r\n//\r\n//            // 1. Tìm hoặc tạo ví USDT (Quote coin cho futures)\r\n//            Wallet wallet = walletService.findOrCreateWallet(memberId, contract.getQuoteCoin());\r\n//\r\n//            // 2. Tìm vị thế hiện tại của member\r\n//            Optional<Position> existingPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(\r\n//                    memberId, trade.getSymbol(), PositionStatus.OPEN);\r\n//\r\n//            // 3. Tính toán margin yêu cầu cho trade này\r\n//            BigDecimal leverage = BigDecimal.valueOf(contract.getLeverageMax());\r\n//            Money requiredMargin = calculateRequiredMarginFromUsdt(trade.getVolume(), leverage);\r\n//\r\n//            if (existingPositionOpt.isPresent()) {\r\n//                // Có vị thế đang mở - xử lý tăng/giảm vị thế hoặc đóng vị thế\r\n//                Position existingPosition = existingPositionOpt.get();\r\n//                handleExistingPosition(wallet, trade, existingPosition, fee, orderSide, requiredMargin, contract);\r\n//            } else {\r\n//                // Không có vị thế - mở vị thế mới\r\n//                handleNewPosition(wallet, trade, fee, requiredMargin);\r\n//            }\r\n//\r\n//            log.debug(\"Hoàn thành cập nhật ví futures cho thành viên, memberId = {}\", memberId);\r\n//\r\n//        } catch (Exception e) {\r\n//            log.error(\"Lỗi khi cập nhật ví futures cho thành viên, tradeId = {}, memberId = {}\",\r\n//                    trade.getId(), memberId, e);\r\n//            throw e;\r\n//        }\r\n//    }\r\n\r\n    /**\r\n     * Xử lý khi đã có vị thế đang mở\r\n     */\r\n//    private void handleExistingPosition(Wallet wallet, Trade trade, Position existingPosition,\r\n//                                        Money fee, OrderDirection orderSide, Money requiredMargin, Contract contract) {\r\n//\r\n//        PositionDirection existingSide = existingPosition.getDirection();\r\n//        PositionDirection tradeSide = (orderSide == OrderDirection.BUY) ? PositionDirection.LONG : PositionDirection.SHORT;\r\n//\r\n//        if (existingSide == tradeSide) {\r\n//            // Cùng chiều - tăng vị thế (Add to position)\r\n//            handleIncreasePosition(wallet, trade, existingPosition, fee, requiredMargin);\r\n//        } else {\r\n//            // Ngược chiều - giảm vị thế hoặc đóng vị thế (Reduce/Close position)\r\n//            handleReduceOrClosePosition(wallet, trade, existingPosition, fee, requiredMargin, contract);\r\n//        }\r\n//    }\r\n\r\n    /**\r\n     * Xử lý tăng vị thế (Add to position)\r\n     */\r\n//    private void handleIncreasePosition(Wallet wallet, Trade trade, Position existingPosition,\r\n//                                        Money fee, Money requiredMargin) {\r\n//\r\n//        log.debug(\"Tăng vị thế, memberId = {}, currentVolume = {}, addVolume = {}\",\r\n//                wallet.getMemberId(), existingPosition.getVolume(), trade.getVolume());\r\n//\r\n//        // 1. Trừ phí giao dịch từ available balance\r\n//        wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), fee);\r\n//\r\n//        // 2. Đóng băng margin bổ sung\r\n//        wallet = walletService.freezeBalance(wallet.getMemberId(), wallet.getCoin(), requiredMargin);\r\n//\r\n//        // 3. Cập nhật total fee\r\n//        walletService.updateTotalFee(wallet.getId().getValue(), fee);\r\n//\r\n//        log.debug(\"Đã tăng vị thế, frozen margin = {}, fee = {}\", requiredMargin, fee);\r\n//    }\r\n\r\n    /**\r\n     * Xử lý giảm/đóng vị thế (Reduce/Close position)\r\n     */\r\n//    private void handleReduceOrClosePosition(Wallet wallet, Trade trade, Position existingPosition,\r\n//                                             Money fee, Money requiredMargin, Contract contract) {\r\n//\r\n//        BigDecimal tradeVolume = trade.getVolume();\r\n//        BigDecimal existingVolume = existingPosition.getVolume();\r\n//\r\n//        log.debug(\"Xử lý giảm/đóng vị thế, memberId = {}, existingVolume = {}, tradeVolume = {}\",\r\n//                wallet.getMemberId(), existingVolume, tradeVolume);\r\n//\r\n//        // 1. Trừ phí giao dịch\r\n//        wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), fee);\r\n//        wallet = walletService.updateTotalFee(wallet.getId().getValue(), fee);\r\n//\r\n//        if (tradeVolume.compareTo(existingVolume) >= 0) {\r\n//            // Đóng toàn bộ vị thế\r\n//            handleFullPositionClose(wallet, trade, existingPosition);\r\n//\r\n//            // Nếu volume trade > existing volume, tạo vị thế mới theo chiều ngược lại\r\n//            if (tradeVolume.compareTo(existingVolume) > 0) {\r\n//                BigDecimal remainingVolume = tradeVolume.subtract(existingVolume);\r\n//                handleNewPositionAfterClose(wallet, trade, remainingVolume, requiredMargin);\r\n//            }\r\n//        } else {\r\n//            // Giảm một phần vị thế\r\n//            handlePartialPositionClose(wallet, trade, existingPosition);\r\n//        }\r\n//    }\r\n\r\n    /**\r\n     * Đóng toàn bộ vị thế\r\n     */\r\n//    private void handleFullPositionClose(Wallet wallet, Trade trade, Position existingPosition) {\r\n//\r\n//        log.debug(\"Đóng toàn bộ vị thế, memberId = {}, volume = {}\",\r\n//                wallet.getMemberId(), existingPosition.getVolume());\r\n//\r\n//        // 1. Tính toán PnL đã thực hiện\r\n//        Money realizedPnl = calculateRealizedPnl(existingPosition, trade.getPrice(), existingPosition.getVolume());\r\n//\r\n//        // 2. Giải phóng toàn bộ margin đã đóng băng cho vị thế này\r\n//        Money positionMargin = existingPosition.getMargin();\r\n//        wallet = walletService.unfreezeBalance(wallet.getMemberId(), wallet.getCoin(), positionMargin);\r\n//\r\n//        // 3. Cập nhật PnL đã thực hiện\r\n//        wallet = walletService.updateRealizedPnl(wallet.getId().getValue(), realizedPnl);\r\n//\r\n//        // 4. Cập nhật available balance dựa trên PnL\r\n//        if (realizedPnl.getValue().compareTo(BigDecimal.ZERO) > 0) {\r\n//            // Lợi nhuận - tăng available balance\r\n//            wallet = walletService.increaseBalance(wallet.getMemberId(), wallet.getCoin(), realizedPnl);\r\n//        } else if (realizedPnl.getValue().compareTo(BigDecimal.ZERO) < 0) {\r\n//            // Lỗ - giảm available balance\r\n//            Money lossAmount = Money.of(realizedPnl.getValue().abs());\r\n//            wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), lossAmount);\r\n//        }\r\n//\r\n//        log.info(\"Đã đóng toàn bộ vị thế, realizedPnl = {}, releasedMargin = {}\",\r\n//                realizedPnl, positionMargin);\r\n//    }\r\n\r\n    /**\r\n     * Giảm một phần vị thế\r\n     */\r\n//    private void handlePartialPositionClose(Wallet wallet, Trade trade, Position existingPosition) {\r\n//\r\n//        BigDecimal closeRatio = trade.getVolume().divide(existingPosition.getVolume(), 8, RoundingMode.HALF_UP);\r\n//\r\n//        log.debug(\"Giảm một phần vị thế, memberId = {}, closeRatio = {}\",\r\n//                wallet.getMemberId(), closeRatio);\r\n//\r\n//        // 1. Tính toán PnL đã thực hiện cho phần đóng\r\n//        Money realizedPnl = calculateRealizedPnl(existingPosition, trade.getPrice(), trade.getVolume());\r\n//\r\n//        // 2. Giải phóng margin tương ứng với phần vị thế đóng\r\n//        Money releasedMargin = Money.of(existingPosition.getMargin().getValue().multiply(closeRatio));\r\n//        wallet = walletService.unfreezeBalance(wallet.getMemberId(), wallet.getCoin(), releasedMargin);\r\n//\r\n//        // 3. Cập nhật PnL đã thực hiện\r\n//        wallet = walletService.updateRealizedPnl(wallet.getId().getValue(), realizedPnl);\r\n//\r\n//        // 4. Cập nhật available balance\r\n//        if (realizedPnl.getValue().compareTo(BigDecimal.ZERO) > 0) {\r\n//            wallet = walletService.increaseBalance(wallet.getMemberId(), wallet.getCoin(), realizedPnl);\r\n//        } else if (realizedPnl.getValue().compareTo(BigDecimal.ZERO) < 0) {\r\n//            Money lossAmount = Money.of(realizedPnl.getValue().abs());\r\n//            wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), lossAmount);\r\n//        }\r\n//\r\n//        log.debug(\"Đã giảm một phần vị thế, realizedPnl = {}, releasedMargin = {}\",\r\n//                realizedPnl, releasedMargin);\r\n//    }\r\n\r\n    /**\r\n     * Xử lý mở vị thế mới\r\n     */\r\n//    private void handleNewPosition(Wallet wallet, Trade trade, Money fee, Money requiredMargin) {\r\n//\r\n//        log.debug(\"Mở vị thế mới, memberId = {}, volume = {}, margin = {}\",\r\n//                wallet.getMemberId(), trade.getVolume(), requiredMargin);\r\n//\r\n//        // 1. Trừ phí giao dịch\r\n//        wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), fee);\r\n//\r\n//        // 2. Đóng băng margin\r\n//        wallet = walletService.freezeBalance(wallet.getMemberId(), wallet.getCoin(), requiredMargin);\r\n//\r\n//        // 3. Cập nhật total fee\r\n//        wallet = walletService.updateTotalFee(wallet.getId().getValue(), fee);\r\n//\r\n//        log.debug(\"Đã mở vị thế mới, frozenMargin = {}, fee = {}\", requiredMargin, fee);\r\n//    }\r\n\r\n    /**\r\n     * Tạo vị thế mới sau khi đóng vị thế cũ (khi volume trade > existing volume)\r\n     */\r\n//    private void handleNewPositionAfterClose(Wallet wallet, Trade trade, BigDecimal remainingVolume,\r\n//                                             Money requiredMargin) {\r\n//\r\n//        log.debug(\"Tạo vị thế mới sau khi đóng, memberId = {}, remainingVolume = {}\",\r\n//                wallet.getMemberId(), remainingVolume);\r\n//\r\n//        // Tính toán margin cho volume còn lại\r\n//        BigDecimal ratio = remainingVolume.divide(trade.getVolume(), 8, RoundingMode.HALF_UP);\r\n//        Money newPositionMargin = Money.of(requiredMargin.getValue().multiply(ratio));\r\n//\r\n//        // Đóng băng margin cho vị thế mới\r\n//        wallet = walletService.freezeBalance(wallet.getMemberId(), wallet.getCoin(), newPositionMargin);\r\n//\r\n//        log.debug(\"Đã tạo vị thế mới, newMargin = {}\", newPositionMargin);\r\n//    }\r\n\r\n    /**\r\n     * Tính toán margin yêu cầu khi volume tính bằng USDT\r\n     *\r\n     * @param volumeUsdt Volume tính bằng USDT (notional value)\r\n     * @param leverage   Đòn bẩy\r\n     * @return Margin yêu cầu\r\n     */\r\n//    private Money calculateRequiredMarginFromUsdt(BigDecimal volumeUsdt, BigDecimal leverage) {\r\n//        // Margin = Volume USDT / Leverage\r\n//        BigDecimal marginValue = volumeUsdt.divide(leverage, 8, RoundingMode.HALF_UP);\r\n//        return Money.of(marginValue);\r\n//    }\r\n\r\n\r\n    /**\r\n     * Tính toán PnL đã thực hiện khi đóng vị thế\r\n     *\r\n     * @param position        Vị thế hiện tại\r\n     * @param closePrice      Giá đóng vị thế\r\n     * @param closeVolumeUsdt Volume đóng tính bằng USDT (notional value)\r\n     * @return PnL đã thực hiện\r\n     */\r\n    private Money calculateRealizedPnl(Position position, Money closePrice, BigDecimal closeVolumeUsdt) {\r\n        try {\r\n            BigDecimal entryPrice = position.getOpenPrice().getValue();\r\n            BigDecimal exitPrice = closePrice.getValue();\r\n            PositionDirection positionSide = position.getDirection();\r\n\r\n            // Volume đóng tính bằng USDT, cần chuyển đổi thành số lượng coin\r\n            // closeVolumeUsdt = closePrice * coinAmount\r\n            // => coinAmount = closeVolumeUsdt / closePrice\r\n            BigDecimal coinAmountClosed = closeVolumeUsdt.divide(exitPrice, 8, RoundingMode.HALF_UP);\r\n\r\n            // Tính toán PnL theo công thức Binance Futures\r\n            BigDecimal pnlTotal;\r\n            if (positionSide == PositionDirection.LONG) {\r\n                // Long: PnL = (Exit Price - Entry Price) * CoinAmount\r\n                pnlTotal = exitPrice.subtract(entryPrice).multiply(coinAmountClosed);\r\n            } else {\r\n                // Short: PnL = (Entry Price - Exit Price) * CoinAmount\r\n                pnlTotal = entryPrice.subtract(exitPrice).multiply(coinAmountClosed);\r\n            }\r\n\r\n            log.debug(\"Tính toán PnL với volume USDT: entryPrice = {}, exitPrice = {}, side = {}, \" +\r\n                            \"volumeUsdt = {}, coinAmount = {}, pnl = {}\",\r\n                    entryPrice, exitPrice, positionSide, closeVolumeUsdt, coinAmountClosed, pnlTotal);\r\n\r\n            return Money.of(pnlTotal);\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi tính toán PnL đã thực hiện cho position = {}\", position.getId(), e);\r\n            throw new RuntimeException(\"Failed to calculate realized PnL\", e);\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * Tạo giao dịch tài chính cho giao dịch\r\n     *\r\n     * @param trade Giao dịch\r\n     */\r\n    private void createTransactionsForTrade(Trade trade) {\r\n        try {\r\n            // Tạo giao dịch phí cho bên mua\r\n            createFeeTransaction(trade, trade.getBuyMemberId(), trade.getBuyFee());\r\n\r\n            // Tạo giao dịch phí cho bên bán\r\n            createFeeTransaction(trade, trade.getSellMemberId(), trade.getSellFee());\r\n\r\n            // Tạo giao dịch lợi nhuận nếu đóng vị thế\r\n            if (isClosingPosition(trade)) {\r\n                createPnlTransaction(trade);\r\n            }\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi tạo giao dịch tài chính cho giao dịch, tradeId = {}\", trade.getId(), e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Tạo giao dịch phí\r\n     *\r\n     * @param trade    Giao dịch\r\n     * @param memberId ID của thành viên\r\n     * @param fee      Phí giao dịch\r\n     */\r\n    private void createFeeTransaction(Trade trade, Long memberId, Money fee) {\r\n        // Sử dụng phương thức factory có sẵn trong Transaction để tạo giao dịch phí\r\n        Transaction transaction = Transaction.createFeeTransaction(\r\n                memberId,\r\n                trade.getSymbol(),\r\n                null,\r\n                fee\r\n        );\r\n\r\n        // Thiết lập referenceId là ID của giao dịch\r\n        transaction = Transaction.builder()\r\n                .id(transaction.getId())\r\n                .memberId(transaction.getMemberId())\r\n                .symbol(transaction.getSymbol())\r\n                .coin(transaction.getCoin())\r\n                .amount(transaction.getAmount())\r\n                .fee(transaction.getFee())\r\n                .type(transaction.getType())\r\n                .createTime(transaction.getCreateTime())\r\n                .flag(transaction.getFlag())\r\n                .isReward(transaction.getIsReward())\r\n                .referenceId(trade.getId().getValue())\r\n                .build();\r\n\r\n        // Lưu giao dịch\r\n        transactionRepository.save(transaction);\r\n\r\n        log.debug(\"Đã tạo giao dịch phí cho memberId = {}, fee = {}\", memberId, fee);\r\n    }\r\n\r\n    /**\r\n     * Tạo giao dịch lợi nhuận\r\n     *\r\n     * @param trade Giao dịch\r\n     */\r\n    private void createPnlTransaction(Trade trade) {\r\n        try {\r\n            log.debug(\"Tạo giao dịch lợi nhuận cho giao dịch, tradeId = {}\", trade.getId());\r\n\r\n            // 1. Tìm vị thế đang mở hiện tại của người mua và người bán\r\n            Optional<Position> buyerPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(\r\n                    trade.getBuyMemberId(), trade.getSymbol(), PositionStatus.OPEN);\r\n            Optional<Position> sellerPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(\r\n                    trade.getSellMemberId(), trade.getSymbol(), PositionStatus.OPEN);\r\n\r\n            // 2. Tính toán lợi nhuận cho người mua\r\n            if (buyerPositionOpt.isPresent()) {\r\n                Position buyerPosition = buyerPositionOpt.get();\r\n\r\n                // Kiểm tra xem đây có phải là đóng vị thế không\r\n                if (buyerPosition.getDirection() != null &&\r\n                        buyerPosition.getDirection() == PositionDirection.SHORT) {\r\n\r\n                    // Tính toán lợi nhuận dựa trên vị thế và giá đóng\r\n                    // Sử dụng positionService để tính toán lợi nhuận\r\n                    Money buyerPnl = positionService.calculateUnrealizedProfit(buyerPosition, trade.getPrice());\r\n\r\n                    // Xác định loại giao dịch (PROFIT hoặc LOSS)\r\n                    TransactionType buyerType = buyerPnl.getValue().compareTo(BigDecimal.ZERO) >= 0 ?\r\n                            TransactionType.TRADE_PROFIT : TransactionType.TRADE_LOSS;\r\n\r\n                    // Tạo giao dịch lợi nhuận cho người mua\r\n                    Money pnlAmount = buyerPnl.getValue().compareTo(BigDecimal.ZERO) < 0 ?\r\n                            Money.of(buyerPnl.getValue().abs()) : buyerPnl;\r\n\r\n                    transactionService.createTransaction(\r\n                            trade.getBuyMemberId(),\r\n                            pnlAmount,\r\n                            buyerType,\r\n                            trade.getId().getValue(),\r\n                            \"PnL for trade \" + trade.getId().getValue() + \" (buyer)\",\r\n                            trade.getSymbol()\r\n                    );\r\n\r\n                    log.debug(\"Đã tạo giao dịch lợi nhuận cho người mua, tradeId = {}, memberId = {}, amount = {}, type = {}\",\r\n                            trade.getId(), trade.getBuyMemberId(), pnlAmount, buyerType);\r\n                }\r\n            }\r\n\r\n            // 3. Tính toán lợi nhuận cho người bán\r\n            if (sellerPositionOpt.isPresent()) {\r\n                Position sellerPosition = sellerPositionOpt.get();\r\n\r\n                // Kiểm tra xem đây có phải là đóng vị thế không\r\n                if (sellerPosition.getDirection() != null &&\r\n                        sellerPosition.getDirection() == PositionDirection.LONG) {\r\n\r\n                    // Tính toán lợi nhuận dựa trên vị thế và giá đóng\r\n                    // Sử dụng positionService để tính toán lợi nhuận\r\n                    Money sellerPnl = positionService.calculateUnrealizedProfit(sellerPosition, trade.getPrice());\r\n\r\n                    // Xác định loại giao dịch (PROFIT hoặc LOSS)\r\n                    TransactionType sellerType = sellerPnl.getValue().compareTo(BigDecimal.ZERO) >= 0 ?\r\n                            TransactionType.TRADE_PROFIT : TransactionType.TRADE_LOSS;\r\n\r\n                    // Tạo giao dịch lợi nhuận cho người bán\r\n                    Money pnlAmount = sellerPnl.getValue().compareTo(BigDecimal.ZERO) < 0 ?\r\n                            Money.of(sellerPnl.getValue().abs()) : sellerPnl;\r\n\r\n                    transactionService.createTransaction(\r\n                            trade.getSellMemberId(),\r\n                            pnlAmount,\r\n                            sellerType,\r\n                            trade.getId().getValue(),\r\n                            \"PnL for trade \" + trade.getId().getValue() + \" (seller)\",\r\n                            trade.getSymbol()\r\n                    );\r\n\r\n                    log.debug(\"Đã tạo giao dịch lợi nhuận cho người bán, tradeId = {}, memberId = {}, amount = {}, type = {}\",\r\n                            trade.getId(), trade.getSellMemberId(), pnlAmount, sellerType);\r\n                }\r\n            }\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi tạo giao dịch lợi nhuận cho giao dịch, tradeId = {}\", trade.getId(), e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Kiểm tra xem giao dịch có đóng vị thế không\r\n     *\r\n     * @param trade Giao dịch\r\n     * @return true nếu đóng vị thế, false nếu không\r\n     */\r\n    private boolean isClosingPosition(Trade trade) {\r\n        try {\r\n            log.debug(\"Kiểm tra xem giao dịch có đóng vị thế không, tradeId = {}\", trade.getId());\r\n\r\n            // 1. Tìm vị thế đang mở hiện tại của thành viên\r\n            Optional<Position> buyerPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(\r\n                    trade.getBuyMemberId(), trade.getSymbol(), PositionStatus.OPEN);\r\n            Optional<Position> sellerPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(\r\n                    trade.getSellMemberId(), trade.getSymbol(), PositionStatus.OPEN);\r\n\r\n            // 2. Kiểm tra xem direction của trade có ngược với direction của vị thế không\r\n            if (buyerPositionOpt.isPresent()) {\r\n                Position buyerPosition = buyerPositionOpt.get();\r\n                // Nếu người mua đang có vị thế bán, thì đây là đóng vị thế\r\n                if (buyerPosition.getDirection() != null &&\r\n                        buyerPosition.getDirection() == PositionDirection.SHORT) {\r\n                    log.debug(\"Giao dịch đóng vị thế cho người mua, tradeId = {}, memberId = {}\",\r\n                            trade.getId(), trade.getBuyMemberId());\r\n                    return true;\r\n                }\r\n            }\r\n\r\n            if (sellerPositionOpt.isPresent()) {\r\n                Position sellerPosition = sellerPositionOpt.get();\r\n                // Nếu người bán đang có vị thế mua, thì đây là đóng vị thế\r\n                if (sellerPosition.getDirection() != null &&\r\n                        sellerPosition.getDirection() == PositionDirection.LONG) {\r\n                    log.debug(\"Giao dịch đóng vị thế cho người bán, tradeId = {}, memberId = {}\",\r\n                            trade.getId(), trade.getSellMemberId());\r\n                    return true;\r\n                }\r\n            }\r\n\r\n            // Không có vị thế nào được đóng\r\n            return false;\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi kiểm tra đóng vị thế, tradeId = {}\", trade.getId(), e);\r\n            return false;\r\n        }\r\n    }\r\n\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderEventConsumer.java b/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderEventConsumer.java
--- a/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderEventConsumer.java	(revision 84040975994bed1ba285d189ae32a79dd134461a)
+++ b/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderEventConsumer.java	(date 1752084723185)
@@ -68,6 +68,9 @@
     @Value("${topic-kafka.contract.order-events}")
     private String orderEventsTopic;
 
+    @Value("${future-core.order-event-consumer.check-ownership:false}")
+    private boolean checkSymbolOwnership;
+
     /**
      * Xử lý OrderEvent
      *
@@ -92,10 +95,17 @@
         log.debug("Nhận event từ topic {}, type = {}, symbol = {}, orderId = {}",
                 orderEventsTopic, event.getType(), symbol, event.getOrder().getOrderId());
 
-        // Chỉ xử lý event nếu symbol được gán cho instance này
-        if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
-            log.debug("Bỏ qua event: {}, symbol: {} không được gán cho pod này", event.getType(), symbol);
-            return;
+        // Kiểm tra ownership nếu được bật trong cấu hình
+        if (checkSymbolOwnership) {
+            boolean isOwned = shardingManager.isSymbolOwnedByThisPod(symbol);
+            log.info("Kiểm tra ownership cho symbol: {}, isOwned: {}, debugInfo: {}", symbol, isOwned, shardingManager.getDebugInfo());
+
+            if (!isOwned) {
+                log.debug("Bỏ qua event: {}, symbol: {} không được gán cho pod này", event.getType(), symbol);
+                return;
+            }
+        } else {
+            log.debug("Bỏ qua kiểm tra ownership cho symbol: {}, xử lý event: {}", symbol, event.getType());
         }
 
         // Không cần lấy matching engine vì chúng ta sẽ sử dụng OrderMatchingEngineService
Index: src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.icetea.lotus.infrastructure.messaging.consumer;\r\n\r\nimport com.fasterxml.jackson.core.JsonProcessingException;\r\nimport com.fasterxml.jackson.databind.ObjectMapper;\r\nimport com.icetea.lotus.core.domain.entity.Order;\r\nimport com.icetea.lotus.core.domain.entity.Trade;\r\nimport com.icetea.lotus.core.domain.service.OrderMatchingEngineService;\r\nimport com.icetea.lotus.infrastructure.messaging.command.OrderCommand;\r\nimport com.icetea.lotus.infrastructure.messaging.producer.OrderEventProducer;\r\nimport com.icetea.lotus.infrastructure.sharding.SymbolShardingManager;\r\nimport lombok.RequiredArgsConstructor;\r\nimport lombok.SneakyThrows;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.springframework.beans.factory.annotation.Value;\r\nimport org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;\r\nimport org.springframework.kafka.annotation.KafkaListener;\r\nimport org.springframework.kafka.core.KafkaTemplate;\r\nimport org.springframework.stereotype.Component;\r\n\r\nimport java.util.List;\r\n\r\n/**\r\n * Consumer cho OrderCommand\r\n */\r\n@Slf4j\r\n@Component\r\n@RequiredArgsConstructor\r\npublic class OrderCommandConsumer {\r\n\r\n    private final SymbolShardingManager shardingManager;\r\n    private final OrderMatchingEngineService orderMatchingEngineService;\r\n    private final OrderEventProducer orderEventProducer;\r\n    private final ObjectMapper objectMapper;\r\n    @Value(\"${topic-kafka.contract.order-commands}\")\r\n    private String orderCommandsTopic;\r\n\r\n    private final KafkaTemplate<String, String> kafkaTemplate;\r\n    /**\r\n     * Xử lý OrderCommand\r\n     * Note: Disabled by default để tránh conflict với matching-engine OrderCommandConsumer\r\n     * Enable bằng cách set future-core.order-command-consumer.enabled=true\r\n     */\r\n    @KafkaListener(\r\n        topics = \"${topic-kafka.contract.order-commands}\",\r\n        containerFactory = \"kafkaListenerContainerFactory\",\r\n        groupId = \"${spring.kafka.consumer.group-id}\"\r\n    )\r\n    @SneakyThrows(JsonProcessingException.class)\r\n    public void handleOrderCommand(String message){\r\n        log.info(\"handleOrderCommand Bắt đầu xử lý command từ topic {}, data = {}\", orderCommandsTopic, message);\r\n        OrderCommand command = objectMapper.readValue(message, OrderCommand.class);\r\n        log.info(\"handleOrderCommand after parse data = {}\", command.toString());\r\n        if (command == null || command.getOrder() == null || command.getOrder().getSymbol() == null) {\r\n            log.error(\"Nhận command không hợp lệ từ topic {}, command = {}\", orderCommandsTopic, command);\r\n            return;\r\n        }\r\n\r\n        log.info(\"Nhận command từ topic {}, type = {}, symbol = {}, orderId = {}\",\r\n                orderCommandsTopic, command.getType(), command.getOrder().getSymbol().getValue(), command.getOrder().getOrderId());\r\n\r\n        String symbol = command.getOrder().getSymbol().getValue();\r\n        log.info(\"handleOrderCommand Symbol: {}\", symbol);\r\n\r\n        // Chỉ xử lý command nếu symbol được gán cho instance này\r\n        if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {\r\n            log.debug(\"Bỏ qua command: {}, symbol: {} không được gán cho pod này\", command.getType(), symbol);\r\n            return;\r\n        }\r\n\r\n        // Xử lý command theo loại\r\n        log.info(\"handleOrderCommand Phân loại message theo type {}\", command.getType());\r\n        switch (command.getType()) {\r\n            case PLACE_ORDER:\r\n                log.debug(\"Xử lý command PLACE_ORDER, symbol: {}\", symbol);\r\n                handlePlaceOrderCommand(command.getOrder());\r\n                break;\r\n\r\n            case CANCEL_ORDER:\r\n                log.debug(\"Xử lý command CANCEL_ORDER, symbol: {}\", symbol);\r\n                handleCancelOrderCommand(command.getOrder());\r\n                break;\r\n\r\n            case UPDATE_ORDER:\r\n                log.debug(\"Xử lý command UPDATE_ORDER, symbol: {}\", symbol);\r\n                handleUpdateOrderCommand(command.getOrder());\r\n                break;\r\n\r\n            default:\r\n                log.warn(\"Không hỗ trợ loại command: {}\", command.getType());\r\n                break;\r\n        }\r\n        log.info(\"END handleOrderCommand, symbol: {}, command = {}\", symbol, command.getCommandId());\r\n    }\r\n\r\n    @KafkaListener(topics = \"action_push\", groupId = \"test-consumer-group\", containerFactory = \"kafkaListenerContainerFactory\")\r\n    public void pushMessage(String message) throws JsonProcessingException {\r\n        log.info(\"Test message received: {}\", message.toString());\r\n        if(message != null){\r\n            OrderCommand orderCommand = new OrderCommand();\r\n            orderCommand.setCommandId(\"123456\");\r\n            kafkaTemplate.send(\"test-consumer-local\", objectMapper.writeValueAsString(orderCommand));\r\n        }\r\n    }\r\n\r\n\r\n    @KafkaListener(topics = \"test-consumer-local\", groupId = \"test-consumer-group\", containerFactory = \"kafkaListenerContainerFactory\")\r\n    public void testMessage(String message) throws JsonProcessingException {\r\n        log.info(\"Test message received: {}\", message.toString());\r\n        OrderCommand orderCommand = objectMapper.readValue(message, OrderCommand.class);\r\n        log.info(\"Test message received parse: {}\",orderCommand.toString());\r\n    }\r\n\r\n    /**\r\n     * Xử lý command đặt lệnh\r\n     *\r\n     * @param order Lệnh\r\n     */\r\n    private void handlePlaceOrderCommand(Order order) {\r\n        try {\r\n            // Xử lý lệnh bằng OrderMatchingEngineService\r\n            List<Trade> trades = orderMatchingEngineService.processOrder(order);\r\n            log.info(\"handlePlaceOrderCommand: {}\", trades.toString());\r\n            // Gửi event\r\n            orderEventProducer.publishOrderPlacedEvent(order, trades);\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi xử lý command đặt lệnh: {}\", order.getOrderId(), e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Xử lý command hủy lệnh\r\n     *\r\n     * @param order Lệnh\r\n     */\r\n    private void handleCancelOrderCommand(Order order) {\r\n        try {\r\n            // Hủy lệnh bằng OrderMatchingEngineService\r\n            boolean success = orderMatchingEngineService.cancelOrder(order);\r\n\r\n            if (success) {\r\n                // Gửi event\r\n                orderEventProducer.publishOrderCancelledEvent(order);\r\n            }\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi xử lý command hủy lệnh: {}\", order.getOrderId(), e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Xử lý command cập nhật lệnh\r\n     *\r\n     * @param order Lệnh\r\n     */\r\n    private void handleUpdateOrderCommand(Order order) {\r\n        try {\r\n            // Hiện tại OrderMatchingEngineService không có phương thức updateOrder\r\n            // Nên chúng ta sẽ hủy lệnh cũ và đặt lệnh mới\r\n            boolean cancelSuccess = orderMatchingEngineService.cancelOrder(order);\r\n\r\n            if (cancelSuccess) {\r\n                // Đặt lệnh mới\r\n                orderMatchingEngineService.processOrder(order);\r\n\r\n                // Gửi event\r\n                orderEventProducer.publishOrderUpdatedEvent(order);\r\n            }\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi xử lý command cập nhật lệnh: {}\", order.getOrderId(), e);\r\n        }\r\n    }\r\n\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java b/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java
--- a/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java	(revision 84040975994bed1ba285d189ae32a79dd134461a)
+++ b/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java	(date 1752084579180)
@@ -34,6 +34,9 @@
     @Value("${topic-kafka.contract.order-commands}")
     private String orderCommandsTopic;
 
+    @Value("${future-core.order-command-consumer.check-ownership:false}")
+    private boolean checkSymbolOwnership;
+
     private final KafkaTemplate<String, String> kafkaTemplate;
     /**
      * Xử lý OrderCommand
@@ -61,10 +64,17 @@
         String symbol = command.getOrder().getSymbol().getValue();
         log.info("handleOrderCommand Symbol: {}", symbol);
 
-        // Chỉ xử lý command nếu symbol được gán cho instance này
-        if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
-            log.debug("Bỏ qua command: {}, symbol: {} không được gán cho pod này", command.getType(), symbol);
-            return;
+        // Kiểm tra ownership nếu được bật trong cấu hình
+        if (checkSymbolOwnership) {
+            boolean isOwned = shardingManager.isSymbolOwnedByThisPod(symbol);
+            log.info("Kiểm tra ownership cho symbol: {}, isOwned: {}, debugInfo: {}", symbol, isOwned, shardingManager.getDebugInfo());
+
+            if (!isOwned) {
+                log.debug("Bỏ qua command: {}, symbol: {} không được gán cho pod này", command.getType(), symbol);
+                return;
+            }
+        } else {
+            log.debug("Bỏ qua kiểm tra ownership cho symbol: {}, xử lý command: {}", symbol, command.getType());
         }
 
         // Xử lý command theo loại
Index: src/main/java/com/icetea/lotus/infrastructure/sharding/SymbolShardingManager.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.icetea.lotus.infrastructure.sharding;\r\n\r\nimport com.icetea.lotus.core.domain.valueobject.Symbol;\r\nimport com.icetea.lotus.core.common.LogMessages;\r\nimport lombok.Getter;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.redisson.api.RLock;\r\nimport org.redisson.api.RMap;\r\nimport org.redisson.api.RedissonClient;\r\nimport org.springframework.beans.factory.annotation.Value;\r\nimport org.springframework.stereotype.Component;\r\n\r\nimport java.util.Map;\r\nimport java.util.Set;\r\nimport java.util.List;\r\nimport java.util.Arrays;\r\nimport java.util.ArrayList;\r\nimport java.util.concurrent.ConcurrentHashMap;\r\nimport java.util.concurrent.ConcurrentSkipListMap;\r\nimport java.util.concurrent.TimeUnit;\r\n\r\n/**\r\n * Quản lý phân phối các symbol giữa các instance\r\n * Legacy component - được giữ lại để tương thích với hệ thống cũ\r\n * Sử dụng SmartShardingManager cho logic mới\r\n */\r\n@Slf4j\r\n@Component\r\npublic class SymbolShardingManager {\r\n\r\n    private final RedissonClient redissonClient;\r\n\r\n    // Constants\r\n    private static final String SYMBOL_TO_POD_MAP = \"symbol-to-pod-map\";\r\n    private static final String SYMBOL_REBALANCING_LOCK = \"symbol-rebalancing-lock\";\r\n\r\n    // Tên của pod\r\n    @Getter\r\n    private final String podName;\r\n\r\n    // Cấu hình symbols cho pod này\r\n    @Value(\"${future.sharding.pod-symbols:BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,XRP/USDT,SOL/USDT,DOT/USDT,DOGE/USDT,AVAX/USDT,MATIC/USDT,LINK/USDT,UNI/USDT,LTC/USDT,EOS/ETH,BZB/USDT,HT/USDT}\")\r\n    private String podSymbolsConfig;\r\n\r\n    // Tự động gán symbols từ cấu hình khi không có trong Redis\r\n    @Value(\"${future.sharding.auto-assign-from-config:true}\")\r\n    private boolean autoAssignFromConfig;\r\n\r\n    // OPTIMIZED: ConcurrentSkipListMap cho better cache locality và ordered access\r\n    private final ConcurrentSkipListMap<String, Boolean> symbolOwnershipCache = new ConcurrentSkipListMap<>();\r\n\r\n    // OPTIMIZED: ConcurrentSkipListMap cho consistent performance\r\n    private final ConcurrentSkipListMap<String, Long> symbolOrderCountMap = new ConcurrentSkipListMap<>();\r\n\r\n    // OPTIMIZED: Cache với TTL để reduce Redis calls\r\n    private final ConcurrentSkipListMap<String, CacheEntry> symbolCacheWithTTL = new ConcurrentSkipListMap<>();\r\n    private static final long CACHE_TTL_MS = 5000; // 5 seconds TTL\r\n\r\n    // Thống kê khối lượng giao dịch cho mỗi symbol\r\n    private final Map<String, Double> symbolVolumeMap = new ConcurrentHashMap<>();\r\n\r\n    /**\r\n     * Khởi tạo SymbolShardingManager\r\n     * @param redissonClient RedissonClient\r\n     * @param podName Tên của pod\r\n     */\r\n    public SymbolShardingManager(RedissonClient redissonClient,\r\n                                @Value(\"${HOSTNAME}\") String podName) {\r\n\r\n        this.redissonClient = redissonClient;\r\n        // Sử dụng giá trị từ cấu hình HOSTNAME\r\n        this.podName = podName;\r\n\r\n        log.info(LogMessages.ShardingManager.INFO_INITIALIZED(), this.podName);\r\n    }\r\n\r\n    /**\r\n     * Kiểm tra xem symbol có được gán cho pod này không\r\n     * @param symbol Symbol cần kiểm tra\r\n     * @return true nếu symbol được gán cho pod này, false nếu không\r\n     */\r\n    public boolean isSymbolOwnedByThisPod(Symbol symbol) {\r\n        return isSymbolOwnedByThisPod(symbol.getValue());\r\n    }\r\n\r\n    /**\r\n     * Kiểm tra xem symbol có được gán cho pod này không (atomic operation)\r\n     * @param symbolStr Symbol cần kiểm tra\r\n     * @return true nếu symbol được gán cho pod này, false nếu không\r\n     */\r\n    public boolean isSymbolOwnedByThisPod(String symbolStr) {\r\n        // Kiểm tra cache trước\r\n        Boolean cachedResult = symbolOwnershipCache.get(symbolStr);\r\n        if (cachedResult != null) {\r\n            return cachedResult;\r\n        }\r\n\r\n        // Sử dụng Redis để lưu trữ mapping giữa symbol và pod\r\n        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);\r\n\r\n        // Kiểm tra xem symbol đã có owner chưa\r\n        String currentOwner = symbolToPodMap.get(symbolStr);\r\n\r\n        if (currentOwner != null) {\r\n            // Symbol đã có owner, kiểm tra xem có phải pod này không\r\n            boolean isOwned = currentOwner.equals(podName);\r\n            symbolOwnershipCache.put(symbolStr, isOwned);\r\n\r\n            if (isOwned) {\r\n                log.debug(\"Pod {} đã sở hữu symbol {}\", podName, symbolStr);\r\n            } else {\r\n                log.debug(\"Symbol {} đã được gán cho pod {} (pod hiện tại: {})\",\r\n                        symbolStr, currentOwner, podName);\r\n            }\r\n\r\n            return isOwned;\r\n        }\r\n\r\n        // Symbol chưa có owner, kiểm tra cấu hình trước khi gán\r\n        if (autoAssignFromConfig) {\r\n            List<String> configuredSymbols = parsePodSymbols();\r\n            if (configuredSymbols.contains(symbolStr)) {\r\n                // Symbol có trong cấu hình của pod này, thử gán\r\n                String previousOwner = symbolToPodMap.putIfAbsent(symbolStr, podName);\r\n                boolean isOwned = previousOwner == null || previousOwner.equals(podName);\r\n\r\n                symbolOwnershipCache.put(symbolStr, isOwned);\r\n\r\n                if (isOwned && previousOwner == null) {\r\n                    log.info(\"Pod {} đã được gán symbol {} từ cấu hình (first assignment)\", podName, symbolStr);\r\n                } else if (isOwned) {\r\n                    log.debug(\"Pod {} đã sở hữu symbol {} từ cấu hình\", podName, symbolStr);\r\n                } else {\r\n                    log.warn(\"Symbol {} đã được gán cho pod {} trong khi pod {} đang cố gán từ cấu hình\",\r\n                            symbolStr, previousOwner, podName);\r\n                }\r\n\r\n                return isOwned;\r\n            }\r\n        }\r\n\r\n        // Symbol không có trong cấu hình hoặc không bật auto-assign, không gán\r\n        symbolOwnershipCache.put(symbolStr, false);\r\n        log.debug(\"Symbol {} không có trong cấu hình của pod {} hoặc auto-assign bị tắt\", symbolStr, podName);\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Lấy tên pod sở hữu symbol\r\n     * @param symbol Symbol cần kiểm tra\r\n     * @return Tên pod sở hữu symbol, null nếu không có pod nào sở hữu\r\n     */\r\n    public String getOwnerPod(Symbol symbol) {\r\n        return getOwnerPod(symbol.getValue());\r\n    }\r\n\r\n    /**\r\n     * Lấy tên pod sở hữu symbol\r\n     * @param symbolStr Symbol cần kiểm tra\r\n     * @return Tên pod sở hữu symbol, null nếu không có pod nào sở hữu\r\n     */\r\n    public String getOwnerPod(String symbolStr) {\r\n        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);\r\n        return symbolToPodMap.get(symbolStr);\r\n    }\r\n\r\n    /**\r\n     * Gán symbol cho pod này\r\n     * @param symbol Symbol cần gán\r\n     * @return true nếu gán thành công, false nếu không\r\n     */\r\n    public boolean assignSymbolToThisPod(Symbol symbol) {\r\n        return assignSymbolToThisPod(symbol.getValue());\r\n    }\r\n\r\n    /**\r\n     * Gán symbol cho pod này (force assignment)\r\n     * @param symbolStr Symbol cần gán\r\n     * @return true nếu gán thành công, false nếu không\r\n     */\r\n    public boolean assignSymbolToThisPod(String symbolStr) {\r\n        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);\r\n\r\n        // Force gán symbol cho pod này (override existing assignment)\r\n        String previousOwner = symbolToPodMap.put(symbolStr, podName);\r\n\r\n        // Cập nhật cache\r\n        symbolOwnershipCache.put(symbolStr, true);\r\n\r\n        if (previousOwner != null && !previousOwner.equals(podName)) {\r\n            log.warn(\"Force assigned symbol {} from pod {} to pod {}\", symbolStr, previousOwner, podName);\r\n        } else {\r\n            log.info(\"Assigned symbol {} to pod {}\", symbolStr, podName);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Thử gán symbol cho pod này (atomic, không override)\r\n     * @param symbolStr Symbol cần gán\r\n     * @return true nếu gán thành công, false nếu symbol đã được gán cho pod khác\r\n     */\r\n    public boolean tryAssignSymbolToThisPod(String symbolStr) {\r\n        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);\r\n\r\n        // Atomic operation: chỉ gán nếu chưa có owner\r\n        String previousOwner = symbolToPodMap.putIfAbsent(symbolStr, podName);\r\n        boolean assigned = previousOwner == null;\r\n\r\n        if (assigned) {\r\n            // Cập nhật cache\r\n            symbolOwnershipCache.put(symbolStr, true);\r\n            log.info(\"Successfully assigned symbol {} to pod {}\", symbolStr, podName);\r\n        } else {\r\n            // Cập nhật cache với kết quả false\r\n            symbolOwnershipCache.put(symbolStr, false);\r\n            log.warn(\"Failed to assign symbol {} to pod {} (already owned by {})\",\r\n                    symbolStr, podName, previousOwner);\r\n        }\r\n\r\n        return assigned;\r\n    }\r\n\r\n    /**\r\n     * Hủy gán symbol khỏi pod này\r\n     * @param symbol Symbol cần hủy gán\r\n     * @return true nếu hủy gán thành công, false nếu không\r\n     */\r\n    public boolean unassignSymbolFromThisPod(Symbol symbol) {\r\n        return unassignSymbolFromThisPod(symbol.getValue());\r\n    }\r\n\r\n    /**\r\n     * Hủy gán symbol khỏi pod này\r\n     * @param symbolStr Symbol cần hủy gán\r\n     * @return true nếu hủy gán thành công, false nếu không\r\n     */\r\n    public boolean unassignSymbolFromThisPod(String symbolStr) {\r\n        // Kiểm tra xem symbol có được gán cho pod này không\r\n        if (!isSymbolOwnedByThisPod(symbolStr)) {\r\n            return false;\r\n        }\r\n\r\n        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);\r\n\r\n        // Hủy gán symbol khỏi pod này\r\n        symbolToPodMap.remove(symbolStr, podName);\r\n\r\n        // Cập nhật cache\r\n        symbolOwnershipCache.put(symbolStr, false);\r\n\r\n        log.info(LogMessages.ShardingManager.INFO_SYMBOL_UNASSIGNED(), symbolStr, podName);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Lấy danh sách các symbol được gán cho pod này\r\n     * @return Danh sách các symbol\r\n     */\r\n    public Set<String> getSymbolsOwnedByThisPod() {\r\n        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);\r\n\r\n        Set<String> ownedSymbols = ConcurrentHashMap.newKeySet();\r\n\r\n        // Lọc các symbol được gán cho pod này\r\n        for (Map.Entry<String, String> entry : symbolToPodMap.entrySet()) {\r\n            if (entry.getValue().equals(podName)) {\r\n                ownedSymbols.add(entry.getKey());\r\n\r\n                // Cập nhật cache\r\n                symbolOwnershipCache.put(entry.getKey(), true);\r\n            }\r\n        }\r\n\r\n        return ownedSymbols;\r\n    }\r\n\r\n    /**\r\n     * Cập nhật thống kê cho symbol\r\n     * @param symbolStr Symbol cần cập nhật\r\n     * @param orderCount Số lượng lệnh xử lý\r\n     * @param volume Khối lượng giao dịch\r\n     */\r\n    public void updateSymbolStats(String symbolStr, long orderCount, double volume) {\r\n        symbolOrderCountMap.put(symbolStr, symbolOrderCountMap.getOrDefault(symbolStr, 0L) + orderCount);\r\n        symbolVolumeMap.put(symbolStr, symbolVolumeMap.getOrDefault(symbolStr, 0.0) + volume);\r\n    }\r\n\r\n    /**\r\n     * Lấy số lượng lệnh xử lý cho symbol\r\n     * @param symbolStr Symbol cần lấy thống kê\r\n     * @return Số lượng lệnh xử lý\r\n     */\r\n    public long getSymbolOrderCount(String symbolStr) {\r\n        return symbolOrderCountMap.getOrDefault(symbolStr, 0L);\r\n    }\r\n\r\n    /**\r\n     * Lấy khối lượng giao dịch cho symbol\r\n     * @param symbolStr Symbol cần lấy thống kê\r\n     * @return Khối lượng giao dịch\r\n     */\r\n    public double getSymbolVolume(String symbolStr) {\r\n        return symbolVolumeMap.getOrDefault(symbolStr, 0.0);\r\n    }\r\n\r\n    /**\r\n     * Đặt lại thống kê cho symbol\r\n     * @param symbolStr Symbol cần đặt lại thống kê\r\n     */\r\n    public void resetSymbolStats(String symbolStr) {\r\n        symbolOrderCountMap.remove(symbolStr);\r\n        symbolVolumeMap.remove(symbolStr);\r\n    }\r\n\r\n    /**\r\n     * Lấy tất cả thống kê về số lượng lệnh xử lý\r\n     * @return Map chứa thống kê số lượng lệnh xử lý cho mỗi symbol\r\n     */\r\n    public Map<String, Long> getAllSymbolOrderCounts() {\r\n        return new ConcurrentHashMap<>(symbolOrderCountMap);\r\n    }\r\n\r\n    /**\r\n     * Lấy tất cả thống kê về khối lượng giao dịch\r\n     * @return Map chứa thống kê khối lượng giao dịch cho mỗi symbol\r\n     */\r\n    public Map<String, Double> getAllSymbolVolumes() {\r\n        return new ConcurrentHashMap<>(symbolVolumeMap);\r\n    }\r\n\r\n    /**\r\n     * Cân bằng lại các symbol giữa các pod\r\n     * @param allSymbols Danh sách tất cả các symbol\r\n     * @param allPods Danh sách tất cả các pod\r\n     */\r\n    public void rebalanceSymbols(Set<String> allSymbols, Set<String> allPods) {\r\n        // Nếu không có pod nào, không cần cân bằng\r\n        if (allPods.isEmpty()) {\r\n            return;\r\n        }\r\n\r\n        // Lấy lock để đảm bảo chỉ có một pod thực hiện cân bằng tại một thời điểm\r\n        RLock lock = redissonClient.getLock(SYMBOL_REBALANCING_LOCK);\r\n\r\n        try {\r\n            // Sử dụng quick lock cho rebalancing (200ms wait, 2000ms lease)\r\n            if (lock.tryLock(200, 2000, TimeUnit.MILLISECONDS)) {\r\n                try {\r\n                    log.info(LogMessages.ShardingManager.INFO_REBALANCING_STARTED(), podName);\r\n\r\n                    RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);\r\n\r\n                    // Xóa các symbol không còn tồn tại\r\n                    for (String symbol : symbolToPodMap.keySet()) {\r\n                        if (!allSymbols.contains(symbol)) {\r\n                            symbolToPodMap.remove(symbol);\r\n\r\n                            // Cập nhật cache\r\n                            symbolOwnershipCache.remove(symbol);\r\n\r\n                            log.info(LogMessages.ShardingManager.INFO_SYMBOL_REMOVED(), symbol);\r\n                        }\r\n                    }\r\n\r\n                    // Gán các symbol chưa được gán\r\n                    int podIndex = 0;\r\n                    String[] podArray = allPods.toArray(new String[0]);\r\n\r\n                    for (String symbol : allSymbols) {\r\n                        if (!symbolToPodMap.containsKey(symbol)) {\r\n                            String assignedPod = podArray[podIndex];\r\n                            symbolToPodMap.put(symbol, assignedPod);\r\n\r\n                            // Cập nhật cache nếu symbol được gán cho pod này\r\n                            symbolOwnershipCache.put(symbol, assignedPod.equals(podName));\r\n\r\n                            log.info(LogMessages.ShardingManager.INFO_SYMBOL_ASSIGNED(), symbol, assignedPod, null);\r\n\r\n                            // Chuyển sang pod tiếp theo\r\n                            podIndex = (podIndex + 1) % podArray.length;\r\n                        }\r\n                    }\r\n\r\n                    log.info(LogMessages.ShardingManager.INFO_REBALANCING_COMPLETED(), podName);\r\n                } finally {\r\n                    lock.unlock();\r\n                }\r\n            } else {\r\n                log.warn(LogMessages.ShardingManager.WARN_REBALANCING_LOCK_FAILED(), podName);\r\n            }\r\n        } catch (InterruptedException e) {\r\n            Thread.currentThread().interrupt();\r\n            log.error(LogMessages.ShardingManager.ERROR_REBALANCING_INTERRUPTED(), podName, e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Xóa cache\r\n     */\r\n    public void clearCache() {\r\n        symbolOwnershipCache.clear();\r\n        symbolCacheWithTTL.clear();\r\n    }\r\n\r\n    /**\r\n     * Parse pod symbols từ cấu hình\r\n     * @return Danh sách symbols được gán cho pod này\r\n     */\r\n    private List<String> parsePodSymbols() {\r\n        if (podSymbolsConfig == null || podSymbolsConfig.trim().isEmpty()) {\r\n            return new ArrayList<>();\r\n        }\r\n\r\n        return Arrays.stream(podSymbolsConfig.split(\",\"))\r\n                .map(String::trim)\r\n                .filter(s -> !s.isEmpty())\r\n                .toList();\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * Cache entry với TTL\r\n     */\r\n    private static class CacheEntry {\r\n        private final boolean value;\r\n        private final long timestamp;\r\n\r\n        public CacheEntry(boolean value) {\r\n            this.value = value;\r\n            this.timestamp = System.currentTimeMillis();\r\n        }\r\n\r\n        public boolean isExpired(long ttlMs) {\r\n            return (System.currentTimeMillis() - timestamp) > ttlMs;\r\n        }\r\n\r\n        public boolean getValue() {\r\n            return value;\r\n        }\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/icetea/lotus/infrastructure/sharding/SymbolShardingManager.java b/src/main/java/com/icetea/lotus/infrastructure/sharding/SymbolShardingManager.java
--- a/src/main/java/com/icetea/lotus/infrastructure/sharding/SymbolShardingManager.java	(revision 84040975994bed1ba285d189ae32a79dd134461a)
+++ b/src/main/java/com/icetea/lotus/infrastructure/sharding/SymbolShardingManager.java	(date 1752084425305)
@@ -412,13 +412,26 @@
      */
     private List<String> parsePodSymbols() {
         if (podSymbolsConfig == null || podSymbolsConfig.trim().isEmpty()) {
+            log.warn("Pod symbols config is null or empty for pod: {}", podName);
             return new ArrayList<>();
         }
 
-        return Arrays.stream(podSymbolsConfig.split(","))
+        List<String> symbols = Arrays.stream(podSymbolsConfig.split(","))
                 .map(String::trim)
                 .filter(s -> !s.isEmpty())
                 .toList();
+
+        log.debug("Parsed pod symbols for pod {}: {}", podName, symbols);
+        return symbols;
+    }
+
+    /**
+     * Get debug information about symbol sharding configuration
+     * @return Debug information
+     */
+    public String getDebugInfo() {
+        return String.format("Pod: %s, AutoAssign: %s, ConfiguredSymbols: %s",
+                podName, autoAssignFromConfig, parsePodSymbols());
     }
 
 
