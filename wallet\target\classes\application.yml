server:
  servlet:
    context-path: /wallet
  port: ${SERVER_PORT:6009}

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  application:
    name: service-wallet
  session:
    store-type: ${SPRING_SESSION_STORE_TYPE:none}

  datasource:
    hikari:
      minimum-idle: 2
      maximum-pool-size: 2
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      auto-commit: true
      leak-detection-threshold: 2000
    tomcat:
      initial-size: 5
      min-idle: 5
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
    dbcp2:
      pool-prepared-statements: true

  kafka:
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:0}
      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
    consumer:
      enable.auto.commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
      session.timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto.commit.interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
      auto.offset.reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      group.id: ${KAFKA_CONSUMER_GROUP_ID:wallet-local}
      concurrency: 9
      maxPollRecordsConfig: 50

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:erlzeziorjuccpvx}

  data:
    jpa:
      repositories:
        enabled: true

  devtools:
    restart:
      enabled: true

  cloud:
    consul:
      discovery:
        enabled: true
        service-name: ${SPRING_APPLICATION_NAME:service-wallet}
        health-check-path: ${server.servlet.context-path}/actuator/health
        health-check-interval: 10s
        prefer-ip-address: true

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://************:32082/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: http://************:32082/realms/cex-lotus
            token-uri: http://************:32082/realms/cex-lotus/protocol/openid-connect/token


# keycloak
keycloak:
  auth-server-url: http://************:32082
  realm: cex-lotus
  resource: cex-exchange-api
  credentials:
    secret: UTj5XuHBZiLHJSZYUd7IJ2uOpyL4IG8S

cex-security:
  resource-server-enabled: true
  default-principal-name: "internal-service"
  permit-all-endpoints:
    - "/**"

management:
  health:
    elasticsearch:
      enabled: false
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
  server:
    base-path: /actuator

spark:
  system:
    work-id: 1
    data-center-id: 1
    host: "@spark.system.host@"
    name: "@spark.system.name@"
    admins: "@spark.system.admins@"
    admin-phones: "@spark.system.admin-phones@"

aliyun:
  mail-sms:
    region: "@aliyun.mail-sms.region@"
    access-key-id: "@aliyun.mail-sms.access-key-id@"
    access-secret: "@aliyun.mail-sms.access-secret@"
    from-address: "@aliyun.mail-sms.from-address@"
    from-alias: "@aliyun.mail-sms.from-alias@"
    sms-sign: "@aliyun.mail-sms.sms-sign@"
    sms-template: "@aliyun.mail-sms.sms-template@"

sms:
  driver: twilio
  gateway: "@sms.gateway@"
  username: "@sms.username@"
  password: "@sms.password@"
  sign: "@sms.sign@"
  internationalGateway: "@sms.internationalGateway@"
  internationalUsername: "@sms.internationalUsername@"
  internationalPassword: "@sms.internationalPassword@"

twilio:
  account-sid: **********************************
  auth-token: 9d6fe9aa6af7e56b72397b219b817e5f
  trial-number: +***********

es:
  username: admin
  password: Icetea@1235601
  mine:
    index: ""
    type: ""
  public:
    ip: ************
  private:
    ip: ""
  port: 9200

rest-template:
  service-name:
    custom-rpc: "SERVICE-RPC-"

dubbo:
  scan:
    base-packages: com.icetea.lotus.service,com.icetea.lotus.consumer
