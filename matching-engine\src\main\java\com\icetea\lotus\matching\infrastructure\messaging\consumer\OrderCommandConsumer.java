package com.icetea.lotus.matching.infrastructure.messaging.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreTradeResult;
import com.icetea.lotus.matching.infrastructure.idempotency.OrderIdempotencyService;
import com.icetea.lotus.matching.infrastructure.messaging.producer.FutureCoreKafkaProducer;
import com.icetea.lotus.matching.infrastructure.monitoring.DuplicateOrderMonitoringService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Order Command Consumer - INPUT topic consumer
 * Handles order commands (PLACE_ORDER, CANCEL_ORDER, UPDATE_ORDER)
 * Migrated từ Future-Core OrderCommandConsumer
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCommandConsumer {

    private final FutureCoreCompatibilityService futureCoreService;
    private final FutureCoreKafkaProducer futureCoreKafkaProducer;
    private final OrderIdempotencyService idempotencyService;
    private final DuplicateOrderMonitoringService monitoringService;
    private final ObjectMapper objectMapper;
    
    // Executor for async processing
    private final ExecutorService processingExecutor = Executors.newFixedThreadPool(15);
    
    /**
     * Handle order commands - INPUT topic consumer
     * Sử dụng futuresKafkaListenerContainerFactory cho futures trading
     * FIXED: Proper acknowledgment after processing completion
     */
    @KafkaListener(
            topics = "${topic-kafka.contract.order-commands:contract-order-commands}",
            containerFactory = "futuresKafkaListenerContainerFactory",
            groupId = "matching-engine-order-commands")
    public void handleOrderCommand(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        log.debug("Received {} order command records", records.size());

        try {
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (ConsumerRecord<String, String> record : records) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                    () -> processOrderCommand(record), processingExecutor);
                futures.add(future);
            }

            // FIXED: Wait for all processing to complete before acknowledging
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(30, TimeUnit.SECONDS); // 30 second timeout to prevent hanging

            // Only acknowledge after ALL processing is complete
            ack.acknowledge();
            log.debug("Successfully processed and acknowledged {} order command records", records.size());

        } catch (TimeoutException e) {
            log.error("Timeout waiting for order command processing to complete", e);
            // Don't acknowledge on timeout - let Kafka retry
        } catch (InterruptedException e) {
            log.error("Interrupted while waiting for order command processing", e);
            Thread.currentThread().interrupt(); // Restore interrupted status
            // Don't acknowledge on interruption - let Kafka retry
        } catch (Exception e) {
            log.error("Error processing order command batch", e);
            // Don't acknowledge on error - let Kafka retry
        }
    }
    
    /**
     * Process individual order command
     */
    private void processOrderCommand(ConsumerRecord<String, String> record) {
        String symbol = record.key();
        String value = record.value();

        // Enhanced validation
        if (symbol == null || symbol.trim().isEmpty()) {
            log.error("Invalid symbol in order command: {}", symbol);
            return;
        }

        if (value == null || value.trim().isEmpty()) {
            log.error("Empty command JSON for symbol: {}", symbol);
            return;
        }

        log.info("Processing order command for symbol: {}", symbol);

        try {
            // Parse command from JSON with validation
            JsonNode commandJson;
            try {
                commandJson = objectMapper.readTree(value);
            } catch (Exception e) {
                log.error("Failed to parse command JSON for symbol: {} - JSON: {}", symbol, value, e);
                return;
            }

            // Validate command structure
            if (!commandJson.has("type") || !commandJson.has("commandId")) {
                log.error("Missing required fields (type/commandId) in command for symbol: {}", symbol);
                return;
            }

            String commandType = commandJson.get("type").asText();
            String commandId = commandJson.get("commandId").asText();

            if (commandType == null || commandType.trim().isEmpty()) {
                log.error("Empty commandType for symbol: {}", symbol);
                return;
            }

            log.info("Processing command type: {} with ID: {} for symbol: {}", commandType, commandId, symbol);

            // Process command theo loại
            switch (commandType) {
                case "PLACE_ORDER":
                    log.debug("Processing PLACE_ORDER command for symbol: {}", symbol);
                    handlePlaceOrderCommand(commandJson, symbol);
                    break;

                case "CANCEL_ORDER":
                    log.debug("Processing CANCEL_ORDER command for symbol: {}", symbol);
                    handleCancelOrderCommand(commandJson, symbol);
                    break;

                case "UPDATE_ORDER":
                    log.debug("Processing UPDATE_ORDER command for symbol: {}", symbol);
                    handleUpdateOrderCommand(commandJson, symbol);
                    break;

                default:
                    log.warn("Unsupported command type: {} for symbol: {}", commandType, symbol);
                    break;
            }

            log.info("Successfully processed command type: {} for symbol: {}", commandType, symbol);

        } catch (Exception e) {
            log.error("Unexpected error processing order command for symbol: {}", symbol, e);
        }
    }
    
    /**
     * Handle PLACE_ORDER command with idempotency protection
     */
    private void handlePlaceOrderCommand(JsonNode commandJson, String symbol) {
        String orderId = null;

        try {
            // Validate order data exists
            if (!commandJson.has("order")) {
                log.error("Missing 'order' field in PLACE_ORDER command for symbol: {}", symbol);
                return;
            }

            JsonNode orderJson = commandJson.get("order");
            if (orderJson == null || orderJson.isNull()) {
                log.error("Null order data in PLACE_ORDER command for symbol: {}", symbol);
                return;
            }

            // Extract orderId for idempotency check
            if (orderJson.has("orderId")) {
                JsonNode orderIdNode = orderJson.get("orderId");
                if (orderIdNode.has("value")) {
                    orderId = orderIdNode.get("value").asText();
                } else {
                    orderId = orderIdNode.asText();
                }
            }

            // IDEMPOTENCY CHECK: Skip if order is already being processed or was processed
            if (orderId != null && !idempotencyService.tryAcquireProcessingLock(orderId)) {
                log.info("Skipping duplicate order processing for orderId: {} symbol: {}", orderId, symbol);
                monitoringService.recordDuplicateOrderSkipped(orderId, symbol);
                return;
            }

            Object order;
            try {
                order = objectMapper.treeToValue(orderJson, Object.class);
            } catch (Exception e) {
                log.error("Failed to parse order data for symbol: {}", symbol, e);
                if (orderId != null) {
                    idempotencyService.releaseProcessingLock(orderId);
                }
                return;
            }

            // Process order through Future-Core compatibility service (internal method)
            FutureCoreTradeResult result = futureCoreService.processContractOrderInternal(order);

            if (result.isSuccess()) {
                // Publish OUTPUT: order placed event
                futureCoreKafkaProducer.publishOrderPlacedEvent(order, result.getTrades());

                // Publish OUTPUT: completed orders if any
                if (result.getCompletedOrder() != null) {
                    futureCoreKafkaProducer.publishContractOrderCompleted(symbol, result.getCompletedOrder());
                }

                // PERFORMANCE OPTIMIZATION: Publish trades in batch instead of individually
                if (result.getTrades() != null && !result.getTrades().isEmpty()) {
                    futureCoreKafkaProducer.publishContractTradeBatch(result.getTrades());
                }

                // IDEMPOTENCY: Mark order as successfully processed
                if (orderId != null) {
                    idempotencyService.markOrderAsProcessed(orderId);
                }

                log.info("Successfully processed PLACE_ORDER command for orderId: {} symbol: {} with {} trades",
                        orderId, symbol, result.getTrades() != null ? result.getTrades().size() : 0);
            } else {
                log.warn("Failed to process PLACE_ORDER command for orderId: {} symbol: {} - {}",
                        orderId, symbol, result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Unexpected error handling PLACE_ORDER command for orderId: {} symbol: {}", orderId, symbol, e);
        } finally {
            // IDEMPOTENCY: Always release processing lock
            if (orderId != null) {
                idempotencyService.releaseProcessingLock(orderId);
            }
        }
    }
    
    /**
     * Handle CANCEL_ORDER command
     */
    private void handleCancelOrderCommand(JsonNode commandJson, String symbol) {
        try {
            JsonNode orderJson = commandJson.get("order");
            Object order = objectMapper.treeToValue(orderJson, Object.class);
            
            // Process order cancellation through Future-Core compatibility service (internal method)
            FutureCoreTradeResult result = futureCoreService.processContractOrderCancelInternal(order);

            if (result.isSuccess()) {
                // Publish OUTPUT: cancel success message (consistent with FutureCoreOrderConsumer)
                futureCoreKafkaProducer.publishContractOrderCancelSuccess(symbol, result.getCancelResult());

                // Publish OUTPUT: order cancelled event for lifecycle tracking
                futureCoreKafkaProducer.publishOrderCancelledEvent(order);

                // Publish OUTPUT: trade plate update if needed
                if (result.getTradePlate() != null) {
                    futureCoreKafkaProducer.publishContractTradePlate(symbol, result.getTradePlate());
                }

                log.info("Successfully processed CANCEL_ORDER command for symbol: {}", symbol);
            } else {
                log.warn("Failed to process CANCEL_ORDER command for symbol: {} - {}",
                        symbol, result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("Error handling CANCEL_ORDER command for symbol: {}", symbol, e);
        }
    }
    
    /**
     * Handle UPDATE_ORDER command
     */
    private void handleUpdateOrderCommand(JsonNode commandJson, String symbol) {
        try {
            JsonNode orderJson = commandJson.get("order");
            Object order = objectMapper.treeToValue(orderJson, Object.class);
            
            // Process order update through Future-Core compatibility service (internal methods)
            // Hiện tại implement như cancel + place order mới
            FutureCoreTradeResult cancelResult = futureCoreService.processContractOrderCancelInternal(order);

            if (cancelResult.isSuccess()) {
                // Place order mới
                FutureCoreTradeResult placeResult = futureCoreService.processContractOrderInternal(order);

                if (placeResult.isSuccess()) {
                    // Publish OUTPUT: order updated event
                    futureCoreKafkaProducer.publishOrderUpdatedEvent(order);

                    // PERFORMANCE OPTIMIZATION: Publish trades in batch
                    if (placeResult.getTrades() != null && !placeResult.getTrades().isEmpty()) {
                        futureCoreKafkaProducer.publishContractTradeBatch(placeResult.getTrades());
                    }

                    log.info("Successfully processed UPDATE_ORDER command for symbol: {}", symbol);
                } else {
                    log.warn("Failed to place new order in UPDATE_ORDER command for symbol: {} - {}",
                            symbol, placeResult.getErrorMessage());
                }
            } else {
                log.warn("Failed to cancel old order in UPDATE_ORDER command for symbol: {} - {}",
                        symbol, cancelResult.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("Error handling UPDATE_ORDER command for symbol: {}", symbol, e);
        }
    }
    
    /**
     * Shutdown executor
     */
    public void shutdown() {
        processingExecutor.shutdown();
    }
}
