# ===================================================================
# MATCHING ENGINE PRODUCTION CONFIGURATION
# Optimized for high-performance production environment
# ===================================================================

server:
  port: 6061

# Production Spring Configuration
spring:
  # Production Redis Configuration
  data:
    redis:
      host: ${REDIS_HOST:redis-cluster.production.svc.cluster.local}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD}
      database: ${REDIS_DATABASE:0}
      timeout: ${REDIS_TIMEOUT:3000}
      lettuce:
        pool:
          max-active: ${REDIS_POOL_MAX_ACTIVE:50}
          max-idle: ${REDIS_POOL_MAX_IDLE:20}
          min-idle: ${REDIS_POOL_MIN_IDLE:10}
          max-wait: ${REDIS_POOL_MAX_WAIT:3000}
        cluster:
          refresh:
            adaptive: true
            period: 30s
    
    # Production MongoDB Configuration
    mongodb:
      uri: ${SPRING_MONGODB_URI:mongodb://matching_engine_user:${MONGODB_PASSWORD}@mongodb-cluster.production.svc.cluster.local:27017/matching_engine_prod?replicaSet=rs0&authSource=admin}
      database: ${MONGODB_DATABASE:matching_engine_prod}
      # Production Connection Pool - Optimized
      connection-pool:
        max-size: ${MONGODB_POOL_MAX_SIZE:100}
        min-size: ${MONGODB_POOL_MIN_SIZE:20}
        max-wait-time: ${MONGODB_POOL_MAX_WAIT:2000}
        max-connection-idle-time: ${MONGODB_POOL_MAX_IDLE:60000}
        max-connection-life-time: ${MONGODB_POOL_MAX_LIFE:300000}
      # Production Socket Configuration
      socket:
        connect-timeout: ${MONGODB_SOCKET_CONNECT_TIMEOUT:2000}
        read-timeout: ${MONGODB_SOCKET_READ_TIMEOUT:5000}
      # Production Server Selection
      server-selection-timeout: ${MONGODB_SERVER_SELECTION_TIMEOUT:2000}
      # Production Write Concern - Strong consistency
      write-concern:
        w: ${MONGODB_WRITE_CONCERN_W:majority}
        j: ${MONGODB_WRITE_CONCERN_J:true}
        w-timeout: ${MONGODB_WRITE_CONCERN_TIMEOUT:2000}
      # Production Read Preference
      read-preference: ${MONGODB_READ_PREFERENCE:primaryPreferred}
      # Production Retry Configuration
      retry-writes: ${MONGODB_RETRY_WRITES:true}
      retry-reads: ${MONGODB_RETRY_READS:true}

  # Production Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:kafka-cluster.production.svc.cluster.local:9092}
    security:
      protocol: ${KAFKA_SECURITY_PROTOCOL:SASL_SSL}
    sasl:
      mechanism: ${KAFKA_SASL_MECHANISM:SCRAM-SHA-512}
      jaas:
        config: ${KAFKA_SASL_JAAS_CONFIG}
    ssl:
      trust-store-location: ${KAFKA_SSL_TRUSTSTORE_LOCATION}
      trust-store-password: ${KAFKA_SSL_TRUSTSTORE_PASSWORD}
      key-store-location: ${KAFKA_SSL_KEYSTORE_LOCATION}
      key-store-password: ${KAFKA_SSL_KEYSTORE_PASSWORD}
    listener:
      concurrency: ${KAFKA_LISTENER_CONCURRENCY:20}
      ack-mode: manual
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:5}
      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:1024}
      linger: ${KAFKA_PRODUCER_LINGER:5}
      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:33554432}
      compression-type: lz4
      acks: all
      enable-idempotence: true
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:matching-engine-prod-group}
      enable-auto-commit: false
      session-timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:10000}
      properties:
        max.poll.records: 100
        fetch.min.bytes: 1024
        fetch.max.wait.ms: 100

# Production Matching Engine Configuration
matching-engine:
  # Production Exchange Configuration
  exchange:
    enabled: true
    default-algorithm: FIFO
    balance-validation: true
    trade-plate-publishing: true
  
  # Production Future-Core Configuration
  future-core:
    enabled: true
    default-algorithm: FIFO
    cas-retry-limit: 20
    performance-monitoring: true
  
  # Production Performance Configuration
  performance:
    metrics-collection: true
    health-monitoring: true
    throughput-target: 50000   # High TPS target for production
    latency-target: 1          # Strict latency target
    thread-pool-size: ${MATCHING_ENGINE_THREAD_POOL_SIZE:50}
    batch-size: ${MATCHING_ENGINE_BATCH_SIZE:500}
    timeout-ms: ${MATCHING_ENGINE_TIMEOUT_MS:2000}
  
  # Production Order Book Configuration
  order-book:
    segment-size: 5000
    cache-validity-ms: 10000
    max-depth: 500

  # Production MongoDB Configuration
  mongodb:
    enabled: ${MATCHING_ENGINE_MONGODB_ENABLED:true}
    # Production Snapshot Settings
    snapshot:
      enabled: ${MATCHING_ENGINE_SNAPSHOT_ENABLED:true}
      auto-save: ${MATCHING_ENGINE_SNAPSHOT_AUTO_SAVE:true}
      save-interval-seconds: ${MATCHING_ENGINE_SNAPSHOT_SAVE_INTERVAL:60}  # 1 minute for production
      max-versions-per-symbol: ${MATCHING_ENGINE_SNAPSHOT_MAX_VERSIONS:20}
      compression-enabled: ${MATCHING_ENGINE_SNAPSHOT_COMPRESSION:true}
      async-save: ${MATCHING_ENGINE_SNAPSHOT_ASYNC:true}
      batch-size: ${MATCHING_ENGINE_SNAPSHOT_BATCH_SIZE:500}
    # Production Cleanup Settings
    cleanup:
      enabled: ${MATCHING_ENGINE_CLEANUP_ENABLED:true}
      retention-days: ${MATCHING_ENGINE_CLEANUP_RETENTION_DAYS:30}
      cleanup-interval-hours: ${MATCHING_ENGINE_CLEANUP_INTERVAL_HOURS:1}
      max-snapshots-per-cleanup: ${MATCHING_ENGINE_CLEANUP_MAX_SNAPSHOTS:5000}
    # Production Collection Names
    collections:
      order-book-snapshots: ${MONGODB_COLLECTION_ORDER_BOOK_SNAPSHOTS:order_book_snapshots}
      trade-history: ${MONGODB_COLLECTION_TRADE_HISTORY:trade_history}
      performance-metrics: ${MONGODB_COLLECTION_PERFORMANCE_METRICS:performance_metrics}
      system-events: ${MONGODB_COLLECTION_SYSTEM_EVENTS:system_events}
    # Production Index Configuration
    indexes:
      auto-create: ${MONGODB_INDEXES_AUTO_CREATE:true}
      background: ${MONGODB_INDEXES_BACKGROUND:true}
    # Production Performance Settings
    performance:
      bulk-write-size: ${MONGODB_BULK_WRITE_SIZE:5000}
      query-timeout-ms: ${MONGODB_QUERY_TIMEOUT:3000}
      cursor-timeout-ms: ${MONGODB_CURSOR_TIMEOUT:60000}

# Production Logging Configuration
logging:
  level:
    com.icetea.lotus.matching: INFO
    com.icetea.lotus.matching.infrastructure.messaging: WARN
    com.icetea.lotus.matching.infrastructure.exchange: INFO
    com.icetea.lotus.matching.infrastructure.futurecore: INFO
    org.apache.kafka: WARN
    org.springframework.kafka: WARN
    org.springframework.data.mongodb: WARN
    org.mongodb.driver: WARN
    root: WARN
  
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{50} - %msg%n"

# Production Feature Flags
features:
  experimental:
    advanced-matching-algorithms: true
    predictive-caching: true
    machine-learning-optimization: false  # Disabled until tested
    adaptive-batching: true
    dynamic-segmentation: true

# Production Settings
production:
  mode: true
  aggressive-optimization: true
  strict-monitoring: true
  auto-tuning: true

# Production Pod Configuration
pod:
  name: ${POD_NAME:matching-engine-prod-${HOSTNAME}}
  namespace: ${POD_NAMESPACE:production}
  ip: ${POD_IP}

# Production Circuit Breaker - Strict
resilience4j:
  circuitbreaker:
    instances:
      matching-engine:
        registerHealthIndicator: true
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 5
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 30s
        failureRateThreshold: 30
        eventConsumerBufferSize: 20

# Production Management
management:
  tracing:
    sampling:
      probability: 0.1  # Sample 10% for production
  endpoints:
    web:
      exposure:
        include: health,info,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: true
    tags:
      application: ${spring.application.name}
      environment: production

# Production Info
info:
  app:
    name: Matching Engine (Production)
    description: High-performance order matching engine - Production Environment
    version: 1.0.0
    environment: production
    architecture: Message-driven (Kafka)
    modules:
      - Exchange Module (Spot Trading)
      - Future-Core Module (Advanced Algorithms)
      - Production Monitoring
