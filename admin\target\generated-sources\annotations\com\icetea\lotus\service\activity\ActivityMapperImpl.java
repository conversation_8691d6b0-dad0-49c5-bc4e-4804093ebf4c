package com.icetea.lotus.service.activity;

import com.icetea.lotus.entity.Activity;
import com.icetea.lotus.model.ActivityRequest;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-13T16:41:24+0700",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ActivityMapperImpl implements ActivityMapper {

    @Override
    public void updateActivityFromRequest(ActivityRequest request, Activity activity) {
        if ( request == null ) {
            return;
        }

        if ( request.getAcceptUnit() != null ) {
            activity.setAcceptUnit( request.getAcceptUnit() );
        }
        if ( request.getActivityLink() != null ) {
            activity.setActivityLink( request.getActivityLink() );
        }
        if ( request.getAmountScale() != null ) {
            activity.setAmountScale( request.getAmountScale() );
        }
        if ( request.getBannerImageUrl() != null ) {
            activity.setBannerImageUrl( request.getBannerImageUrl() );
        }
        if ( request.getContent() != null ) {
            activity.setContent( request.getContent() );
        }
        if ( request.getDetail() != null ) {
            activity.setDetail( request.getDetail() );
        }
        if ( request.getEndTime() != null ) {
            activity.setEndTime( request.getEndTime() );
        }
        if ( request.getHoldLimit() != null ) {
            activity.setHoldLimit( request.getHoldLimit() );
        }
        if ( request.getHoldUnit() != null ) {
            activity.setHoldUnit( request.getHoldUnit() );
        }
        if ( request.getId() != null ) {
            activity.setId( request.getId() );
        }
        if ( request.getLimitTimes() != null ) {
            activity.setLimitTimes( request.getLimitTimes() );
        }
        if ( request.getLockedDays() != null ) {
            activity.setLockedDays( request.getLockedDays() );
        }
        if ( request.getLockedFee() != null ) {
            activity.setLockedFee( request.getLockedFee() );
        }
        if ( request.getLockedPeriod() != null ) {
            activity.setLockedPeriod( request.getLockedPeriod() );
        }
        if ( request.getLockedUnit() != null ) {
            activity.setLockedUnit( request.getLockedUnit() );
        }
        if ( request.getMiningDays() != null ) {
            activity.setMiningDays( request.getMiningDays() );
        }
        if ( request.getMiningInvite() != null ) {
            activity.setMiningInvite( request.getMiningInvite() );
        }
        if ( request.getMiningPeriod() != null ) {
            activity.setMiningPeriod( request.getMiningPeriod() );
        }
        if ( request.getMiningUnit() != null ) {
            activity.setMiningUnit( request.getMiningUnit() );
        }
        if ( request.getNoticeLink() != null ) {
            activity.setNoticeLink( request.getNoticeLink() );
        }
        if ( request.getPrice() != null ) {
            activity.setPrice( request.getPrice() );
        }
        if ( request.getPriceScale() != null ) {
            activity.setPriceScale( request.getPriceScale() );
        }
        if ( request.getReleaseAmount() != null ) {
            activity.setReleaseAmount( request.getReleaseAmount() );
        }
        if ( request.getReleasePercent() != null ) {
            activity.setReleasePercent( request.getReleasePercent() );
        }
        if ( request.getReleaseTimes() != null ) {
            activity.setReleaseTimes( request.getReleaseTimes() );
        }
        if ( request.getReleaseType() != null ) {
            activity.setReleaseType( request.getReleaseType() );
        }
        if ( request.getSettings() != null ) {
            activity.setSettings( request.getSettings() );
        }
        if ( request.getSmallImageUrl() != null ) {
            activity.setSmallImageUrl( request.getSmallImageUrl() );
        }
        if ( request.getStartTime() != null ) {
            activity.setStartTime( request.getStartTime() );
        }
        if ( request.getStatus() != null ) {
            activity.setStatus( request.getStatus() );
        }
        if ( request.getStep() != null ) {
            activity.setStep( request.getStep() );
        }
        if ( request.getTitle() != null ) {
            activity.setTitle( request.getTitle() );
        }
        if ( request.getTotalSupply() != null ) {
            activity.setTotalSupply( request.getTotalSupply() );
        }
        if ( request.getType() != null ) {
            activity.setType( request.getType() );
        }
        if ( request.getUnit() != null ) {
            activity.setUnit( request.getUnit() );
        }
    }
}
