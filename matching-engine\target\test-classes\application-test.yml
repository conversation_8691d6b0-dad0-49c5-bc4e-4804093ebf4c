# Test Configuration for Performance Tests
spring:
  
  # Disable unnecessary components for performance testing
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
  
  # JPA configuration (if needed)
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
  
  # Logging configuration
  logging:
    level:
      com.icetea.lotus.matching: INFO
      org.springframework: WARN
      org.hibernate: WARN
    pattern:
      console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Performance test specific configuration
performance:
  test:
    # Test data configuration
    symbol: "BTC/USDT"
    base-price: 50000
    price-variance: 1000
    
    # Load test configuration
    warmup-orders: 1000
    test-orders-per-thread: 5000
    concurrent-threads: 10
    
    # Stress test configuration
    stress-test:
      target-tps: 1000
      duration-seconds: 60
      max-threads: 20
    
    # Latency test configuration
    latency-test:
      sample-size: 10000
      percentiles: [50, 95, 99, 99.9]

# Matching engine configuration
matching-engine:
  # Spot trading configuration
  spot:
    algorithm: "CoinTraderV2"
    stp-mode: "CANCEL_MAKER"
    enable-trade-plate: true
  
  # Futures trading configuration
  futures:
    algorithm: "FIFO"
    stp-mode: "EXPIRE_MAKER"
    enable-trade-plate: true
    enable-stop-orders: true
  
  # Performance optimization
  performance:
    enable-metrics: true
    enable-monitoring: false
    batch-size: 100
    thread-pool-size: 20

# Disable external dependencies for testing
kafka:
  enabled: false

mongodb:
  enabled: false

redis:
  enabled: false

# JVM optimization for performance testing
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true
