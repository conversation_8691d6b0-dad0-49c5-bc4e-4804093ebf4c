package com.icetea.lotus.infrastructure.messaging.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.Order;

import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.entity.Transaction;
import com.icetea.lotus.core.domain.entity.Wallet;
import com.icetea.lotus.core.domain.repository.ContractRepository;
import com.icetea.lotus.core.domain.repository.PositionRepository;
import com.icetea.lotus.core.domain.repository.TradeRepository;
import com.icetea.lotus.core.domain.repository.TransactionRepository;
import com.icetea.lotus.core.domain.service.PositionService;
import com.icetea.lotus.core.domain.service.TransactionService;
import com.icetea.lotus.core.domain.service.WalletService;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.TransactionType;
import com.icetea.lotus.infrastructure.messaging.event.OrderEvent;
import com.icetea.lotus.infrastructure.service.MarketDataService;
import com.icetea.lotus.infrastructure.sharding.SmartShardingManager;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * Consumer cho OrderEvent
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderEventConsumer {

    private final SmartShardingManager smartShardingManager;

    private final TradeRepository tradeRepository;

    @Qualifier("positionPersistenceAdapter")
    private final PositionRepository positionRepository;

    private final ContractRepository contractRepository;
    private final TransactionRepository transactionRepository;

    private final PositionService positionService;
    private final WalletService walletService;
    private final TransactionService transactionService;
    private final MarketDataService marketDataService;

    private final ObjectMapper objectMapper;

    @Value("${topic-kafka.contract.order-events}")
    private String orderEventsTopic;

    /**
     * Xử lý OrderEvent
     *
     * @param message OrderEvent
     */
    @KafkaListener(
            topics = "${topic-kafka.contract.order-events}",
            containerFactory = "kafkaListenerContainerFactory",
            groupId = "contract-perpetual-futures-core-event"
    )
    @SneakyThrows
    public void handleOrderEvent(String message) {
        OrderEvent event = objectMapper.readValue(message, OrderEvent.class);

        if (event == null || event.getOrder() == null || event.getOrder().getSymbol() == null) {
            log.error("Nhận event không hợp lệ từ topic {}, event = {}", orderEventsTopic, event);
            return;
        }

        String symbol = event.getOrder().getSymbol().getValue();

        log.debug("Nhận event từ topic {}, type = {}, symbol = {}, orderId = {}",
                orderEventsTopic, event.getType(), symbol, event.getOrder().getOrderId());

        // Chỉ xử lý event nếu symbol được gán cho instance này
        if (!smartShardingManager.canProcessSymbol(symbol)) {
            log.debug("Bỏ qua event: {}, symbol: {} không được gán cho pod này", event.getType(), symbol);
            return;
        }

        // Không cần lấy matching engine vì chúng ta sẽ sử dụng OrderMatchingEngineService

        // Xử lý event theo loại
        switch (event.getType()) {
            case ORDER_PLACED:
                log.debug("Xử lý event ORDER_PLACED, symbol: {}", symbol);
                // Xử lý event ORDER_PLACED
                try {
                    handleOrderPlacedEvent(event, symbol);
                } catch (Exception e) {
                    log.error("Lỗi khi xử lý event ORDER_PLACED, symbol = {}", symbol, e);
                }
                break;

            case ORDER_CANCELLED:
                log.debug("Xử lý event ORDER_CANCELLED, symbol: {}", symbol);
                // Order đã được hủy trong matching engine, không cần gọi cancelOrder lại
                // Chỉ cần log để theo dõi event
                log.info("Order đã được hủy thành công trong matching engine, orderId: {}, symbol: {}", 
                        event.getOrder().getOrderId().getValue(), symbol);
                break;

            case ORDER_UPDATED:
                log.debug("Xử lý event ORDER_UPDATED, symbol: {}", symbol);
                // Cập nhật lệnh trong matching engine
                // Hiện tại chưa hỗ trợ cập nhật lệnh
                break;

            default:
                log.warn("Không hỗ trợ loại event: {}", event.getType());
                break;
        }
    }

    /**
     * Xử lý event ORDER_PLACED
     *
     * @param event  OrderEvent
     * @param symbol Symbol
     */
    private void handleOrderPlacedEvent(OrderEvent event, String symbol) {
        try {
            // Lấy thông tin lệnh và giao dịch
            Order order = event.getOrder();
            List<Trade> trades = event.getTrades();

            if (trades == null || trades.isEmpty()) {
                log.debug("Không có giao dịch nào được tạo ra, orderId = {}", order.getOrderId());
                return;
            }

            log.debug("Xử lý {} giao dịch cho lệnh {}, symbol = {}", trades.size(), order.getOrderId(), symbol);

            // 1. Lưu giao dịch vào cơ sở dữ liệu (nếu chưa được lưu)
            tradeRepository.saveAll(trades);

            // 2. Cập nhật vị thế
            for (Trade trade : trades) {
                // Lấy hợp đồng
                Optional<Contract> contractOpt = contractRepository.findBySymbolOptional(trade.getSymbol());
                if (contractOpt.isEmpty()) {
                    log.warn("Không tìm thấy hợp đồng cho symbol: {}", trade.getSymbol());
                    continue;
                }
                Contract contract = contractOpt.get();

                // Cập nhật vị thế cho người mua
                updatePositionForTrade(trade, trade.getBuyMemberId(), contract, PositionDirection.LONG);

                // Cập nhật vị thế cho người bán
                updatePositionForTrade(trade, trade.getSellMemberId(), contract, PositionDirection.SHORT);

                // 3. Cập nhật ví
//                updateWalletForTrade(trade, contract);

                // 4. Tạo giao dịch tài chính
                createTransactionsForTrade(trade);

                // 5. Cập nhật dữ liệu thị trường
                marketDataService.updateMarkPriceFromTrade(trade);
            }

            // 6. Kiểm tra thanh lý - TẮT THANH LÝ TỰ ĐỘNG
            log.debug("Bỏ qua kiểm tra thanh lý cho symbol: {} (đã tắt thanh lý tự động)", symbol);

            // TẮT THANH LÝ TỰ ĐỘNG - Comment liquidation check
            // liquidationCheckService.checkLiquidationForSymbol(symbol);
        } catch (Exception e) {
            log.error("Lỗi khi xử lý event ORDER_PLACED, symbol = {}", symbol, e);
            throw e;
        }
    }

    /**
     * Cập nhật vị thế cho giao dịch
     *
     * @param trade    Giao dịch
     * @param memberId ID của thành viên
     * @param contract Hợp đồng
     */
    private void updatePositionForTrade(Trade trade, Long memberId, Contract contract, PositionDirection direction) {
        try {
            Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(
                    memberId, trade.getSymbol(), PositionStatus.OPEN);

            Position updatedPosition;
            if (positionOpt.isPresent()) {
                Position position = positionOpt.get();

                boolean isSameDirection = position.getDirection() == direction;

                if (isSameDirection) {
                    // Mở thêm cùng chiều: cập nhật lại entry price, volume, liquidation price
                    updatedPosition = positionService.updatePositionAfterTrade(position, trade, contract);
                } else {
                    // Đóng bớt hoặc đóng hết
                    BigDecimal currentVolume = position.getVolume();
                    BigDecimal tradeVolume = trade.getVolume();

                    if (tradeVolume.compareTo(currentVolume) >= 0) {
                        // Đóng hết vị thế
                        position.setVolume(BigDecimal.ZERO);
                        position.setStatus(PositionStatus.CLOSED);
                        updatedPosition = position;
                    } else {
                        // Đóng bớt vị thế
                        updatedPosition = updatePositionAfterPartialClose(position, trade, contract);
                    }

                    // Tính realized PnL và cập nhật vào Wallet
                    Money closePrice = trade.getPrice();
                    BigDecimal closeVolumeUsdt = trade.getVolume().multiply(closePrice.getValue());
                    Money realizedPnl = calculateRealizedPnl(position, closePrice, closeVolumeUsdt);

                    updateWalletAfterPositionClose(
                            memberId,
                            realizedPnl,
                            trade.getSellFee().add(trade.getBuyFee()),
                            closeVolumeUsdt.divide(position.getLeverage(), 8, RoundingMode.HALF_UP)
                    );
                }
            } else {
                // Không có vị thế mở, tạo mới
                log.info("Tạo vị thế mới cho memberId = {}, symbol = {}, direction = {}, volume = {}",
                        memberId, trade.getSymbol().getValue(), direction, trade.getVolume());
                updatedPosition = positionService.createPositionFromTrade(trade, memberId, contract);
                log.info("Đã tạo vị thế mới: id = {}, direction = {}, volume = {}, status = {}",
                        updatedPosition.getId(), updatedPosition.getDirection(),
                        updatedPosition.getVolume(), updatedPosition.getStatus());
            }

            // Lưu vị thế
            Position savedPosition = positionRepository.save(updatedPosition);
            log.info("Đã lưu vị thế vào database: id = {}, memberId = {}, symbol = {}, direction = {}, volume = {}, status = {}",
                    savedPosition.getId(), memberId, trade.getSymbol().getValue(),
                    savedPosition.getDirection(), savedPosition.getVolume(), savedPosition.getStatus());

        } catch (Exception e) {
            log.error("Lỗi khi cập nhật vị thế cho giao dịch, tradeId = {}, memberId = {}", trade.getId(), memberId, e);
            throw e;
        }
    }


    public Position updatePositionAfterPartialClose(Position position, Trade trade, Contract contract) {
        BigDecimal currentVolume = position.getVolume();
        BigDecimal tradeVolume = trade.getVolume();

        // Volume còn lại sau khi đóng bớt
        BigDecimal remainingVolume = currentVolume.subtract(tradeVolume);

        if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
            // Nếu đóng hết vị thế, đánh dấu đóng
            position.setVolume(BigDecimal.ZERO);
            position.setStatus(PositionStatus.CLOSED);
            return position;
        }

        // Đóng bớt: KHÔNG cập nhật entry price!
        // Giữ nguyên entry price cũ, chỉ giảm volume và cập nhật liquidation price
        position.setVolume(remainingVolume);

        // Cập nhật liquidation price dựa trên entry price cũ, leverage, contract
        Money newLiquidationPrice = calculateLiquidationPrice(
                position.getOpenPrice(),
                position.getDirection(),
                position.getLeverage(),
                contract
        );
        position.setLiquidationPrice(newLiquidationPrice);

        // Nếu có break even price, có thể tính lại ở đây nếu cần

        return position;
    }

    public void updateWalletAfterPositionClose(Long memberId, Money realizedPnl, Money closeFee, BigDecimal marginReleased) {
        // 1. Lấy ví của member
        Wallet wallet = walletService.findOrCreateWallet(memberId, "USDT");
        if (wallet == null) {
            throw new RuntimeException("Không tìm thấy ví cho memberId: " + memberId);
        }

        // 2. Cộng realized PnL vào ví
        wallet.setBalance(wallet.getBalance().add(realizedPnl));

        // 3. Trừ phí đóng lệnh
        wallet.setBalance(wallet.getBalance().subtract(closeFee));

        // 4. Cộng lại phần margin đã giải phóng (marginReleased)
        wallet.setBalance(wallet.getBalance().add(Money.of(marginReleased)));

        // 5. Lưu lại ví
        walletService.save(wallet);
    }


//    private void updatePositionForTrade(Trade trade, Long memberId, Contract contract) {
//        try {
//            log.debug("Cập nhật vị thế cho giao dịch, tradeId = {}, memberId = {}, symbol = {}",
//                    trade.getId(), memberId, trade.getSymbol());
//
//            // 1. Tìm vị thế đang mở hiện tại của thành viên
//            Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(
//                    memberId, trade.getSymbol(), PositionStatus.OPEN);
//
//            Position updatedPosition;
//            if (positionOpt.isPresent()) {
//                // 2. Nếu có vị thế đang mở, cập nhật vị thế dựa trên giao dịch mới
//                Position position = positionOpt.get();
//
//                // Ghi log thông tin chi tiết về vị thế trước khi cập nhật ở cấp độ debug
//                if (log.isDebugEnabled()) {
//                    log.debug("Chi tiết vị thế trước khi cập nhật: memberId={}, symbol={}, direction={}, volume={}, " +
//                                    "entryPrice={}, liquidationPrice={}, leverage={}",
//                            memberId, trade.getSymbol().getValue(), position.getDirection(),
//                            position.getVolume(), position.getOpenPrice().getValue(),
//                            position.getLiquidationPrice() != null ? position.getLiquidationPrice().getValue() : "N/A",
//                            position.getLeverage());
//                }
//
//                // Cập nhật vị thế dựa trên giao dịch mới
//                // PositionServiceImpl sẽ tự động tính toán entry price, break even price, và các giá trị khác
//
//                updatedPosition = positionService.updatePositionAfterTrade(position, trade, contract);
//                log.debug("Đã cập nhật vị thế hiện tại cho memberId = {}, symbol = {}, volume = {}, status = {}",
//                        memberId, trade.getSymbol(), updatedPosition.getVolume(), updatedPosition.getStatus());
//
//                // Ghi log thông tin chi tiết về vị thế đã cập nhật
//                if (updatedPosition.getStatus() == PositionStatus.OPEN && log.isDebugEnabled()) {
//                    // Tính toán break even price với phí giao dịch (giả sử 0.1%)
//                    BigDecimal feeRate = new BigDecimal("0.001"); // 0.1%
//                    Money breakEvenPrice = updatedPosition.calculateBreakEvenPrice(feeRate);
//
//                    log.debug("Chi tiết vị thế sau khi cập nhật: memberId={}, symbol={}, direction={}, volume={}, " +
//                                    "entryPrice={}, liquidationPrice={}, breakEvenPrice={}, leverage={}",
//                            memberId, trade.getSymbol().getValue(), updatedPosition.getDirection(),
//                            updatedPosition.getVolume(), updatedPosition.getOpenPrice().getValue(),
//                            updatedPosition.getLiquidationPrice().getValue(), breakEvenPrice.getValue(),
//                            updatedPosition.getLeverage());
//                }
//            } else {
//                // 3. Nếu không có vị thế đang mở, tạo vị thế mới
//                updatedPosition = positionService.createPositionFromTrade(trade, memberId, contract);
//                log.debug("Đã tạo vị thế mới cho memberId = {}, symbol = {}, volume = {}, status = {}",
//                        memberId, trade.getSymbol(), updatedPosition.getVolume(), updatedPosition.getStatus());
//
//                // Ghi log thông tin chi tiết về vị thế mới
//                if (updatedPosition.getStatus() == PositionStatus.OPEN) {
//                    // Tính toán break even price với phí giao dịch (giả sử 0.1%)
//                    BigDecimal feeRate = new BigDecimal("0.001"); // 0.1%
//                    Money breakEvenPrice = updatedPosition.calculateBreakEvenPrice(feeRate);
//
//                    log.debug("Chi tiết vị thế mới: memberId={}, symbol={}, direction={}, volume={}, " +
//                                    "entryPrice={}, liquidationPrice={}, breakEvenPrice={}, leverage={}",
//                            memberId, trade.getSymbol().getValue(), updatedPosition.getDirection(),
//                            updatedPosition.getVolume(), updatedPosition.getOpenPrice().getValue(),
//                            updatedPosition.getLiquidationPrice().getValue(), breakEvenPrice.getValue(),
//                            updatedPosition.getLeverage());
//                }
//            }
//
//            // 4. Lưu vị thế vào cơ sở dữ liệu
//            Position savedPosition = positionRepository.save(updatedPosition);
//            log.debug("Đã lưu vị thế vào cơ sở dữ liệu, id = {}, memberId = {}, symbol = {}, status = {}",
//                    savedPosition.getId(), memberId, trade.getSymbol(), savedPosition.getStatus());
//
//        } catch (Exception e) {
//            log.error("Lỗi khi cập nhật vị thế cho giao dịch, tradeId = {}, memberId = {}", trade.getId(), memberId, e);
//            throw e;
//        }
//    }

    /**
     * Tính toán giá thanh lý (Liquidation Price) dựa trên công thức chuẩn.
     * <p>
     * Công thức:
     * <ul>
     *   <li>Đối với vị thế <b>LONG</b>:
     *     <pre>
     *     liquidationPrice = entryPrice × (1 - 1 / leverage)
     *     </pre>
     *   </li>
     *   <li>Đối với vị thế <b>SHORT</b>:
     *     <pre>
     *     liquidationPrice = entryPrice × (1 + 1 / leverage)
     *     </pre>
     *   </li>
     * </ul>
     *
     * @param entryPrice Giá vào lệnh (Entry Price)
     * @param direction Hướng vị thế (LONG hoặc SHORT)
     * @param leverage Đòn bẩy sử dụng
     * @param contract Thông tin hợp đồng, có thể sử dụng thêm để điều chỉnh logic (nếu cần)
     * @return Giá thanh lý (Liquidation Price) dưới dạng {@link Money}
     */
    private Money calculateLiquidationPrice(Money entryPrice, PositionDirection direction, BigDecimal leverage, Contract contract) {
        // Kiểm tra leverage không bằng 0 để tránh lỗi chia cho 0
        if (leverage == null || leverage.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Leverage must be greater than zero");
        }

        // ✅ SỬ DỤNG công thức đúng từ Position entity thay vì công thức sai
        BigDecimal volume = BigDecimal.ONE; // Normalized volume

        // Tính margin = (Entry Price × Volume) / Leverage
        Money margin = Money.of(entryPrice.getValue().multiply(volume).divide(leverage, 8, RoundingMode.HALF_UP));

        // Tạo temporary position để sử dụng công thức đúng
        Position tempPosition = Position.builder()
                .direction(direction)
                .openPrice(entryPrice)
                .volume(volume)
                .margin(margin)
                .build();

        // Sử dụng công thức chính xác từ Position entity
        return tempPosition.calculateLiquidationPrice(contract.getMaintenanceMarginRate().getValue());
    }



    /**
     * Cập nhật ví cho giao dịch futures theo cơ chế Binance
     *
     * @param trade    Giao dịch đã khớp
     * @param contract Hợp đồng futures
     */
//    private void updateWalletForTrade(Trade trade, Contract contract) {
//        try {
//            log.info("Bắt đầu cập nhật ví cho giao dịch futures, tradeId = {}", trade.getId());
//
//            // Cập nhật ví cho bên mua (Long)
//            updateWalletForMember(trade, trade.getBuyMemberId(), trade.getBuyFee(), contract, OrderDirection.BUY);
//
//            // Cập nhật ví cho bên bán (Short)
//            updateWalletForMember(trade, trade.getSellMemberId(), trade.getSellFee(), contract, OrderDirection.SELL);
//
//            log.info("Hoàn thành cập nhật ví cho giao dịch futures, tradeId = {}", trade.getId());
//        } catch (Exception e) {
//            log.error("Lỗi khi cập nhật ví cho giao dịch futures, tradeId = {}", trade.getId(), e);
//            throw new RuntimeException("Failed to update wallet for futures trade", e);
//        }
//    }

    /**
     * Cập nhật ví cho thành viên theo cơ chế Binance Futures
     *
     * @param trade     Giao dịch
     * @param memberId  ID của thành viên
     * @param fee       Phí giao dịch
     * @param contract  Hợp đồng
     * @param orderSide BUY hoặc SELL
     */
//    private void updateWalletForMember(Trade trade, Long memberId, Money fee, Contract contract, OrderDirection orderSide) {
//        try {
//            log.debug("Cập nhật ví futures cho thành viên, tradeId = {}, memberId = {}, side = {}, fee = {}",
//                    trade.getId(), memberId, orderSide, fee);
//
//            // 1. Tìm hoặc tạo ví USDT (Quote coin cho futures)
//            Wallet wallet = walletService.findOrCreateWallet(memberId, contract.getQuoteCoin());
//
//            // 2. Tìm vị thế hiện tại của member
//            Optional<Position> existingPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(
//                    memberId, trade.getSymbol(), PositionStatus.OPEN);
//
//            // 3. Tính toán margin yêu cầu cho trade này
//            BigDecimal leverage = BigDecimal.valueOf(contract.getLeverageMax());
//            Money requiredMargin = calculateRequiredMarginFromUsdt(trade.getVolume(), leverage);
//
//            if (existingPositionOpt.isPresent()) {
//                // Có vị thế đang mở - xử lý tăng/giảm vị thế hoặc đóng vị thế
//                Position existingPosition = existingPositionOpt.get();
//                handleExistingPosition(wallet, trade, existingPosition, fee, orderSide, requiredMargin, contract);
//            } else {
//                // Không có vị thế - mở vị thế mới
//                handleNewPosition(wallet, trade, fee, requiredMargin);
//            }
//
//            log.debug("Hoàn thành cập nhật ví futures cho thành viên, memberId = {}", memberId);
//
//        } catch (Exception e) {
//            log.error("Lỗi khi cập nhật ví futures cho thành viên, tradeId = {}, memberId = {}",
//                    trade.getId(), memberId, e);
//            throw e;
//        }
//    }

    /**
     * Xử lý khi đã có vị thế đang mở
     */
//    private void handleExistingPosition(Wallet wallet, Trade trade, Position existingPosition,
//                                        Money fee, OrderDirection orderSide, Money requiredMargin, Contract contract) {
//
//        PositionDirection existingSide = existingPosition.getDirection();
//        PositionDirection tradeSide = (orderSide == OrderDirection.BUY) ? PositionDirection.LONG : PositionDirection.SHORT;
//
//        if (existingSide == tradeSide) {
//            // Cùng chiều - tăng vị thế (Add to position)
//            handleIncreasePosition(wallet, trade, existingPosition, fee, requiredMargin);
//        } else {
//            // Ngược chiều - giảm vị thế hoặc đóng vị thế (Reduce/Close position)
//            handleReduceOrClosePosition(wallet, trade, existingPosition, fee, requiredMargin, contract);
//        }
//    }

    /**
     * Xử lý tăng vị thế (Add to position)
     */
//    private void handleIncreasePosition(Wallet wallet, Trade trade, Position existingPosition,
//                                        Money fee, Money requiredMargin) {
//
//        log.debug("Tăng vị thế, memberId = {}, currentVolume = {}, addVolume = {}",
//                wallet.getMemberId(), existingPosition.getVolume(), trade.getVolume());
//
//        // 1. Trừ phí giao dịch từ available balance
//        wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), fee);
//
//        // 2. Đóng băng margin bổ sung
//        wallet = walletService.freezeBalance(wallet.getMemberId(), wallet.getCoin(), requiredMargin);
//
//        // 3. Cập nhật total fee
//        walletService.updateTotalFee(wallet.getId().getValue(), fee);
//
//        log.debug("Đã tăng vị thế, frozen margin = {}, fee = {}", requiredMargin, fee);
//    }

    /**
     * Xử lý giảm/đóng vị thế (Reduce/Close position)
     */
//    private void handleReduceOrClosePosition(Wallet wallet, Trade trade, Position existingPosition,
//                                             Money fee, Money requiredMargin, Contract contract) {
//
//        BigDecimal tradeVolume = trade.getVolume();
//        BigDecimal existingVolume = existingPosition.getVolume();
//
//        log.debug("Xử lý giảm/đóng vị thế, memberId = {}, existingVolume = {}, tradeVolume = {}",
//                wallet.getMemberId(), existingVolume, tradeVolume);
//
//        // 1. Trừ phí giao dịch
//        wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), fee);
//        wallet = walletService.updateTotalFee(wallet.getId().getValue(), fee);
//
//        if (tradeVolume.compareTo(existingVolume) >= 0) {
//            // Đóng toàn bộ vị thế
//            handleFullPositionClose(wallet, trade, existingPosition);
//
//            // Nếu volume trade > existing volume, tạo vị thế mới theo chiều ngược lại
//            if (tradeVolume.compareTo(existingVolume) > 0) {
//                BigDecimal remainingVolume = tradeVolume.subtract(existingVolume);
//                handleNewPositionAfterClose(wallet, trade, remainingVolume, requiredMargin);
//            }
//        } else {
//            // Giảm một phần vị thế
//            handlePartialPositionClose(wallet, trade, existingPosition);
//        }
//    }

    /**
     * Đóng toàn bộ vị thế
     */
//    private void handleFullPositionClose(Wallet wallet, Trade trade, Position existingPosition) {
//
//        log.debug("Đóng toàn bộ vị thế, memberId = {}, volume = {}",
//                wallet.getMemberId(), existingPosition.getVolume());
//
//        // 1. Tính toán PnL đã thực hiện
//        Money realizedPnl = calculateRealizedPnl(existingPosition, trade.getPrice(), existingPosition.getVolume());
//
//        // 2. Giải phóng toàn bộ margin đã đóng băng cho vị thế này
//        Money positionMargin = existingPosition.getMargin();
//        wallet = walletService.unfreezeBalance(wallet.getMemberId(), wallet.getCoin(), positionMargin);
//
//        // 3. Cập nhật PnL đã thực hiện
//        wallet = walletService.updateRealizedPnl(wallet.getId().getValue(), realizedPnl);
//
//        // 4. Cập nhật available balance dựa trên PnL
//        if (realizedPnl.getValue().compareTo(BigDecimal.ZERO) > 0) {
//            // Lợi nhuận - tăng available balance
//            wallet = walletService.increaseBalance(wallet.getMemberId(), wallet.getCoin(), realizedPnl);
//        } else if (realizedPnl.getValue().compareTo(BigDecimal.ZERO) < 0) {
//            // Lỗ - giảm available balance
//            Money lossAmount = Money.of(realizedPnl.getValue().abs());
//            wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), lossAmount);
//        }
//
//        log.info("Đã đóng toàn bộ vị thế, realizedPnl = {}, releasedMargin = {}",
//                realizedPnl, positionMargin);
//    }

    /**
     * Giảm một phần vị thế
     */
//    private void handlePartialPositionClose(Wallet wallet, Trade trade, Position existingPosition) {
//
//        BigDecimal closeRatio = trade.getVolume().divide(existingPosition.getVolume(), 8, RoundingMode.HALF_UP);
//
//        log.debug("Giảm một phần vị thế, memberId = {}, closeRatio = {}",
//                wallet.getMemberId(), closeRatio);
//
//        // 1. Tính toán PnL đã thực hiện cho phần đóng
//        Money realizedPnl = calculateRealizedPnl(existingPosition, trade.getPrice(), trade.getVolume());
//
//        // 2. Giải phóng margin tương ứng với phần vị thế đóng
//        Money releasedMargin = Money.of(existingPosition.getMargin().getValue().multiply(closeRatio));
//        wallet = walletService.unfreezeBalance(wallet.getMemberId(), wallet.getCoin(), releasedMargin);
//
//        // 3. Cập nhật PnL đã thực hiện
//        wallet = walletService.updateRealizedPnl(wallet.getId().getValue(), realizedPnl);
//
//        // 4. Cập nhật available balance
//        if (realizedPnl.getValue().compareTo(BigDecimal.ZERO) > 0) {
//            wallet = walletService.increaseBalance(wallet.getMemberId(), wallet.getCoin(), realizedPnl);
//        } else if (realizedPnl.getValue().compareTo(BigDecimal.ZERO) < 0) {
//            Money lossAmount = Money.of(realizedPnl.getValue().abs());
//            wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), lossAmount);
//        }
//
//        log.debug("Đã giảm một phần vị thế, realizedPnl = {}, releasedMargin = {}",
//                realizedPnl, releasedMargin);
//    }

    /**
     * Xử lý mở vị thế mới
     */
//    private void handleNewPosition(Wallet wallet, Trade trade, Money fee, Money requiredMargin) {
//
//        log.debug("Mở vị thế mới, memberId = {}, volume = {}, margin = {}",
//                wallet.getMemberId(), trade.getVolume(), requiredMargin);
//
//        // 1. Trừ phí giao dịch
//        wallet = walletService.decreaseBalance(wallet.getMemberId(), wallet.getCoin(), fee);
//
//        // 2. Đóng băng margin
//        wallet = walletService.freezeBalance(wallet.getMemberId(), wallet.getCoin(), requiredMargin);
//
//        // 3. Cập nhật total fee
//        wallet = walletService.updateTotalFee(wallet.getId().getValue(), fee);
//
//        log.debug("Đã mở vị thế mới, frozenMargin = {}, fee = {}", requiredMargin, fee);
//    }

    /**
     * Tạo vị thế mới sau khi đóng vị thế cũ (khi volume trade > existing volume)
     */
//    private void handleNewPositionAfterClose(Wallet wallet, Trade trade, BigDecimal remainingVolume,
//                                             Money requiredMargin) {
//
//        log.debug("Tạo vị thế mới sau khi đóng, memberId = {}, remainingVolume = {}",
//                wallet.getMemberId(), remainingVolume);
//
//        // Tính toán margin cho volume còn lại
//        BigDecimal ratio = remainingVolume.divide(trade.getVolume(), 8, RoundingMode.HALF_UP);
//        Money newPositionMargin = Money.of(requiredMargin.getValue().multiply(ratio));
//
//        // Đóng băng margin cho vị thế mới
//        wallet = walletService.freezeBalance(wallet.getMemberId(), wallet.getCoin(), newPositionMargin);
//
//        log.debug("Đã tạo vị thế mới, newMargin = {}", newPositionMargin);
//    }

    /**
     * Tính toán margin yêu cầu khi volume tính bằng USDT
     *
     * @param volumeUsdt Volume tính bằng USDT (notional value)
     * @param leverage   Đòn bẩy
     * @return Margin yêu cầu
     */
//    private Money calculateRequiredMarginFromUsdt(BigDecimal volumeUsdt, BigDecimal leverage) {
//        // Margin = Volume USDT / Leverage
//        BigDecimal marginValue = volumeUsdt.divide(leverage, 8, RoundingMode.HALF_UP);
//        return Money.of(marginValue);
//    }


    /**
     * Tính toán PnL đã thực hiện khi đóng vị thế
     *
     * @param position        Vị thế hiện tại
     * @param closePrice      Giá đóng vị thế
     * @param closeVolumeUsdt Volume đóng tính bằng USDT (notional value)
     * @return PnL đã thực hiện
     */
    private Money calculateRealizedPnl(Position position, Money closePrice, BigDecimal closeVolumeUsdt) {
        try {
            BigDecimal entryPrice = position.getOpenPrice().getValue();
            BigDecimal exitPrice = closePrice.getValue();
            PositionDirection positionSide = position.getDirection();

            // Volume đóng tính bằng USDT, cần chuyển đổi thành số lượng coin
            // closeVolumeUsdt = closePrice * coinAmount
            // => coinAmount = closeVolumeUsdt / closePrice
            BigDecimal coinAmountClosed = closeVolumeUsdt.divide(exitPrice, 8, RoundingMode.HALF_UP);

            // Tính toán PnL theo công thức Binance Futures
            BigDecimal pnlTotal;
            if (positionSide == PositionDirection.LONG) {
                // Long: PnL = (Exit Price - Entry Price) * CoinAmount
                pnlTotal = exitPrice.subtract(entryPrice).multiply(coinAmountClosed);
            } else {
                // Short: PnL = (Entry Price - Exit Price) * CoinAmount
                pnlTotal = entryPrice.subtract(exitPrice).multiply(coinAmountClosed);
            }

            log.debug("Tính toán PnL với volume USDT: entryPrice = {}, exitPrice = {}, side = {}, " +
                            "volumeUsdt = {}, coinAmount = {}, pnl = {}",
                    entryPrice, exitPrice, positionSide, closeVolumeUsdt, coinAmountClosed, pnlTotal);

            return Money.of(pnlTotal);

        } catch (Exception e) {
            log.error("Lỗi khi tính toán PnL đã thực hiện cho position = {}", position.getId(), e);
            throw new RuntimeException("Failed to calculate realized PnL", e);
        }
    }


    /**
     * Tạo giao dịch tài chính cho giao dịch
     *
     * @param trade Giao dịch
     */
    private void createTransactionsForTrade(Trade trade) {
        try {
            // Tạo giao dịch phí cho bên mua
            createFeeTransaction(trade, trade.getBuyMemberId(), trade.getBuyFee());

            // Tạo giao dịch phí cho bên bán
            createFeeTransaction(trade, trade.getSellMemberId(), trade.getSellFee());

            // Tạo giao dịch lợi nhuận nếu đóng vị thế
            if (isClosingPosition(trade)) {
                createPnlTransaction(trade);
            }
        } catch (Exception e) {
            log.error("Lỗi khi tạo giao dịch tài chính cho giao dịch, tradeId = {}", trade.getId(), e);
            throw e;
        }
    }

    /**
     * Tạo giao dịch phí
     *
     * @param trade    Giao dịch
     * @param memberId ID của thành viên
     * @param fee      Phí giao dịch
     */
    private void createFeeTransaction(Trade trade, Long memberId, Money fee) {
        // Sử dụng phương thức factory có sẵn trong Transaction để tạo giao dịch phí
        Transaction transaction = Transaction.createFeeTransaction(
                memberId,
                trade.getSymbol(),
                null,
                fee
        );

        // Thiết lập referenceId là ID của giao dịch
        transaction = Transaction.builder()
                .id(transaction.getId())
                .memberId(transaction.getMemberId())
                .symbol(transaction.getSymbol())
                .coin(transaction.getCoin())
                .amount(transaction.getAmount())
                .fee(transaction.getFee())
                .type(transaction.getType())
                .createTime(transaction.getCreateTime())
                .flag(transaction.getFlag())
                .isReward(transaction.getIsReward())
                .referenceId(trade.getId().getValue())
                .build();

        // Lưu giao dịch
        transactionRepository.save(transaction);

        log.debug("Đã tạo giao dịch phí cho memberId = {}, fee = {}", memberId, fee);
    }

    /**
     * Tạo giao dịch lợi nhuận
     *
     * @param trade Giao dịch
     */
    private void createPnlTransaction(Trade trade) {
        try {
            log.debug("Tạo giao dịch lợi nhuận cho giao dịch, tradeId = {}", trade.getId());

            // 1. Tìm vị thế đang mở hiện tại của người mua và người bán
            Optional<Position> buyerPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(
                    trade.getBuyMemberId(), trade.getSymbol(), PositionStatus.OPEN);
            Optional<Position> sellerPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(
                    trade.getSellMemberId(), trade.getSymbol(), PositionStatus.OPEN);

            // 2. Tính toán lợi nhuận cho người mua
            if (buyerPositionOpt.isPresent()) {
                Position buyerPosition = buyerPositionOpt.get();

                // Kiểm tra xem đây có phải là đóng vị thế không
                if (buyerPosition.getDirection() != null &&
                        buyerPosition.getDirection() == PositionDirection.SHORT) {

                    // Tính toán lợi nhuận dựa trên vị thế và giá đóng
                    // Sử dụng positionService để tính toán lợi nhuận
                    Money buyerPnl = positionService.calculateUnrealizedProfit(buyerPosition, trade.getPrice());

                    // Xác định loại giao dịch (PROFIT hoặc LOSS)
                    TransactionType buyerType = buyerPnl.getValue().compareTo(BigDecimal.ZERO) >= 0 ?
                            TransactionType.TRADE_PROFIT : TransactionType.TRADE_LOSS;

                    // Tạo giao dịch lợi nhuận cho người mua
                    Money pnlAmount = buyerPnl.getValue().compareTo(BigDecimal.ZERO) < 0 ?
                            Money.of(buyerPnl.getValue().abs()) : buyerPnl;

                    transactionService.createTransaction(
                            trade.getBuyMemberId(),
                            pnlAmount,
                            buyerType,
                            trade.getId().getValue(),
                            "PnL for trade " + trade.getId().getValue() + " (buyer)",
                            trade.getSymbol()
                    );

                    log.debug("Đã tạo giao dịch lợi nhuận cho người mua, tradeId = {}, memberId = {}, amount = {}, type = {}",
                            trade.getId(), trade.getBuyMemberId(), pnlAmount, buyerType);
                }
            }

            // 3. Tính toán lợi nhuận cho người bán
            if (sellerPositionOpt.isPresent()) {
                Position sellerPosition = sellerPositionOpt.get();

                // Kiểm tra xem đây có phải là đóng vị thế không
                if (sellerPosition.getDirection() != null &&
                        sellerPosition.getDirection() == PositionDirection.LONG) {

                    // Tính toán lợi nhuận dựa trên vị thế và giá đóng
                    // Sử dụng positionService để tính toán lợi nhuận
                    Money sellerPnl = positionService.calculateUnrealizedProfit(sellerPosition, trade.getPrice());

                    // Xác định loại giao dịch (PROFIT hoặc LOSS)
                    TransactionType sellerType = sellerPnl.getValue().compareTo(BigDecimal.ZERO) >= 0 ?
                            TransactionType.TRADE_PROFIT : TransactionType.TRADE_LOSS;

                    // Tạo giao dịch lợi nhuận cho người bán
                    Money pnlAmount = sellerPnl.getValue().compareTo(BigDecimal.ZERO) < 0 ?
                            Money.of(sellerPnl.getValue().abs()) : sellerPnl;

                    transactionService.createTransaction(
                            trade.getSellMemberId(),
                            pnlAmount,
                            sellerType,
                            trade.getId().getValue(),
                            "PnL for trade " + trade.getId().getValue() + " (seller)",
                            trade.getSymbol()
                    );

                    log.debug("Đã tạo giao dịch lợi nhuận cho người bán, tradeId = {}, memberId = {}, amount = {}, type = {}",
                            trade.getId(), trade.getSellMemberId(), pnlAmount, sellerType);
                }
            }
        } catch (Exception e) {
            log.error("Lỗi khi tạo giao dịch lợi nhuận cho giao dịch, tradeId = {}", trade.getId(), e);
            throw e;
        }
    }

    /**
     * Kiểm tra xem giao dịch có đóng vị thế không
     *
     * @param trade Giao dịch
     * @return true nếu đóng vị thế, false nếu không
     */
    private boolean isClosingPosition(Trade trade) {
        try {
            log.debug("Kiểm tra xem giao dịch có đóng vị thế không, tradeId = {}", trade.getId());

            // 1. Tìm vị thế đang mở hiện tại của thành viên
            Optional<Position> buyerPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(
                    trade.getBuyMemberId(), trade.getSymbol(), PositionStatus.OPEN);
            Optional<Position> sellerPositionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(
                    trade.getSellMemberId(), trade.getSymbol(), PositionStatus.OPEN);

            // 2. Kiểm tra xem direction của trade có ngược với direction của vị thế không
            if (buyerPositionOpt.isPresent()) {
                Position buyerPosition = buyerPositionOpt.get();
                // Nếu người mua đang có vị thế bán, thì đây là đóng vị thế
                if (buyerPosition.getDirection() != null &&
                        buyerPosition.getDirection() == PositionDirection.SHORT) {
                    log.debug("Giao dịch đóng vị thế cho người mua, tradeId = {}, memberId = {}",
                            trade.getId(), trade.getBuyMemberId());
                    return true;
                }
            }

            if (sellerPositionOpt.isPresent()) {
                Position sellerPosition = sellerPositionOpt.get();
                // Nếu người bán đang có vị thế mua, thì đây là đóng vị thế
                if (sellerPosition.getDirection() != null &&
                        sellerPosition.getDirection() == PositionDirection.LONG) {
                    log.debug("Giao dịch đóng vị thế cho người bán, tradeId = {}, memberId = {}",
                            trade.getId(), trade.getSellMemberId());
                    return true;
                }
            }

            // Không có vị thế nào được đóng
            return false;
        } catch (Exception e) {
            log.error("Lỗi khi kiểm tra đóng vị thế, tradeId = {}", trade.getId(), e);
            return false;
        }
    }


}
