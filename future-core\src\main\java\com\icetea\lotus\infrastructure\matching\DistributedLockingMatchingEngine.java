package com.icetea.lotus.infrastructure.matching;

import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.repository.ContractRepository;
import com.icetea.lotus.core.domain.repository.OrderRepository;
import com.icetea.lotus.core.domain.service.LastPriceService;
import com.icetea.lotus.core.domain.entity.OrderBook;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.domain.entity.MatchingAlgorithm;
import com.icetea.lotus.core.common.LogMessages;
import com.icetea.lotus.infrastructure.exception.SymbolNotOwnedByThisPodException;
import com.icetea.lotus.infrastructure.matching.distributed.DistributedLockFreeMatchingEngine;
import com.icetea.lotus.infrastructure.sharding.SmartShardingManager;
import com.icetea.lotus.infrastructure.util.SnowflakeIdGenerator;
import com.icetea.lotus.infrastructure.matching.stp.SelfTradePreventionService;
import com.icetea.lotus.infrastructure.pool.ObjectPoolManager;
import com.icetea.lotus.infrastructure.persistence.mongodb.service.MongoOrderBookSnapshotService;
import com.icetea.lotus.infrastructure.locking.FineGrainedLockManager;
import com.icetea.lotus.infrastructure.sharding.IncrementalSnapshotManager;
import com.icetea.lotus.infrastructure.persistence.mongodb.service.ChangeBasedSnapshotService;
import com.icetea.lotus.infrastructure.persistence.mongodb.service.SnapshotProvider;
import com.icetea.lotus.infrastructure.matching.distributed.DistributedOrderBookSnapshot;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Matching Engine phân tán sử dụng Redis để đảm bảo chỉ có một instance xử lý một symbol tại một thời điểm
 * Tạo DistributedLockFreeMatchingEngine khi cần thiết thay vì inject
 */
@Slf4j
@Service
public class DistributedLockingMatchingEngine implements SnapshotProvider {

    private final RedissonClient redissonClient;
    private final SmartShardingManager smartShardingManager;
    private final ContractRepository contractRepository;
    private final OrderRepository orderRepository;
    private final SnowflakeIdGenerator snowflakeIdGenerator;
    private final ApplicationEventPublisher eventPublisher;
    private final LastPriceService lastPriceService;
    private final SelfTradePreventionService stpService;
    private final ObjectPoolManager objectPoolManager;
    private final MongoOrderBookSnapshotService mongoSnapshotService;
    private final FineGrainedLockManager fineGrainedLockManager;
    private final IncrementalSnapshotManager incrementalSnapshotManager;
    private final ChangeBasedSnapshotService changeBasedSnapshotService;

    // Cache cho các matching engine theo symbol
    private final Map<String, DistributedLockFreeMatchingEngine> matchingEngines = new ConcurrentHashMap<>();


    /**
     * Khởi tạo DistributedLockingMatchingEngine
     */
    @Autowired
    public DistributedLockingMatchingEngine(RedissonClient redissonClient,
                                            SmartShardingManager smartShardingManager,
                                            ContractRepository contractRepository,
                                            OrderRepository orderRepository,
                                            SnowflakeIdGenerator snowflakeIdGenerator,
                                            ApplicationEventPublisher eventPublisher,
                                            LastPriceService lastPriceService,
                                            SelfTradePreventionService stpService,
                                            ObjectPoolManager objectPoolManager,
                                            MongoOrderBookSnapshotService mongoSnapshotService,
                                            FineGrainedLockManager fineGrainedLockManager,
                                            IncrementalSnapshotManager incrementalSnapshotManager,
                                            ChangeBasedSnapshotService changeBasedSnapshotService) {
        this.redissonClient = redissonClient;
        this.smartShardingManager = smartShardingManager;
        this.contractRepository = contractRepository;
        this.orderRepository = orderRepository;
        this.snowflakeIdGenerator = snowflakeIdGenerator;
        this.eventPublisher = eventPublisher;
        this.lastPriceService = lastPriceService;
        this.stpService = stpService;
        this.objectPoolManager = objectPoolManager;
        this.mongoSnapshotService = mongoSnapshotService;
        this.fineGrainedLockManager = fineGrainedLockManager;
        this.incrementalSnapshotManager = incrementalSnapshotManager;
        this.changeBasedSnapshotService = changeBasedSnapshotService;

        log.info(LogMessages.OrderMatching.INFO_DISTRIBUTED_ENGINE_INITIALIZED());
    }

    /**
     * Validate symbol ownership and execute operation
     */
    private <T> T executeWithSymbolValidation(String symbolStr, SymbolOperation<T> operation) throws Exception {
        // Sử dụng SmartShardingManager thay vì shardingManager cũ
        if (!smartShardingManager.canProcessSymbol(symbolStr)) {
            log.warn(LogMessages.OrderMatching.WARN_SYMBOL_NOT_OWNED(), symbolStr);
            throw new SymbolNotOwnedByThisPodException(
                    "Symbol " + symbolStr + " không thể được xử lý bởi pod này");
        }

        return operation.execute();
    }

    /**
     * Functional interface for symbol operations
     */
    @FunctionalInterface
    private interface SymbolOperation<T> {
        T execute() throws Exception;
    }

    /**
     * Lấy hoặc tạo matching engine cho symbol (public method for external access)
     *
     * @param symbol Symbol
     * @return DistributedLockFreeMatchingEngine
     */
    public DistributedLockFreeMatchingEngine getOrCreateMatchingEngine(Symbol symbol) {
        return getOrCreateMatchingEngineInternal(symbol);
    }

    /**
     * Lấy hoặc tạo matching engine cho symbol (internal method)
     *
     * @param symbol Symbol
     * @return DistributedLockFreeMatchingEngine
     */
    private DistributedLockFreeMatchingEngine getOrCreateMatchingEngineInternal(Symbol symbol) {
        String symbolStr = symbol.getValue();

        // Lấy matching engine từ cache
        DistributedLockFreeMatchingEngine engine = matchingEngines.get(symbolStr);

        if (engine == null) {
            // Tìm hợp đồng
            Contract contract = contractRepository.findBySymbol(symbol);

            if (contract == null) {
                log.error(LogMessages.OrderMatching.ERROR_CONTRACT_NOT_FOUND(), symbol);
                return null;
            }

            // Tạo matching engine mới với all optimization components
            engine = new DistributedLockFreeMatchingEngine(symbol, contract, orderRepository,
                    snowflakeIdGenerator, eventPublisher, redissonClient, lastPriceService, stpService,
                    objectPoolManager, mongoSnapshotService, changeBasedSnapshotService);
            matchingEngines.put(symbolStr, engine);

            log.info(LogMessages.OrderMatching.INFO_ENGINE_INITIALIZED(), symbol);
        }

        return engine;
    }

    /**
     * Xử lý lệnh mới với symbol ownership validation (no locks)
     *
     * @param order Lệnh cần xử lý
     * @return Danh sách các giao dịch được tạo ra
     */
    public List<Trade> processOrder(Order order) {
        String symbolStr = order.getSymbol().getValue();
        String threadName = Thread.currentThread().getName();
        long startTime = System.currentTimeMillis();

        log.debug("Thread {} bắt đầu xử lý lệnh cho symbol: {}", threadName, symbolStr);

        try {
            // Sử dụng symbol validation thay vì lock
            return executeWithSymbolValidation(symbolStr, () -> {
                // Lấy hoặc tạo matching engine cho symbol
                DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(order.getSymbol());
                if (engine == null) {
                    log.error("Không thể tạo matching engine cho symbol: {}", order.getSymbol().getValue());
                    throw new RuntimeException("Không thể tạo matching engine cho symbol: " + order.getSymbol().getValue());
                }

                // Xử lý lệnh với matching engine (CAS-based lock-free)
                List<Trade> trades = engine.processOrder(order);

                // INCREMENTAL SNAPSHOT: Track order changes for efficient shard reassignment
                incrementalSnapshotManager.trackOrderChange(symbolStr, order,
                        IncrementalSnapshotManager.ChangeType.ADD);

                log.debug("Thread {} đã xử lý xong lệnh cho symbol: {}, tạo ra {} giao dịch", threadName, symbolStr, trades.size());
                return trades;
            });

        } catch (SymbolNotOwnedByThisPodException e) {
            // Symbol không thuộc pod này - không retry
            log.warn("Thread {} không thể xử lý lệnh cho symbol: {} (not owned by this pod)", threadName, symbolStr);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Lỗi không mong đợi khi xử lý lệnh cho symbol: {} bởi thread: {}", symbolStr, threadName, e);
            throw new RuntimeException("Lỗi khi xử lý lệnh: " + e.getMessage(), e);
        } finally {
            long totalTime = System.currentTimeMillis() - startTime;
            log.debug("Thread {} hoàn thành xử lý lệnh cho symbol: {} trong {}ms", threadName, symbolStr, totalTime);
        }
    }

    /**
     * FINE-GRAINED LOCKING: Hủy lệnh với order-level lock
     *
     * @param orderId ID lệnh cần hủy
     * @param symbol  Symbol của lệnh
     * @return true nếu hủy thành công, false nếu không
     */
    public boolean cancelOrder(OrderId orderId, Symbol symbol) {
        String symbolStr = symbol.getValue();

        try {
            // Sử dụng symbol validation
            return executeWithSymbolValidation(symbolStr, () -> {

                // FINE-GRAINED LOCKING: Use order-level lock for highest granularity
                return fineGrainedLockManager.executeWithOrderLock(orderId.getValue(), () -> {
                    // Lấy hoặc tạo matching engine cho symbol
                    DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(symbol);
                    if (engine == null) {
                        log.warn("Không thể tạo matching engine cho symbol: {}", symbolStr);
                        return false;
                    }

                    // FIXED: Get order before cancelling to track changes properly
                    Optional<Order> orderToCancel = engine.getOrder(orderId);

                    // Hủy lệnh với matching engine (lock-free within order scope)
                    boolean cancelled = engine.cancelOrder(orderId);

                    // Track incremental changes if cancelled successfully
                    if (cancelled && orderToCancel.isPresent()) {
                        // Track the actual order being removed
                        incrementalSnapshotManager.trackOrderChange(symbolStr, orderToCancel.get(),
                                IncrementalSnapshotManager.ChangeType.REMOVE);

                        log.debug("Successfully cancelled order {} on symbol {}", orderId.getValue(), symbolStr);
                    }

                    return cancelled;
                });
            });
        } catch (SymbolNotOwnedByThisPodException e) {
            // Symbol không thuộc pod này
            log.warn("Không thể hủy lệnh cho symbol: {} (not owned by this pod)", symbolStr);
            return false;
        } catch (Exception e) {
            log.error("Lỗi không mong đợi khi hủy lệnh cho symbol: {}", symbolStr, e);
            throw new RuntimeException("Lỗi khi hủy lệnh: " + e.getMessage(), e);
        }
    }

    /**
     * FINE-GRAINED LOCKING: Hủy tất cả các lệnh của một thành viên
     * Sử dụng client-level lock thay vì symbol-level lock
     *
     * @param memberId ID thành viên
     * @param symbol   Symbol của lệnh
     * @return Danh sách các lệnh đã hủy
     */
    public List<Order> cancelAllOrders(Long memberId, Symbol symbol) {
        String symbolStr = symbol.getValue();

        // Kiểm tra xem symbol có được gán cho pod này không
        if (!smartShardingManager.canProcessSymbol(symbolStr)) {
            log.warn(LogMessages.OrderMatching.WARN_SYMBOL_NOT_OWNED(), symbolStr);
            throw new SymbolNotOwnedByThisPodException(
                    "Symbol " + symbolStr + " không được gán cho pod này");
        }

        // FINE-GRAINED LOCKING: Use client-level lock instead of symbol-level
        // This allows multiple clients to cancel orders simultaneously
        return fineGrainedLockManager.executeWithClientLock(memberId, symbolStr, () -> {
            // Lấy hoặc tạo matching engine cho symbol
            DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(symbol);
            if (engine == null) {
                log.warn("Không thể tạo matching engine cho symbol: {}", symbolStr);
                return Collections.emptyList();
            }

            // Track incremental changes for shard reassignment
            List<Order> cancelledOrders = engine.cancelAllOrders(memberId);

            // Track changes for incremental snapshot
            for (Order order : cancelledOrders) {
                incrementalSnapshotManager.trackOrderChange(symbolStr, order,
                        IncrementalSnapshotManager.ChangeType.REMOVE);
            }

            log.debug("Cancelled {} orders for client {} on symbol {}",
                    cancelledOrders.size(), memberId, symbolStr);

            return cancelledOrders;
        });
    }

    /**
     * Cập nhật giá đánh dấu với distributed locking
     *
     * @param symbol    Symbol
     * @param markPrice Giá đánh dấu mới
     */
    public void updateMarkPrice(Symbol symbol, Money markPrice) {
        String symbolStr = symbol.getValue();

        // Kiểm tra xem symbol có được gán cho pod này không
        if (!smartShardingManager.canProcessSymbol(symbolStr)) {
            log.warn(LogMessages.OrderMatching.WARN_SYMBOL_NOT_OWNED(), symbolStr);
            return;
        }

        // Tạo khóa cho symbol
        String lockKey = "order_matching:" + symbolStr;
        RLock lock = redissonClient.getLock(lockKey);
        boolean lockAcquired = false;

        try {
            // Sử dụng medium lock cho price updates (100ms wait, 500ms lease)
            lockAcquired = lock.tryLock(100, 500, TimeUnit.MILLISECONDS);
            if (lockAcquired) {
                try {
                    // Lấy hoặc tạo matching engine cho symbol
                    DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(symbol);
                    if (engine == null) {
                        log.warn("Không thể tạo matching engine cho symbol: {}", symbolStr);
                        return;
                    }

                    // Cập nhật giá đánh dấu với matching engine
                    engine.updateMarkPrice(markPrice);
                } finally {
                    // Chỉ mở khóa nếu đã lấy được khóa và thread hiện tại vẫn giữ khóa
                    if (lockAcquired && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                log.error(LogMessages.OrderMatching.ERROR_LOCK_TIMEOUT(), symbolStr);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error(LogMessages.OrderMatching.ERROR_LOCK_INTERRUPTED(), symbolStr, e);
        } catch (Exception e) {
            log.error("Lỗi không mong đợi khi cập nhật giá đánh dấu cho symbol: {}", symbolStr, e);
        }
    }

    /**
     * Cập nhật giá chỉ số với distributed locking
     *
     * @param symbol     Symbol
     * @param indexPrice Giá chỉ số mới
     */
    public void updateIndexPrice(Symbol symbol, Money indexPrice) {
        String symbolStr = symbol.getValue();

        // Kiểm tra xem symbol có được gán cho pod này không
        if (!smartShardingManager.canProcessSymbol(symbolStr)) {
            log.warn(LogMessages.OrderMatching.WARN_SYMBOL_NOT_OWNED(), symbolStr);
            return;
        }

        // Tạo khóa cho symbol
        String lockKey = "order_matching:" + symbolStr;
        RLock lock = redissonClient.getLock(lockKey);
        boolean lockAcquired = false;

        try {
            // Sử dụng medium lock cho index price updates (100ms wait, 500ms lease)
            lockAcquired = lock.tryLock(100, 500, TimeUnit.MILLISECONDS);
            if (lockAcquired) {
                try {
                    // Lấy hoặc tạo matching engine cho symbol
                    DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(symbol);
                    if (engine == null) {
                        log.warn("Không thể tạo matching engine cho symbol: {}", symbolStr);
                        return;
                    }

                    // Cập nhật giá chỉ số với matching engine
                    engine.updateIndexPrice(indexPrice);
                } finally {
                    // Chỉ mở khóa nếu đã lấy được khóa và thread hiện tại vẫn giữ khóa
                    if (lockAcquired && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                log.error(LogMessages.OrderMatching.ERROR_LOCK_TIMEOUT(), symbolStr);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error(LogMessages.OrderMatching.ERROR_LOCK_INTERRUPTED(), symbolStr, e);
        } catch (Exception e) {
            log.error("Lỗi không mong đợi khi cập nhật giá chỉ số cho symbol: {}", symbolStr, e);
        }
    }

    /**
     * Lấy sổ lệnh (read-only operation với symbol validation)
     *
     * @param symbol Symbol
     * @return Sổ lệnh
     */
    public OrderBook getOrderBook(Symbol symbol) {
        String symbolStr = symbol.getValue();

        try {
            // Sử dụng symbol validation cho read-only operation
            return executeWithSymbolValidation(symbolStr, () -> {
                // Lấy hoặc tạo matching engine cho symbol
                DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(symbol);
                if (engine == null) {
                    log.warn("Không thể tạo matching engine cho symbol: {}", symbolStr);
                    return null;
                }

                // Lấy sổ lệnh (lock-free read)
                return engine.getOrderBook();
            });
        } catch (SymbolNotOwnedByThisPodException e) {
            // Symbol không thuộc pod này
            log.warn("Không thể lấy order book cho symbol: {} (not owned by this pod)", symbolStr);
            return null;
        } catch (Exception e) {
            log.error("Lỗi không mong đợi khi lấy sổ lệnh cho symbol: {}", symbolStr, e);
            return null;
        }
    }

    /**
     * Thiết lập trạng thái giao dịch với symbol validation (no locks)
     */
    public void setTradingEnabled(Symbol symbol, boolean enabled) {
        String symbolStr = symbol.getValue();

        try {
            // Sử dụng symbol validation cho configuration change
            executeWithSymbolValidation(symbolStr, () -> {
                // Lấy hoặc tạo matching engine cho symbol
                DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(symbol);
                if (engine == null) {
                    log.warn("Không thể tạo matching engine cho symbol: {}", symbolStr);
                    return null;
                }

                // Bật/tắt giao dịch với matching engine (lock-free)
//                engine.setTradingEnabled(enabled);
                return null;
            });
        } catch (SymbolNotOwnedByThisPodException e) {
            // Symbol không thuộc pod này
            log.warn("Không thể thiết lập trading enabled cho symbol: {} (not owned by this pod)", symbolStr);
        } catch (Exception e) {
            log.error("Lỗi khi thiết lập trạng thái giao dịch cho symbol: {}", symbolStr, e);
        }
    }

    /**
     * Thiết lập thuật toán khớp lệnh với symbol validation (no locks)
     */
    public void setMatchingAlgorithm(Symbol symbol, MatchingAlgorithm algorithm) {
        String symbolStr = symbol.getValue();

        try {
            // Sử dụng symbol validation cho configuration change
            executeWithSymbolValidation(symbolStr, () -> {
                // Lấy hoặc tạo matching engine cho symbol
                DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(symbol);
                if (engine == null) {
                    log.warn("Không thể tạo matching engine cho symbol: {}", symbolStr);
                    return null;
                }

                // Thiết lập thuật toán khớp lệnh với matching engine (lock-free)
                engine.setMatchingAlgorithm(algorithm);
                return null;
            });
        } catch (SymbolNotOwnedByThisPodException e) {
            // Symbol không thuộc pod này
            log.warn("Không thể thiết lập matching algorithm cho symbol: {} (not owned by this pod)", symbolStr);
        } catch (Exception e) {
            log.error("Lỗi khi thiết lập thuật toán khớp lệnh cho symbol: {}", symbolStr, e);
        }
    }

    /**
     * Kiểm tra thanh lý vị thế với distributed locking
     */
    public void checkLiquidation(Position position) {
        String symbolStr = position.getSymbol().getValue();

        // Kiểm tra xem symbol có được gán cho pod này không
        if (!smartShardingManager.canProcessSymbol(symbolStr)) {
            log.warn(LogMessages.OrderMatching.WARN_SYMBOL_NOT_OWNED(), symbolStr);
            return;
        }

        // Tạo khóa cho symbol
        String lockKey = "order_matching:" + symbolStr;
        RLock lock = redissonClient.getLock(lockKey);
        boolean lockAcquired = false;

        try {
            // Sử dụng medium lock cho liquidation checks (200ms wait, 1000ms lease)
            lockAcquired = lock.tryLock(200, 1000, TimeUnit.MILLISECONDS);
            if (lockAcquired) {
                try {
                    // Lấy hoặc tạo matching engine cho symbol
                    DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(position.getSymbol());
                    if (engine == null) {
                        log.warn("Không thể tạo matching engine cho symbol: {}", symbolStr);
                        return;
                    }

                    // Thêm vị thế vào danh sách kiểm tra với matching engine
                    engine.addPositionToCheck(position);
                } finally {
                    // Chỉ mở khóa nếu đã lấy được khóa và thread hiện tại vẫn giữ khóa
                    if (lockAcquired && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                log.error(LogMessages.OrderMatching.ERROR_LOCK_TIMEOUT(), symbolStr);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error(LogMessages.OrderMatching.ERROR_LOCK_INTERRUPTED(), symbolStr, e);
        } catch (Exception e) {
            log.error("Lỗi không mong đợi khi kiểm tra thanh lý cho symbol: {}", symbolStr, e);
        }
    }

    /**
     * Xử lý lệnh thanh lý với symbol validation (no locks)
     */
    public List<Trade> processLiquidationOrder(Order liquidationOrder) {
        String symbolStr = liquidationOrder.getSymbol().getValue();

        try {
            // Sử dụng symbol validation cho liquidation order
            return executeWithSymbolValidation(symbolStr, () -> {
                // Lấy hoặc tạo matching engine cho symbol
                DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(liquidationOrder.getSymbol());
                if (engine == null) {
                    log.warn("Không thể tạo matching engine cho symbol: {}", symbolStr);
                    return Collections.emptyList();
                }

                // Xử lý lệnh thanh lý với matching engine (lock-free)
                return engine.processOrder(liquidationOrder);
            });
        } catch (SymbolNotOwnedByThisPodException e) {
            // Symbol không thuộc pod này
            log.warn("Không thể xử lý liquidation order cho symbol: {} (not owned by this pod)", symbolStr);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Lỗi không mong đợi khi xử lý lệnh thanh lý cho symbol: {}", symbolStr, e);
            return Collections.emptyList();
        }
    }

    // SnapshotProvider interface implementation

    /**
     * Get current order book snapshot for the given symbol
     *
     * @param symbol Symbol to get snapshot for
     * @return Current snapshot or null if not available
     */
    @Override
    public DistributedOrderBookSnapshot getCurrentSnapshot(Symbol symbol) {
        try {
            DistributedLockFreeMatchingEngine engine = getOrCreateMatchingEngineInternal(symbol);
            if (engine != null) {
                return engine.getCurrentSnapshot();
            }
        } catch (Exception e) {
            log.debug("Failed to get snapshot for symbol: {}", symbol.getValue(), e);
        }
        return null;
    }

    /**
     * Check if snapshot provider is available for the given symbol
     *
     * @param symbol Symbol to check
     * @return true if provider can provide snapshot for this symbol
     */
    @Override
    public boolean isAvailable(Symbol symbol) {
        try {
            // Check if this pod owns the symbol
            return smartShardingManager.canProcessSymbol(symbol.getValue());
        } catch (Exception e) {
            log.debug("Failed to check availability for symbol: {}", symbol.getValue(), e);
            return false;
        }
    }

}
