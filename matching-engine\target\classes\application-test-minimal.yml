# Minimal configuration for testing startup issues
server:
  port: 6061

spring:
  application:
    name: matching-engine-test

  main:
    allow-bean-definition-overriding: true

  # Disable auto-configuration that might cause issues
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoAutoConfiguration
      - org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration

# Disable matching engine features for basic startup test
matching-engine:
  exchange:
    enabled: false
  future-core:
    enabled: false
  mongodb:
    enabled: false
  sharding:
    enabled: false

# Minimal logging
logging:
  level:
    root: DEBUG
    com.icetea.lotus.matching: DEBUG
    org.springframework.boot: DEBUG
