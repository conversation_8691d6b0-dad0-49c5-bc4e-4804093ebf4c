package com.icetea.lotus.infrastructure.sharding;

import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.Trade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Integration Service để kết nối tất cả sharding components
 * Cung cấp unified interface cho order processing với intelligent routing
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShardingIntegrationService {

    private final IntelligentOrderRouter orderRouter;
    private final DistributedMatchingEngineManager engineManager;
    private final SmartShardingManager shardingManager;
    private final PartitionBasedLoadBalancer loadBalancer;
    private final PodLoadMonitor podLoadMonitor;
    private final SymbolMetricsCollector metricsCollector;
    
    /**
     * X<PERSON> lý order với intelligent routing và load balancing
     */
    public CompletableFuture<List<Trade>> processOrder(Order order) {
        try {
            log.debug("Processing order {} for symbol {}", order.getOrderId(), order.getSymbol().getValue());

            // Sử dụng intelligent router để xử lý order
            return orderRouter.routeOrder(order);

        } catch (Exception e) {
            log.error("Error processing order {}: {}", order.getOrderId(), e.getMessage(), e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * Hủy order với intelligent routing và load balancing
     */
    public CompletableFuture<Boolean> cancelOrder(Order order) {
        try {
            log.debug("Canceling order {} for symbol {}", order.getOrderId(), order.getSymbol().getValue());

            // Sử dụng intelligent router để hủy order
            return orderRouter.routeCancelOrder(order);

        } catch (Exception e) {
            log.error("Error canceling order {}: {}", order.getOrderId(), e.getMessage(), e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    /**
     * Xử lý order trên partition cụ thể (được gọi từ router)
     */
    public List<Trade> processOrderOnPartition(Order order, String partitionKey) {
        return engineManager.processOrderOnPartition(order, partitionKey);
    }
    
    /**
     * Kiểm tra xem symbol có thể được xử lý bởi pod hiện tại không
     */
    public boolean canProcessSymbol(String symbol) {
        return shardingManager.canProcessSymbol(symbol);
    }

    /**
     * Lấy partition key cho order
     */
    public String getPartitionKey(Order order) {
        return loadBalancer.getPartitionKey(order);
    }
    
    /**
     * Lấy danh sách tất cả partitions cho symbol
     */
    public List<String> getAllPartitions(String symbol) {
        return loadBalancer.getAllPartitions(symbol);
    }
    
    /**
     * Trigger rebalancing cho symbol
     */
    public void rebalanceSymbol(String symbol) {
        try {
            log.info("Triggering rebalancing for symbol: {}", symbol);
            
            // Rebalance partitions
            engineManager.rebalancePartitions(symbol);
            
            // Update metrics
            metricsCollector.updateMetrics(symbol);
            
            log.info("Rebalancing completed for symbol: {}", symbol);
            
        } catch (Exception e) {
            log.error("Error rebalancing symbol {}: {}", symbol, e.getMessage(), e);
        }
    }
    
    /**
     * Lấy load info của pod hiện tại
     */
    public PodLoadInfo getCurrentPodLoadInfo() {
        String currentPod = getCurrentPodName();
        return podLoadMonitor.getPodLoadInfo(currentPod);
    }
    
    /**
     * Lấy metrics của symbol
     */
    public SymbolMetrics getSymbolMetrics(String symbol) {
        return metricsCollector.getMetrics(symbol);
    }
    
    /**
     * Lấy partition config của symbol
     */
    public PartitionConfig getPartitionConfig(String symbol) {
        return loadBalancer.getPartitionConfig(symbol);
    }
    
    /**
     * Kiểm tra health của sharding system
     */
    public ShardingHealthStatus getHealthStatus() {
        try {
            // Kiểm tra các components
            boolean routerHealthy = checkRouterHealth();
            boolean engineManagerHealthy = checkEngineManagerHealth();
            boolean loadBalancerHealthy = checkLoadBalancerHealth();
            boolean podMonitorHealthy = checkPodMonitorHealth();
            
            boolean overallHealthy = routerHealthy && engineManagerHealthy && 
                                   loadBalancerHealthy && podMonitorHealthy;
            
            return ShardingHealthStatus.builder()
                    .healthy(overallHealthy)
                    .routerHealthy(routerHealthy)
                    .engineManagerHealthy(engineManagerHealthy)
                    .loadBalancerHealthy(loadBalancerHealthy)
                    .podMonitorHealthy(podMonitorHealthy)
                    .timestamp(System.currentTimeMillis())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error checking health status: {}", e.getMessage(), e);
            return ShardingHealthStatus.builder()
                    .healthy(false)
                    .errorMessage(e.getMessage())
                    .timestamp(System.currentTimeMillis())
                    .build();
        }
    }
    
    /**
     * Lấy statistics của sharding system
     */
    public ShardingStatistics getStatistics() {
        try {
            List<String> availablePods = podLoadMonitor.getAvailablePods();
            List<String> highLoadPods = podLoadMonitor.getHighLoadPods();
            List<String> lowLoadPods = podLoadMonitor.getLowLoadPods();
            
            return ShardingStatistics.builder()
                    .totalPods(availablePods.size())
                    .highLoadPods(highLoadPods.size())
                    .lowLoadPods(lowLoadPods.size())
                    .averageLoad(calculateAverageLoad(availablePods))
                    .totalPartitions(getTotalPartitions())
                    .activeSymbols(getActiveSymbolCount())
                    .timestamp(System.currentTimeMillis())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error getting statistics: {}", e.getMessage(), e);
            return ShardingStatistics.builder()
                    .timestamp(System.currentTimeMillis())
                    .build();
        }
    }
    
    /**
     * Initialize sharding system khi application ready
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializeShardingSystem() {
        log.info("Initializing sharding system...");
        
        try {
            // Update current pod load
            podLoadMonitor.updateCurrentPodLoad();
            
            // Initialize metrics collection
            metricsCollector.updateMetrics();
            
            log.info("Sharding system initialized successfully");
            
        } catch (Exception e) {
            log.error("Error initializing sharding system: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Cleanup khi shutdown
     */
    @PreDestroy
    public void shutdown() {
        log.info("Shutting down sharding system...");
        
        try {
            // Shutdown engine manager
            engineManager.shutdown();
            
            log.info("Sharding system shutdown completed");
            
        } catch (Exception e) {
            log.error("Error during sharding system shutdown: {}", e.getMessage(), e);
        }
    }
    
    // Helper methods
    private boolean checkRouterHealth() {
        // Kiểm tra router có hoạt động không
        return orderRouter != null;
    }
    
    private boolean checkEngineManagerHealth() {
        // Kiểm tra engine manager có hoạt động không
        return engineManager != null;
    }
    
    private boolean checkLoadBalancerHealth() {
        // Kiểm tra load balancer có hoạt động không
        return loadBalancer != null;
    }
    
    private boolean checkPodMonitorHealth() {
        // Kiểm tra pod monitor có hoạt động không
        List<String> pods = podLoadMonitor.getAvailablePods();
        return pods != null && !pods.isEmpty();
    }
    
    private double calculateAverageLoad(List<String> pods) {
        if (pods.isEmpty()) return 0.0;
        
        double totalLoad = pods.stream()
                .mapToDouble(pod -> {
                    PodLoadInfo info = podLoadMonitor.getPodLoadInfo(pod);
                    return info != null ? info.getOverallLoad() : 0.0;
                })
                .sum();
                
        return totalLoad / pods.size();
    }
    
    private int getTotalPartitions() {
        try {
            return engineManager.getActivePartitions().size();
        } catch (Exception e) {
            log.error("Error getting total partitions: {}", e.getMessage());
            return 0;
        }
    }

    private int getActiveSymbolCount() {
        try {
            // Lấy từ Redis hoặc tính từ partition keys
            Set<String> symbols = engineManager.getActivePartitions().stream()
                    .map(partition -> partition.split("-")[0]) // Extract symbol from partition key
                    .collect(java.util.stream.Collectors.toSet());
            return symbols.size();
        } catch (Exception e) {
            log.error("Error getting active symbol count: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * Lấy partition engine cho symbol và partition key
     */
    public boolean hasPartitionEngine(String partitionKey) {
        return engineManager.hasPartition(partitionKey);
    }

    /**
     * Lấy order book snapshot cho partition
     */
    public String getOrderBookSnapshot(String partitionKey) {
        return engineManager.getOrderBookSnapshot(partitionKey);
    }

    /**
     * Khôi phục partition từ snapshot
     */
    public boolean restorePartitionFromSnapshot(String partitionKey, String snapshot) {
        return engineManager.restorePartitionFromSnapshot(partitionKey, snapshot);
    }

    /**
     * Lấy danh sách active partitions
     */
    public Set<String> getActivePartitions() {
        return engineManager.getActivePartitions();
    }

    /**
     * Remove partition engine
     */
    public void removePartitionEngine(String partitionKey) {
        engineManager.removePartitionEngine(partitionKey);
    }
    
    private String getCurrentPodName() {
        return System.getProperty("pod.name", "default-pod");
    }
}
