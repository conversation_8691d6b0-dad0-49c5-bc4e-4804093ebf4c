package com.icetea.lotus.matching.infrastructure.messaging.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreTradeResult;
import com.icetea.lotus.matching.infrastructure.messaging.producer.FutureCoreKafkaProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Future-Core Order Consumer - Copy từ Future-Core module
 * Processes futures trading orders and events from Kafka
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FutureCoreOrderConsumer {
    
    private final FutureCoreCompatibilityService futureCoreService;
    private final FutureCoreKafkaProducer futureCoreKafkaProducer;
    private final ObjectMapper objectMapper;
    
    // Executor for async processing
    private final ExecutorService processingExecutor = Executors.newFixedThreadPool(20);
    
    // ==================== CONTRACT ORDER NEW TOPIC - REMOVED ====================
    // Topic contract-order-new đã được loại bỏ theo yêu cầu
    // Matching engine sẽ không xử lý topic này nữa
    
    /**
     * Process contract order cancellations
     * FIXED: Proper acknowledgment after processing completion
     */
    @KafkaListener(
            topics = "${topic-kafka.contract.order-cancel:contract-order-cancel}",
            containerFactory = "futuresKafkaListenerContainerFactory",
            groupId = "matching-engine-contract-cancels")
    public void handleContractOrderCancel(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        log.debug("Received {} contract order cancel records", records.size());

        try {
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (ConsumerRecord<String, String> record : records) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                    () -> processContractOrderCancel(record), processingExecutor);
                futures.add(future);
            }

            // FIXED: Wait for all processing to complete before acknowledging
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(30, TimeUnit.SECONDS); // 30 second timeout to prevent hanging

            // Only acknowledge after ALL processing is complete
            ack.acknowledge();
            log.debug("Successfully processed and acknowledged {} contract order cancel records", records.size());

        } catch (TimeoutException e) {
            log.error("Timeout waiting for contract order cancel processing to complete", e);
            // Don't acknowledge on timeout - let Kafka retry
        } catch (InterruptedException e) {
            log.error("Interrupted while waiting for contract order cancel processing", e);
            Thread.currentThread().interrupt(); // Restore interrupted status
            // Don't acknowledge on interruption - let Kafka retry
        } catch (Exception e) {
            log.error("Error processing contract order cancel batch", e);
            // Don't acknowledge on error - let Kafka retry
        }
    }

    /**
     * Process individual contract order cancel
     */
    private void processContractOrderCancel(ConsumerRecord<String, String> record) {
        String symbol = record.key();
        String value = record.value();

        log.info("Processing contract order cancel for symbol: {}", symbol);

        try {
            // Parse cancel request from JSON
            Object cancelRequest = parseCancelRequestFromJson(value);

            // Process through Future-Core compatibility service (internal method)
            FutureCoreTradeResult result = futureCoreService.processContractOrderCancelInternal(cancelRequest);

            // Publish results
            if (result.isSuccess()) {
                // Publish cancel success message (consistent with spot pattern)
                futureCoreKafkaProducer.publishContractOrderCancelSuccess(symbol, result.getCancelResult());

                // Publish trade plate update if needed
                if (result.getTradePlate() != null) {
                    futureCoreKafkaProducer.publishContractTradePlate(symbol, result.getTradePlate());
                }

                log.info("Successfully processed contract order cancel for symbol: {}", symbol);
            } else {
                log.warn("Failed to process contract order cancel for symbol: {} - {}",
                        symbol, result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Error processing contract order cancel for symbol: {}", symbol, e);
        }
    }
    
    // ==================== CONTRACT TRADE TOPIC - REMOVED ====================
    // Topic contract-trade là OUTPUT của matching engine, không nên consume
    // Matching engine tạo ra trades, không consume trades từ chính nó
    
    /**
     * Handle funding rate updates - Copy từ OrderConsumer.handleFundingRate()
     */
    @KafkaListener(
            topics = "${topic-kafka.contract.funding-rate:contract-funding-rate}",
            containerFactory = "futuresKafkaListenerContainerFactory",
            groupId = "matching-engine-funding-rate")
    public void handleFundingRate(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        log.debug("Received {} funding rate update records", records.size());

        try {
            for (ConsumerRecord<String, String> record : records) {
                processingExecutor.submit(() -> processFundingRate(record));
            }

            // Manual acknowledgment sau khi submit tất cả tasks
            ack.acknowledge();

        } catch (Exception e) {
            log.error("Error processing funding rate batch", e);
            // Không acknowledge nếu có lỗi
        }
    }

    /**
     * Process individual funding rate update
     */
    private void processFundingRate(ConsumerRecord<String, String> record) {
        log.info("Processing funding rate update for topic: {}, key: {}", record.topic(), record.key());

        try {
            String symbol = record.key();
            String value = record.value();

            JsonNode json = objectMapper.readTree(value);
            BigDecimal rate = new BigDecimal(json.get("rate").asText());

            // Update funding rate through Future-Core service
            futureCoreService.updateFundingRate(symbol, rate);

            log.debug("Successfully updated funding rate for symbol: {} to {}", symbol, rate);

        } catch (Exception e) {
            log.error("Error processing funding rate update", e);
        }
    }
    
    // ==================== CONTRACT ORDER EVENTS TOPIC - REMOVED ====================
    // Topic contract-order-events là OUTPUT của matching engine, không nên consume
    // Matching engine publish order events, không consume events từ chính nó
    
    // parseContractOrderFromJson method đã được loại bỏ vì không cần thiết
    
    /**
     * Parse cancel request from JSON
     */
    private Object parseCancelRequestFromJson(String cancelJson) {
        try {
            return objectMapper.readValue(cancelJson, Object.class);
        } catch (Exception e) {
            log.error("Failed to parse cancel request from JSON: {}", cancelJson, e);
            throw new RuntimeException("Invalid cancel request JSON", e);
        }
    }
    
    // parseTradeFromJson và parseOrderEventFromJson methods đã được loại bỏ
    // vì không cần thiết sau khi loại bỏ OUTPUT topic consumers
    
    /**
     * Async processing with error handling
     */
    private CompletableFuture<Void> processAsync(Runnable task) {
        return CompletableFuture.runAsync(task, processingExecutor)
                .exceptionally(throwable -> {
                    log.error("Async processing failed", throwable);
                    return null;
                });
    }
    
    /**
     * Shutdown executor
     */
    public void shutdown() {
        processingExecutor.shutdown();
    }
}
