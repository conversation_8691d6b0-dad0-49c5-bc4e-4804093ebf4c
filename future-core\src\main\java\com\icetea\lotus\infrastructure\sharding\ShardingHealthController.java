package com.icetea.lotus.infrastructure.sharding;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * REST Controller cho sharding health check và management
 */
@Slf4j
@RestController
@RequestMapping("/actuator/sharding")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "sharding.intelligent.enabled", havingValue = "true", matchIfMissing = true)
public class ShardingHealthController {

    private final ShardingIntegrationService shardingService;
    private final IntelligentOrderRouter orderRouter;
    private final PartitionTransferHandler transferHandler;
    private final SmartShardingManager shardingManager;
    
    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<ShardingHealthStatus> getHealth() {
        try {
            ShardingHealthStatus status = shardingService.getHealthStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("Error getting sharding health: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ShardingHealthStatus.builder()
                            .healthy(false)
                            .errorMessage(e.getMessage())
                            .timestamp(System.currentTimeMillis())
                            .build());
        }
    }
    
    /**
     * Statistics endpoint
     */
    @GetMapping("/stats")
    public ResponseEntity<ShardingStatistics> getStatistics() {
        try {
            ShardingStatistics stats = shardingService.getStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Error getting sharding statistics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Routing statistics endpoint
     */
    @GetMapping("/routing/stats")
    public ResponseEntity<OrderRoutingStatistics> getRoutingStatistics() {
        try {
            OrderRoutingStatistics stats = orderRouter.getRoutingStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Error getting routing statistics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Active partitions endpoint
     */
    @GetMapping("/partitions")
    public ResponseEntity<Set<String>> getActivePartitions() {
        try {
            Set<String> partitions = shardingService.getActivePartitions();
            return ResponseEntity.ok(partitions);
        } catch (Exception e) {
            log.error("Error getting active partitions: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Partition config endpoint
     */
    @GetMapping("/partitions/{symbol}/config")
    public ResponseEntity<PartitionConfig> getPartitionConfig(@PathVariable String symbol) {
        try {
            PartitionConfig config = shardingService.getPartitionConfig(symbol);
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            log.error("Error getting partition config for {}: {}", symbol, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Symbol metrics endpoint
     */
    @GetMapping("/symbols/{symbol}/metrics")
    public ResponseEntity<SymbolMetrics> getSymbolMetrics(@PathVariable String symbol) {
        try {
            SymbolMetrics metrics = shardingService.getSymbolMetrics(symbol);
            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            log.error("Error getting symbol metrics for {}: {}", symbol, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Pod load info endpoint
     */
    @GetMapping("/pod/load")
    public ResponseEntity<PodLoadInfo> getCurrentPodLoad() {
        try {
            PodLoadInfo loadInfo = shardingService.getCurrentPodLoadInfo();
            return ResponseEntity.ok(loadInfo);
        } catch (Exception e) {
            log.error("Error getting current pod load: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Pending transfers endpoint
     */
    @GetMapping("/transfers/pending")
    public ResponseEntity<List<String>> getPendingTransfers() {
        try {
            List<String> transfers = transferHandler.getPendingTransfers();
            return ResponseEntity.ok(transfers);
        } catch (Exception e) {
            log.error("Error getting pending transfers: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Pending cleanups endpoint
     */
    @GetMapping("/cleanups/pending")
    public ResponseEntity<List<String>> getPendingCleanups() {
        try {
            List<String> cleanups = transferHandler.getPendingCleanups();
            return ResponseEntity.ok(cleanups);
        } catch (Exception e) {
            log.error("Error getting pending cleanups: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Trigger rebalancing endpoint
     */
    @PostMapping("/symbols/{symbol}/rebalance")
    public ResponseEntity<String> triggerRebalancing(@PathVariable String symbol) {
        try {
            shardingService.rebalanceSymbol(symbol);
            return ResponseEntity.ok("Rebalancing triggered for symbol: " + symbol);
        } catch (Exception e) {
            log.error("Error triggering rebalancing for {}: {}", symbol, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error triggering rebalancing: " + e.getMessage());
        }
    }
    
    /**
     * Force partition transfer endpoint
     */
    @PostMapping("/partitions/{partitionKey}/transfer")
    public ResponseEntity<String> forcePartitionTransfer(@PathVariable String partitionKey) {
        try {
            boolean success = transferHandler.forceProcessTransfer(partitionKey);
            if (success) {
                return ResponseEntity.ok("Partition transfer completed: " + partitionKey);
            } else {
                return ResponseEntity.badRequest()
                        .body("Partition transfer failed: " + partitionKey);
            }
        } catch (Exception e) {
            log.error("Error forcing partition transfer for {}: {}", partitionKey, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error forcing transfer: " + e.getMessage());
        }
    }
    
    /**
     * Force partition cleanup endpoint
     */
    @PostMapping("/partitions/{partitionKey}/cleanup")
    public ResponseEntity<String> forcePartitionCleanup(@PathVariable String partitionKey) {
        try {
            boolean success = transferHandler.forceCleanup(partitionKey);
            if (success) {
                return ResponseEntity.ok("Partition cleanup completed: " + partitionKey);
            } else {
                return ResponseEntity.badRequest()
                        .body("Partition cleanup failed: " + partitionKey);
            }
        } catch (Exception e) {
            log.error("Error forcing partition cleanup for {}: {}", partitionKey, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error forcing cleanup: " + e.getMessage());
        }
    }
    
    /**
     * Assign symbol to current pod endpoint
     */
    @PostMapping("/symbols/{symbol}/assign")
    public ResponseEntity<String> assignSymbolToCurrentPod(@PathVariable String symbol) {
        try {
            boolean success = shardingManager.canProcessSymbol(symbol);
            if (success) {
                return ResponseEntity.ok("Symbol assigned to current pod: " + symbol);
            } else {
                return ResponseEntity.badRequest()
                        .body("Failed to assign symbol to current pod: " + symbol);
            }
        } catch (Exception e) {
            log.error("Error assigning symbol {} to current pod: {}", symbol, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error assigning symbol: " + e.getMessage());
        }
    }
    
    /**
     * Check if symbol can be processed endpoint
     */
    @GetMapping("/symbols/{symbol}/can-process")
    public ResponseEntity<Boolean> canProcessSymbol(@PathVariable String symbol) {
        try {
            boolean canProcess = shardingService.canProcessSymbol(symbol);
            return ResponseEntity.ok(canProcess);
        } catch (Exception e) {
            log.error("Error checking if symbol {} can be processed: {}", symbol, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
