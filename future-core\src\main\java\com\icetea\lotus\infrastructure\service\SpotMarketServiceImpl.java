package com.icetea.lotus.infrastructure.service;

import com.icetea.lotus.core.domain.service.SpotMarketService;
import com.icetea.lotus.core.domain.service.impl.DynamicPricingManager;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.websocket.WebSocketSpotMarketClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Implementation của SpotMarketService
 * Kết nối với module market để lấy giá spot
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotMarketServiceImpl implements SpotMarketService {

    private final RestTemplate restTemplate;
    private final WebSocketSpotMarketClient webSocketSpotMarketClient;
    private final DynamicPricingManager dynamicPricingManager;

    @Value("${market.websocket.url}")
    private String marketServiceUrl;

    // Cache giá spot
    private final Map<String, CacheEntry> spotPriceCache = new ConcurrentHashMap<>();

    // Thời gian hết hạn cache (5 giây)
    private static final long CACHE_EXPIRY_MS = 5000;

    /**
     * Lấy giá mua tốt nhất
     * @param symbol Symbol của cặp tiền
     * @return Money
     */
    @Override
    public Money getBestBidPrice(Symbol symbol) {
        try {
            log.debug("Lấy giá mua tốt nhất, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return Money.ZERO;
            }

            String spotSymbol = convertToSpotSymbol(symbol.getValue());
            String url = "http://" + marketServiceUrl + "/market/ticker?symbol=" + spotSymbol;

            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> data = response.getBody();

            if (data != null && data.containsKey("bid")) {
                BigDecimal bidPrice = new BigDecimal(data.get("bid").toString());
                return Money.of(bidPrice);
            }

            return Money.ZERO;
        } catch (Exception e) {
            log.error("Lỗi khi lấy giá mua tốt nhất, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            return Money.ZERO;
        }
    }

    /**
     * Lấy giá bán tốt nhất
     * @param symbol Symbol của cặp tiền
     * @return Money
     */
    @Override
    public Money getBestAskPrice(Symbol symbol) {
        try {
            log.debug("Lấy giá bán tốt nhất, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return Money.ZERO;
            }

            String spotSymbol = convertToSpotSymbol(symbol.getValue());
            String url = marketServiceUrl + "/market/ticker?symbol=" + spotSymbol;

            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> data = response.getBody();

            if (data != null && data.containsKey("ask")) {
                BigDecimal askPrice = new BigDecimal(data.get("ask").toString());
                return Money.of(askPrice);
            }

            return Money.ZERO;
        } catch (Exception e) {
            log.error("Lỗi khi lấy giá bán tốt nhất, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            return Money.ZERO;
        }
    }

    /**
     * Lấy giá trung bình
     * @param symbol Symbol của cặp tiền
     * @return Money
     */
    @Override
    public Money getMidPrice(Symbol symbol) {
        try {
            log.debug("Lấy giá trung bình, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return Money.ZERO;
            }

            Money bidPrice = getBestBidPrice(symbol);
            Money askPrice = getBestAskPrice(symbol);

            if (bidPrice.isZero() || askPrice.isZero()) {
                return getLastPrice(symbol);
            }

            BigDecimal midPrice = bidPrice.getValue().add(askPrice.getValue())
                    .divide(BigDecimal.valueOf(2), 8, RoundingMode.HALF_UP);

            return Money.of(midPrice);
        } catch (Exception e) {
            log.error("Lỗi khi lấy giá trung bình, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            return Money.ZERO;
        }
    }

    /**
     * Lấy giá giao dịch cuối cùng
     * @param symbol Symbol của cặp tiền
     * @return Money
     */
    @Override
    public Money getLastPrice(Symbol symbol) {
        try {
            log.debug("Lấy giá giao dịch cuối cùng, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return Money.ZERO;
            }

            // Lấy giá thị trường
            String spotSymbol = convertToSpotSymbol(symbol.getValue());
            String url = marketServiceUrl + "/market/ticker?symbol=" + spotSymbol;

            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> data = response.getBody();

            log.info("Response from /market/ticker?symbol= = {}", data);

            if (data != null && data.containsKey("close")) {
                BigDecimal lastPrice = new BigDecimal(data.get("close").toString());
                return Money.of(lastPrice);
            }

            return Money.ZERO;
        } catch (Exception e) {
            log.error("Lỗi khi lấy giá giao dịch cuối cùng, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            return Money.ZERO;
        }
    }

    /**
     * Lấy khối lượng giao dịch 24h
     * @param symbol Symbol của cặp tiền
     * @return BigDecimal
     */
    @Override
    public BigDecimal get24hVolume(Symbol symbol) {
        try {
            log.debug("Lấy khối lượng giao dịch 24h, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return BigDecimal.ZERO;
            }

            String spotSymbol = convertToSpotSymbol(symbol.getValue());
            String url = marketServiceUrl + "/market/ticker?symbol=" + spotSymbol;

            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> data = response.getBody();

            if (data != null && data.containsKey("volume")) {
                return new BigDecimal(data.get("volume").toString());
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Lỗi khi lấy khối lượng giao dịch 24h, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * Lấy giá cao nhất 24h
     * @param symbol Symbol của cặp tiền
     * @return Money
     */
    @Override
    public Money get24hHigh(Symbol symbol) {
        try {
            log.debug("Lấy giá cao nhất 24h, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return Money.ZERO;
            }

            String spotSymbol = convertToSpotSymbol(symbol.getValue());
            String url = marketServiceUrl + "/market/ticker?symbol=" + spotSymbol;

            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> data = response.getBody();

            if (data != null && data.containsKey("high")) {
                BigDecimal highPrice = new BigDecimal(data.get("high").toString());
                return Money.of(highPrice);
            }

            return Money.ZERO;
        } catch (Exception e) {
            log.error("Lỗi khi lấy giá cao nhất 24h, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            return Money.ZERO;
        }
    }

    /**
     * Lấy giá thấp nhất 24h
     * @param symbol Symbol của cặp tiền
     * @return Money
     */
    @Override
    public Money get24hLow(Symbol symbol) {
        try {
            log.debug("Lấy giá thấp nhất 24h, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return Money.ZERO;
            }

            String spotSymbol = convertToSpotSymbol(symbol.getValue());
            String url = marketServiceUrl + "/market/ticker?symbol=" + spotSymbol;

            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> data = response.getBody();

            if (data != null && data.containsKey("low")) {
                BigDecimal lowPrice = new BigDecimal(data.get("low").toString());
                return Money.of(lowPrice);
            }

            return Money.ZERO;
        } catch (Exception e) {
            log.error("Lỗi khi lấy giá thấp nhất 24h, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            return Money.ZERO;
        }
    }

    /**
     * Lấy tỷ lệ thay đổi 24h
     * @param symbol Symbol của cặp tiền
     * @return BigDecimal
     */
    @Override
    public BigDecimal get24hChangePercent(Symbol symbol) {
        try {
            log.debug("Lấy tỷ lệ thay đổi 24h, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return BigDecimal.ZERO;
            }

            String spotSymbol = convertToSpotSymbol(symbol.getValue());
            String url = marketServiceUrl + "/market/ticker?symbol=" + spotSymbol;

            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> data = response.getBody();

            if (data != null && data.containsKey("chg")) {
                return new BigDecimal(data.get("chg").toString());
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Lỗi khi lấy tỷ lệ thay đổi 24h, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * Lấy giá spot hiện tại
     * @param symbol Symbol của cặp tiền
     * @return Money
     */
    @Override
    public Money getSpotPrice(Symbol symbol) {
        try {
            log.debug("Lấy giá spot hiện tại, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return Money.ZERO;
            }

            // Kiểm tra cache
            String symbolStr = symbol.getValue();
            CacheEntry cacheEntry = spotPriceCache.get(symbolStr);
            if (cacheEntry != null && !cacheEntry.isExpired()) {
                log.debug("Lấy giá spot từ cache, symbol = {}, price = {}", symbolStr, cacheEntry.getPrice());
                return Money.of(cacheEntry.getPrice());
            }

            // Thử lấy giá từ K-line WebSocket trước
            com.icetea.lotus.application.dto.KLineDto kLineDto = webSocketSpotMarketClient.getLatestKLine(symbol);
            if (kLineDto != null && kLineDto.getClosePrice() != null) {
                BigDecimal closePrice = kLineDto.getClosePrice();
                Money wsPrice = Money.of(closePrice);

                log.debug("Lấy giá spot từ K-line WebSocket, symbol = {}, price = {}", symbolStr, closePrice);

                // Cập nhật cache
                spotPriceCache.put(symbolStr, new CacheEntry(closePrice, System.currentTimeMillis()));

                // Cập nhật trạng thái biến động
                DynamicPricingManager.VolatilityState volatilityState = dynamicPricingManager.updateSpotPrice(symbol, wsPrice);
                if (volatilityState != DynamicPricingManager.VolatilityState.NORMAL) {
//                    log.info("Phát hiện biến động giá spot từ K-line: symbol = {}, price = {}, state = {}",
//                            symbolStr, closePrice, volatilityState);
                }

                return wsPrice;
            }

            // Nếu không có giá từ WebSocket, lấy giá từ REST API
            log.debug("Không có giá từ WebSocket, lấy giá từ REST API, symbol = {}", symbolStr);
            Money price = getLastPrice(symbol);

            // Cập nhật cache
            spotPriceCache.put(symbolStr, new CacheEntry(price.getValue(), System.currentTimeMillis()));

            // Cập nhật trạng thái biến động
            DynamicPricingManager.VolatilityState volatilityState = dynamicPricingManager.updateSpotPrice(symbol, price);
            if (volatilityState != DynamicPricingManager.VolatilityState.NORMAL) {
                log.info("Phát hiện biến động giá spot từ REST API: symbol = {}, price = {}, state = {}",
                        symbolStr, price.getValue(), volatilityState);
            }

            return price;
        } catch (Exception e) {
            log.error("Lỗi khi lấy giá spot hiện tại, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            return Money.ZERO;
        }
    }

    /**
     * Chuyển đổi symbol từ hợp đồng tương lai sang spot
     * Ví dụ: BTC-USDT-PERP -> BTC/USDT
     * @param symbol Symbol của hợp đồng tương lai
     * @return Symbol của spot
     */
    private String convertToSpotSymbol(String symbol) {
        if (symbol == null || symbol.isEmpty()) {
            return "";
        }

        // Xử lý các trường hợp đặc biệt
        if (symbol.endsWith("-PERP")) {
            String[] parts = symbol.split("-");
            if (parts.length >= 2) {
                return parts[0] + "/" + parts[1];
            }
        }

        // Trường hợp mặc định
        return symbol.replace("-", "/");
    }

    /**
     * Class lưu trữ thông tin cache
     */
    private static class CacheEntry {
        private final BigDecimal price;
        private final long timestamp;

        public CacheEntry(BigDecimal price, long timestamp) {
            this.price = price;
            this.timestamp = timestamp;
        }

        public BigDecimal getPrice() {
            return price;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_EXPIRY_MS;
        }
    }
}
