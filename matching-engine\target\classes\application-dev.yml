# ===================================================================
# MATCHING ENGINE DEVELOPMENT CONFIGURATION
# Copy từ Future-Core application-dev.yml
# ===================================================================

server:
  port: 6061

# Cấu hình Spring cho Development
spring:
  # PostgreSQL removed - matching engine only needs Redis, MongoDB, Kafka
  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: ${server.servlet.context-path}/actuator/health
        health-check-interval: 10s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.uuid}
        register-health-check: ${CONSUL_REGISTER_HEALTH_CHECK:true}
        # Add these properties to fix IP registration
        hostname: ${POD_IP:${spring.cloud.client.ip-address:localhost}}
        ip-address: ${POD_IP:${spring.cloud.client.ip-address:127.0.0.1}}
        prefer-agent-address: false

  # Cấu hình Redis cho Development
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:30679}
      database: ${REDIS_DATABASE:1}  # Different DB for dev
      timeout: ${REDIS_TIMEOUT:5000}
      lettuce:
        pool:
          max-active: ${REDIS_POOL_MAX_ACTIVE:10}  # Smaller pool for dev
          max-idle: ${REDIS_POOL_MAX_IDLE:5}
          min-idle: ${REDIS_POOL_MIN_IDLE:2}
          max-wait: ${REDIS_POOL_MAX_WAIT:3000}

    # MongoDB Configuration for Development
    mongodb:
      uri: ${SPRING_MONGODB_URI:********************************************************************************}
      database: ${MONGODB_DATABASE:matching_engine_dev}
      # Development Connection Pool - Smaller
      connection-pool:
        max-size: ${MONGODB_POOL_MAX_SIZE:10}
        min-size: ${MONGODB_POOL_MIN_SIZE:2}
        max-wait-time: ${MONGODB_POOL_MAX_WAIT:3000}
        max-connection-idle-time: ${MONGODB_POOL_MAX_IDLE:15000}
        max-connection-life-time: ${MONGODB_POOL_MAX_LIFE:30000}
      # Development Socket Configuration
      socket:
        connect-timeout: ${MONGODB_SOCKET_CONNECT_TIMEOUT:3000}
        read-timeout: ${MONGODB_SOCKET_READ_TIMEOUT:5000}
      # Development Server Selection
      server-selection-timeout: ${MONGODB_SERVER_SELECTION_TIMEOUT:3000}
      # Development Write Concern - Relaxed
      write-concern:
        w: ${MONGODB_WRITE_CONCERN_W:1}  # Single node for dev
        j: ${MONGODB_WRITE_CONCERN_J:false}  # No journal for dev
        w-timeout: ${MONGODB_WRITE_CONCERN_TIMEOUT:3000}
      # Development Read Preference
      read-preference: ${MONGODB_READ_PREFERENCE:primary}
      # Development Retry Configuration
      retry-writes: ${MONGODB_RETRY_WRITES:false}  # Disabled for dev
      retry-reads: ${MONGODB_RETRY_READS:false}   # Disabled for dev

  # Cấu hình Kafka cho Development
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:30092}
    listener:
      # Cấu hình đồng thời cho môi trường dev với một instance
      concurrency: 1  # Single thread for dev
      ack-mode: manual
    producer:
      retries: 1  # Less retries for dev
      batch-size: 16  # Smaller batch for dev
      linger: 1
      buffer-memory: 524288  # Smaller buffer for dev
      compression-type: gzip
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:matching-engine-dev-group}
      enable-auto-commit: true  # Simpler for dev
      session-timeout: 15000

# Matching Engine Development Configuration
matching-engine:
  # Auto-initialize default symbols
  auto-init-symbols: ${MATCHING_ENGINE_AUTO_INIT_SYMBOLS:true}
  default-symbols: ${MATCHING_ENGINE_DEFAULT_SYMBOLS:BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,XRP/USDT,SOL/USDT,DOT/USDT,DOGE/USDT,AVAX/USDT,MATIC/USDT,LINK/USDT,UNI/USDT,LTC/USDT,EOS/ETH,BZB/USDT,HT/USDT}

  # Pod-specific symbols for development
  # Example configurations for different pods:
  # pod-symbols: BTC/USDT,ETH/USDT  # For pod-1
  # pod-symbols: SOL/USDT,DOT/USDT  # For pod-2
  pod-symbols: ${MATCHING_ENGINE_POD_SYMBOLS:BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,XRP/USDT,SOL/USDT,DOT/USDT,DOGE/USDT,AVAX/USDT,MATIC/USDT,LINK/USDT,UNI/USDT,LTC/USDT,EOS/ETH,BZB/USDT,HT/USDT}
  auto-init-pod-symbols: ${MATCHING_ENGINE_AUTO_INIT_POD_SYMBOLS:true}

  # Exchange Configuration for Dev
  exchange:
    enabled: true
    default-algorithm: FIFO
    trade-plate-publishing: true
  
  # Future-Core Configuration for Dev
  future-core:
    enabled: true
    default-algorithm: FIFO
    cas-retry-limit: 3  # Less retries for dev
    performance-monitoring: false  # Disabled for dev
  
  # Performance Configuration for Dev
  performance:
    metrics-collection: false  # Disabled for dev
    health-monitoring: true
    throughput-target: 1000   # Lower target for dev
    latency-target: 10        # More relaxed for dev
  
  # Order Book Configuration for Dev
  order-book:
    segment-size: 100    # Smaller for dev
    cache-validity-ms: 1000  # Shorter for dev
    max-depth: 20        # Smaller for dev

  # MongoDB Development Configuration
  mongodb:
    enabled: ${MATCHING_ENGINE_MONGODB_ENABLED:true}
    # Development Snapshot Settings
    snapshot:
      enabled: ${MATCHING_ENGINE_SNAPSHOT_ENABLED:true}
      auto-save: ${MATCHING_ENGINE_SNAPSHOT_AUTO_SAVE:false}  # Manual for dev
      save-interval-seconds: ${MATCHING_ENGINE_SNAPSHOT_SAVE_INTERVAL:600}  # 10 minutes for dev
      max-versions-per-symbol: ${MATCHING_ENGINE_SNAPSHOT_MAX_VERSIONS:5}  # Less versions for dev
      compression-enabled: ${MATCHING_ENGINE_SNAPSHOT_COMPRESSION:false}  # Disabled for dev
      async-save: ${MATCHING_ENGINE_SNAPSHOT_ASYNC:false}  # Sync for dev debugging
      batch-size: ${MATCHING_ENGINE_SNAPSHOT_BATCH_SIZE:50}  # Smaller batch for dev
    # Development Cleanup Settings
    cleanup:
      enabled: ${MATCHING_ENGINE_CLEANUP_ENABLED:false}  # Disabled for dev
      retention-days: ${MATCHING_ENGINE_CLEANUP_RETENTION_DAYS:1}  # Short retention for dev
      cleanup-interval-hours: ${MATCHING_ENGINE_CLEANUP_INTERVAL_HOURS:6}  # Less frequent for dev
      max-snapshots-per-cleanup: ${MATCHING_ENGINE_CLEANUP_MAX_SNAPSHOTS:100}  # Smaller for dev
    # Development Collection Names
    collections:
      order-book-snapshots: ${MONGODB_COLLECTION_ORDER_BOOK_SNAPSHOTS:dev_order_book_snapshots}
      trade-history: ${MONGODB_COLLECTION_TRADE_HISTORY:dev_trade_history}
      performance-metrics: ${MONGODB_COLLECTION_PERFORMANCE_METRICS:dev_performance_metrics}
      system-events: ${MONGODB_COLLECTION_SYSTEM_EVENTS:dev_system_events}
    # Development Index Configuration
    indexes:
      auto-create: ${MONGODB_INDEXES_AUTO_CREATE:true}
      background: ${MONGODB_INDEXES_BACKGROUND:false}  # Foreground for dev
    # Development Performance Settings
    performance:
      bulk-write-size: ${MONGODB_BULK_WRITE_SIZE:100}  # Smaller for dev
      query-timeout-ms: ${MONGODB_QUERY_TIMEOUT:5000}  # Shorter for dev
      cursor-timeout-ms: ${MONGODB_CURSOR_TIMEOUT:15000}  # Shorter for dev

# Development-specific Topic Configuration
topic-kafka:
  exchange:
    order: dev-exchange-order
    order-cancel: dev-exchange-order-cancel
    order-cancel-success: dev-exchange-order-cancel-success
    order-completed: dev-exchange-order-completed
    trade: dev-exchange-trade
    trade-plate: dev-exchange-trade-plate
  
  contract:
    order-new: dev-contract-order-new
    order-cancel: dev-contract-order-cancel
    order-completed: dev-contract-order-completed
    trade: dev-contract-trade
    trade-plate: dev-contract-trade-plate
    position: dev-contract-position
    mark-price: dev-contract-mark-price
    index-price: dev-contract-index-price
    funding-rate: dev-contract-funding-rate
    liquidation: dev-contract-liquidation
    last-price: dev-contract-last-price
    order-events: dev-contract-order-events

# Consumer Groups for Development
consumer-groups:
  exchange:
    orders: matching-engine-dev-exchange-orders
    cancels: matching-engine-dev-exchange-cancels
    trades: matching-engine-dev-exchange-trades
  
  contract:
    orders: matching-engine-dev-contract-orders
    cancels: matching-engine-dev-contract-cancels
    trades: matching-engine-dev-contract-trades
    events: matching-engine-dev-order-events
    funding-rate: matching-engine-dev-funding-rate

# Development Logging Configuration
logging:
  level:
    com.icetea.lotus.matching: INFO  # Reduced verbosity
    com.icetea.lotus.matching.infrastructure.messaging: INFO
    com.icetea.lotus.matching.infrastructure.exchange: INFO
    com.icetea.lotus.matching.infrastructure.futurecore: INFO
    org.apache.kafka: WARN  # Less verbose for Kafka
    org.springframework.kafka: WARN
    org.hibernate.SQL: WARN  # Disable SQL logging for cleaner output
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
    root: INFO
  
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Development Feature Flags
features:
  experimental:
    advanced-matching-algorithms: false  # Disabled for dev stability
    predictive-caching: false
    machine-learning-optimization: false
    adaptive-batching: false  # Disabled for dev
    dynamic-segmentation: false

# Development Production Settings
production:
  mode: false  # Development mode
  aggressive-optimization: false  # Disabled for dev
  strict-monitoring: false  # Relaxed for dev
  auto-tuning: false

# Development Pod Configuration
pod:
  name: ${POD_NAME:dev-matching-engine-pod}
  namespace: ${POD_NAMESPACE:development}
  ip: ${POD_IP:127.0.0.1}

# Development Circuit Breaker - More relaxed
resilience4j:
  circuitbreaker:
    instances:
      matching-engine:
        registerHealthIndicator: true
        slidingWindowSize: 5  # Smaller window for dev
        minimumNumberOfCalls: 3  # Less calls needed
        permittedNumberOfCallsInHalfOpenState: 2
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 10s  # Longer wait for dev
        failureRateThreshold: 70  # More tolerant for dev
        eventConsumerBufferSize: 5

# Development Management
management:
  tracing:
    sampling:
      probability: 1.0
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: true
    tags:
      application: ${spring.application.name}

# Development Info
info:
  app:
    name: Matching Engine (Development)
    description: High-performance order matching engine - Development Environment
    version: 1.0.0-DEV
    environment: development
    architecture: Message-driven (Kafka)
    modules:
      - Exchange Module (Spot Trading)
      - Future-Core Module (Advanced Algorithms)
      - Development Tools
