# Test Profile Configuration - Disable external dependencies
spring:
  # Disable Redis auto-configuration
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
      - org.redisson.spring.starter.RedissonAutoConfigurationV2
      - org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration

  # Disable Kafka
  kafka:
    bootstrap-servers: 

# Disable matching engine components that require external services
matching-engine:
  exchange:
    enabled: false
  future-core:
    enabled: false
  sharding:
    enabled: false

# Logging for test
logging:
  level:
    com.icetea.lotus.matching: DEBUG
    org.springframework.boot.autoconfigure: WARN
    org.springframework.kafka: OFF
    org.redisson: OFF



