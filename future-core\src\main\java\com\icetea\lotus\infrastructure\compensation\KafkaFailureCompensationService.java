package com.icetea.lotus.infrastructure.compensation;

import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.infrastructure.messaging.producer.OrderCommandProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * Service để xử lý compensation cho Kafka failures
 * Retry failed Kafka sends để đảm bảo orders đ<PERSON><PERSON><PERSON> g<PERSON><PERSON> đến matching engine
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaFailureCompensationService {

    private final OrderCommandProducer orderCommandProducer;
    private final RedisTemplate<String, String> redisTemplate;
    
    private static final String FAILED_KAFKA_SEND_PREFIX = "failed_kafka_send:";
    private static final String RETRY_COUNT_PREFIX = "kafka_retry_count:";
    private static final Duration FAILED_SEND_TTL = Duration.ofHours(24);
    private static final int MAX_RETRY_ATTEMPTS = 5;

    /**
     * Schedule order để retry Kafka send
     * 
     * @param order Order cần retry
     */
    public void scheduleKafkaRetry(Order order) {
        if (order == null || order.getOrderId() == null) {
            log.warn("Invalid order for Kafka retry scheduling");
            return;
        }

        String orderId = order.getOrderId().getValue();
        
        try {
            // Store failed order info for retry
            String failedSendKey = FAILED_KAFKA_SEND_PREFIX + orderId;
            String orderJson = serializeOrder(order);
            
            redisTemplate.opsForValue().set(failedSendKey, orderJson, FAILED_SEND_TTL);
            
            // Initialize retry count
            String retryCountKey = RETRY_COUNT_PREFIX + orderId;
            redisTemplate.opsForValue().set(retryCountKey, "0", FAILED_SEND_TTL);
            
            log.info("Scheduled Kafka retry for order: {}", orderId);
            
        } catch (Exception e) {
            log.error("Failed to schedule Kafka retry for order: {}", orderId, e);
        }
    }

    /**
     * Retry failed Kafka sends - chạy mỗi 30 giây
     */
    @Scheduled(fixedDelay = 30000) // 30 seconds
    public void retryFailedKafkaSends() {
        try {
            Set<String> failedSendKeys = redisTemplate.keys(FAILED_KAFKA_SEND_PREFIX + "*");
            
            if (failedSendKeys == null || failedSendKeys.isEmpty()) {
                return;
            }
            
            log.debug("Found {} failed Kafka sends to retry", failedSendKeys.size());
            
            for (String failedSendKey : failedSendKeys) {
                String orderId = extractOrderIdFromKey(failedSendKey);
                retryKafkaSendForOrder(orderId, failedSendKey);
            }
            
        } catch (Exception e) {
            log.error("Error during Kafka retry processing", e);
        }
    }

    /**
     * Retry Kafka send cho một order cụ thể
     */
    private void retryKafkaSendForOrder(String orderId, String failedSendKey) {
        try {
            // Get retry count
            String retryCountKey = RETRY_COUNT_PREFIX + orderId;
            String retryCountStr = redisTemplate.opsForValue().get(retryCountKey);
            int retryCount = retryCountStr != null ? Integer.parseInt(retryCountStr) : 0;
            
            if (retryCount >= MAX_RETRY_ATTEMPTS) {
                log.warn("Max retry attempts reached for order: {}, giving up", orderId);
                cleanupFailedOrder(orderId, failedSendKey, retryCountKey);
                return;
            }
            
            // Get order data
            String orderJson = redisTemplate.opsForValue().get(failedSendKey);
            if (orderJson == null) {
                log.warn("Order data not found for retry: {}", orderId);
                cleanupFailedOrder(orderId, failedSendKey, retryCountKey);
                return;
            }
            
            Order order = deserializeOrder(orderJson);
            if (order == null) {
                log.error("Failed to deserialize order for retry: {}", orderId);
                cleanupFailedOrder(orderId, failedSendKey, retryCountKey);
                return;
            }
            
            // Attempt to send to Kafka
            boolean success = attemptKafkaSend(order);
            
            if (success) {
                log.info("Successfully retried Kafka send for order: {} after {} attempts", orderId, retryCount + 1);
                cleanupFailedOrder(orderId, failedSendKey, retryCountKey);
            } else {
                // Increment retry count
                int newRetryCount = retryCount + 1;
                redisTemplate.opsForValue().set(retryCountKey, String.valueOf(newRetryCount), FAILED_SEND_TTL);
                log.warn("Kafka retry failed for order: {}, attempt {}/{}", orderId, newRetryCount, MAX_RETRY_ATTEMPTS);
            }
            
        } catch (Exception e) {
            log.error("Error retrying Kafka send for order: {}", orderId, e);
        }
    }

    /**
     * Attempt to send order to Kafka
     */
    private boolean attemptKafkaSend(Order order) {
        try {
            orderCommandProducer.sendPlaceOrderCommand(order);
            return true;
        } catch (Exception e) {
            log.debug("Kafka send attempt failed for order: {}", order.getOrderId().getValue(), e);
            return false;
        }
    }

    /**
     * Cleanup failed order entries
     */
    private void cleanupFailedOrder(String orderId, String failedSendKey, String retryCountKey) {
        try {
            redisTemplate.delete(failedSendKey);
            redisTemplate.delete(retryCountKey);
            log.debug("Cleaned up failed order entries for: {}", orderId);
        } catch (Exception e) {
            log.warn("Failed to cleanup entries for order: {}", orderId, e);
        }
    }

    /**
     * Extract orderId from Redis key
     */
    private String extractOrderIdFromKey(String key) {
        return key.substring(FAILED_KAFKA_SEND_PREFIX.length());
    }

    /**
     * Serialize order to JSON string
     */
    private String serializeOrder(Order order) {
        // Simple serialization - in production, use proper JSON serialization
        return String.format("{\"orderId\":\"%s\",\"memberId\":%d,\"symbol\":\"%s\",\"createTime\":\"%s\"}", 
            order.getOrderId().getValue(),
            order.getMemberId(),
            order.getSymbol().getValue(),
            LocalDateTime.now().toString());
    }

    /**
     * Deserialize order from JSON string
     */
    private Order deserializeOrder(String orderJson) {
        try {
            // Simple deserialization - in production, use proper JSON deserialization
            // For now, return null to indicate deserialization failure
            // This should be implemented with proper JSON parsing
            log.warn("Order deserialization not implemented, skipping retry");
            return null;
        } catch (Exception e) {
            log.error("Failed to deserialize order: {}", orderJson, e);
            return null;
        }
    }

    /**
     * Get count of pending retries
     */
    public int getPendingRetryCount() {
        try {
            Set<String> failedSendKeys = redisTemplate.keys(FAILED_KAFKA_SEND_PREFIX + "*");
            return failedSendKeys != null ? failedSendKeys.size() : 0;
        } catch (Exception e) {
            log.error("Error getting pending retry count", e);
            return 0;
        }
    }
}
