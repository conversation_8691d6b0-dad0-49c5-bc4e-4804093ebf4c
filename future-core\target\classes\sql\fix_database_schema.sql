-- <PERSON><PERSON><PERSON> để sửa lỗi schema cơ sở dữ liệu

-- 1. Sửa lỗi bảng contract_trade
-- Thêm cột buy_order_type và sell_order_type vào bảng contract_trade
ALTER TABLE contract_trade ADD COLUMN IF NOT EXISTS buy_order_type VARCHAR(20);
ALTER TABLE contract_trade ADD COLUMN IF NOT EXISTS sell_order_type VARCHAR(20);

-- 2. Tạo bảng contract_index_price nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS contract_index_price (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT,
    symbol VARCHAR(50) NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    reference_prices TEXT,
    create_time TIMESTAMP NOT NULL
);

-- Tạo các chỉ mục cho bảng contract_index_price
CREATE INDEX IF NOT EXISTS idx_contract_index_price_symbol ON contract_index_price (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_index_price_create_time ON contract_index_price (create_time);

-- C<PERSON><PERSON> nhật dữ liệu mẫu cho bảng contract_trade
UPDATE contract_trade SET buy_order_type = 'LIMIT', sell_order_type = 'LIMIT' WHERE buy_order_type IS NULL;

-- 3. Sửa lỗi bảng contract_order
-- Thêm các cột thiếu vào bảng contract_order
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS activation_price DECIMAL(18,8);
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS post_only BOOLEAN DEFAULT FALSE;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS max_slippage DECIMAL(18,8) DEFAULT 0;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS fill_or_kill BOOLEAN DEFAULT FALSE;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS immediate_or_cancel BOOLEAN DEFAULT FALSE;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS cancel_reason VARCHAR(255);
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS complete_time TIMESTAMP;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS deal_volume DECIMAL(18,8) DEFAULT 0;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS deal_money DECIMAL(18,8) DEFAULT 0;

-- Đổi tên cột slippage thành max_slippage nếu cần
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'contract_order'
        AND column_name = 'slippage'
    ) THEN
        ALTER TABLE contract_order RENAME COLUMN slippage TO max_slippage;
    END IF;
END $$;

-- Cập nhật dữ liệu mẫu cho bảng contract_order
UPDATE contract_order SET post_only = FALSE, fill_or_kill = FALSE, immediate_or_cancel = FALSE WHERE post_only IS NULL;

-- 4. Kiểm tra và hiển thị thông tin về các cột đã thêm
SELECT
    table_name,
    column_name,
    data_type
FROM
    information_schema.columns
WHERE
    table_name IN ('contract_trade', 'contract_order', 'contract_index_price')
    AND (column_name IN ('buy_order_type', 'sell_order_type', 'activation_price', 'post_only', 'max_slippage', 'fill_or_kill', 'immediate_or_cancel', 'cancel_reason', 'complete_time', 'deal_volume', 'deal_money')
         OR table_name = 'contract_index_price')
ORDER BY
    table_name,
    column_name;
