-- Script để thêm unique constraints nhằm ngăn chặn duplicate order records
-- Author: edward nguyen
-- Date: 2025-07-13

-- 1. Thêm unique constraint để ngăn duplicate orders từ cùng member trong cùng thời điểm
-- Constraint này ngăn chặn duplicate orders được tạo trong cùng một millisecond
ALTER TABLE contract_order 
ADD CONSTRAINT uk_contract_order_member_symbol_time 
UNIQUE (member_id, symbol, create_time);

-- 2. Thêm composite unique constraint cho business logic duplicate prevention
-- Constraint này ngăn chặn duplicate orders với cùng thông tin business
ALTER TABLE contract_order 
ADD CONSTRAINT uk_contract_order_business_key 
UNIQUE (member_id, symbol, type, price, volume, direction, create_time);

-- 3. Thêm index để cải thiện performance cho duplicate checks
CREATE INDEX IF NOT EXISTS idx_contract_order_duplicate_check 
ON contract_order (member_id, symbol, create_time);

CREATE INDEX IF NOT EXISTS idx_contract_order_business_duplicate 
ON contract_order (member_id, symbol, type, direction, create_time);

-- 4. Thêm partial unique constraint cho MARKET orders (không có price)
-- Market orders chỉ cần check member_id, symbol, type, volume, direction, create_time
CREATE UNIQUE INDEX IF NOT EXISTS uk_contract_order_market_orders
ON contract_order (member_id, symbol, type, volume, direction, create_time)
WHERE type IN ('MARKET', 'STOP_MARKET', 'TAKE_PROFIT_MARKET');

-- 5. Thêm constraint cho LIMIT orders (có price)
CREATE UNIQUE INDEX IF NOT EXISTS uk_contract_order_limit_orders
ON contract_order (member_id, symbol, type, price, volume, direction, create_time)
WHERE type IN ('LIMIT', 'STOP_LIMIT', 'TAKE_PROFIT_LIMIT');

-- 6. Comments để giải thích các constraints
COMMENT ON CONSTRAINT uk_contract_order_member_symbol_time ON contract_order 
IS 'Ngăn chặn duplicate orders từ cùng member cho cùng symbol trong cùng thời điểm';

COMMENT ON CONSTRAINT uk_contract_order_business_key ON contract_order 
IS 'Ngăn chặn duplicate orders với cùng thông tin business logic';

COMMENT ON INDEX idx_contract_order_duplicate_check 
IS 'Index để cải thiện performance cho duplicate detection queries';

COMMENT ON INDEX uk_contract_order_market_orders 
IS 'Unique constraint cho MARKET orders (không có price)';

COMMENT ON INDEX uk_contract_order_limit_orders 
IS 'Unique constraint cho LIMIT orders (có price)';

-- 7. Tạo function để check duplicate trước khi insert (optional)
CREATE OR REPLACE FUNCTION check_duplicate_order()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if similar order exists within last 1 second
    IF EXISTS (
        SELECT 1 FROM contract_order 
        WHERE member_id = NEW.member_id 
        AND symbol = NEW.symbol 
        AND type = NEW.type
        AND direction = NEW.direction
        AND ABS(EXTRACT(EPOCH FROM (NEW.create_time - create_time))) < 1
        AND order_id != NEW.order_id
    ) THEN
        RAISE EXCEPTION 'Duplicate order detected for member % symbol % within 1 second', NEW.member_id, NEW.symbol;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 8. Tạo trigger để check duplicate (optional - có thể enable nếu cần)
-- CREATE TRIGGER trigger_check_duplicate_order
--     BEFORE INSERT ON contract_order
--     FOR EACH ROW
--     EXECUTE FUNCTION check_duplicate_order();

-- 9. Tạo view để monitor duplicate attempts
CREATE OR REPLACE VIEW v_potential_duplicates AS
SELECT 
    member_id,
    symbol,
    type,
    direction,
    price,
    volume,
    COUNT(*) as duplicate_count,
    MIN(create_time) as first_attempt,
    MAX(create_time) as last_attempt,
    EXTRACT(EPOCH FROM (MAX(create_time) - MIN(create_time))) as time_diff_seconds
FROM contract_order
GROUP BY member_id, symbol, type, direction, price, volume
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, last_attempt DESC;

COMMENT ON VIEW v_potential_duplicates 
IS 'View để monitor các potential duplicate orders trong system';

-- 10. Tạo function để cleanup duplicate orders (nếu đã tồn tại)
CREATE OR REPLACE FUNCTION cleanup_duplicate_orders()
RETURNS TABLE(
    cleaned_orders INTEGER,
    kept_orders INTEGER
) AS $$
DECLARE
    duplicate_record RECORD;
    orders_to_delete TEXT[];
    cleaned_count INTEGER := 0;
    kept_count INTEGER := 0;
BEGIN
    -- Find duplicates based on business key
    FOR duplicate_record IN
        SELECT member_id, symbol, type, direction, price, volume, create_time
        FROM contract_order
        GROUP BY member_id, symbol, type, direction, price, volume, create_time
        HAVING COUNT(*) > 1
    LOOP
        -- Keep the first order, delete the rest
        SELECT ARRAY_AGG(order_id ORDER BY create_time DESC OFFSET 1)
        INTO orders_to_delete
        FROM contract_order
        WHERE member_id = duplicate_record.member_id
        AND symbol = duplicate_record.symbol
        AND type = duplicate_record.type
        AND direction = duplicate_record.direction
        AND (price = duplicate_record.price OR (price IS NULL AND duplicate_record.price IS NULL))
        AND volume = duplicate_record.volume
        AND create_time = duplicate_record.create_time;
        
        IF orders_to_delete IS NOT NULL THEN
            DELETE FROM contract_order WHERE order_id = ANY(orders_to_delete);
            cleaned_count := cleaned_count + array_length(orders_to_delete, 1);
            kept_count := kept_count + 1;
        END IF;
    END LOOP;
    
    RETURN QUERY SELECT cleaned_count, kept_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION cleanup_duplicate_orders() 
IS 'Function để cleanup duplicate orders đã tồn tại trong database';

-- 11. Tạo monitoring query để check constraint violations
-- Query này có thể được sử dụng để monitor duplicate attempts
/*
SELECT 
    constraint_name,
    COUNT(*) as violation_count,
    MAX(created_at) as last_violation
FROM information_schema.constraint_violations 
WHERE table_name = 'contract_order'
GROUP BY constraint_name
ORDER BY violation_count DESC;
*/

-- 12. Performance optimization: Tạo partial indexes cho active orders
CREATE INDEX IF NOT EXISTS idx_contract_order_active_orders
ON contract_order (member_id, symbol, status, create_time)
WHERE status IN ('NEW', 'PARTIALLY_FILLED', 'PENDING');

COMMENT ON INDEX idx_contract_order_active_orders 
IS 'Index để optimize queries cho active orders';

-- 13. Tạo index cho order lookup by orderId (nếu chưa có)
CREATE INDEX IF NOT EXISTS idx_contract_order_order_id
ON contract_order (order_id);

-- 14. Tạo compound index cho common query patterns
CREATE INDEX IF NOT EXISTS idx_contract_order_member_status_time
ON contract_order (member_id, status, create_time DESC);

-- 15. Final verification query
-- Chạy query này để verify constraints đã được tạo thành công
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'contract_order'::regclass
AND conname LIKE 'uk_contract_order%'
ORDER BY conname;
